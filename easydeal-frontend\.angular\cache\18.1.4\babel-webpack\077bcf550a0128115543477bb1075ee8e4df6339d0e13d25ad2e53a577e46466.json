{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../../_metronic/shared/keenicon/keenicon.component\";\nexport class EmptyPropertiesCardComponent {\n  userRole;\n  customMessage = '';\n  onFileUpload;\n  onDownloadTemplate;\n  get displayMessage() {\n    if (this.customMessage) {\n      return this.customMessage;\n    }\n    switch (this.userRole) {\n      case 'broker':\n        return \"Create a new property or upload the property units you're working on.\";\n      case 'developer':\n        return 'Create a new project or start building your development portfolio.';\n      default:\n        return \"Create a new property or upload the property units you're working on.\";\n    }\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (file && this.onFileUpload) {\n      this.onFileUpload(file);\n    }\n  }\n  downloadTemplate() {\n    if (this.onDownloadTemplate) {\n      this.onDownloadTemplate();\n    }\n  }\n  static ɵfac = function EmptyPropertiesCardComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || EmptyPropertiesCardComponent)();\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: EmptyPropertiesCardComponent,\n    selectors: [[\"app-empty-properties-card\"]],\n    inputs: {\n      userRole: \"userRole\",\n      customMessage: \"customMessage\",\n      onFileUpload: \"onFileUpload\",\n      onDownloadTemplate: \"onDownloadTemplate\"\n    },\n    decls: 20,\n    vars: 1,\n    consts: [[\"fileInput\", \"\"], [1, \"card-body\", \"d-flex\", \"flex-column\", \"justify-content-between\", \"mt-9\", \"bgi-no-repeat\", \"bgi-size-cover\", \"bgi-position-x-center\", \"pb-0\"], [\"src\", \"../../../../assets/media/broker/empty-data-and-properties.png\", \"alt\", \"\", 1, \"mx-auto\", \"h-150px\", \"h-lg-200px\", \"theme-light-show\"], [\"src\", \"../../../../assets/media/broker/empty-data-and-properties.png\", \"alt\", \"\", 1, \"mx-auto\", \"h-150px\", \"h-lg-200px\", \"theme-dark-show\"], [1, \"mb-10\", \"mt-10\"], [1, \"fs-2hx\", \"fw-bold\", \"text-dark-blue\", \"text-center\", \"mb-1\"], [1, \"me-2\"], [1, \"fs-4\", \"fw-bold\", \"text-gray-500\", \"text-center\", \"mb-5\"], [1, \"text-center\", \"mb-2\"], [\"type\", \"file\", \"accept\", \".xlsx,.xls\", \"hidden\", \"\", 3, \"change\"], [\"type\", \"button\", 1, \"btn\", \"btn-md\", \"btn-dark-blue\", \"fw-bold\", 3, \"click\"], [\"src\", \"../../../../assets/media/broker/excellogo.png\", \"alt\", \"\", 1, \"mx-auto\", \"h-30px\"], [1, \"text-center\"], [\"type\", \"button\", 1, \"fw-bold\", \"text-success\", \"bg-transparent\", \"border-0\", \"cursor-pointer\", 3, \"click\"], [\"name\", \"folder-down\", \"type\", \"outline\", 1, \"text-success\", \"fs-3\"]],\n    template: function EmptyPropertiesCardComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1);\n        i0.ɵɵelement(1, \"img\", 2)(2, \"img\", 3);\n        i0.ɵɵelementStart(3, \"div\", 4)(4, \"div\", 5)(5, \"span\", 6);\n        i0.ɵɵtext(6, \"There is nothing currently!\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"div\", 7)(8, \"span\");\n        i0.ɵɵtext(9);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 8)(11, \"input\", 9, 0);\n        i0.ɵɵlistener(\"change\", function EmptyPropertiesCardComponent_Template_input_change_11_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onFileSelected($event));\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"button\", 10);\n        i0.ɵɵlistener(\"click\", function EmptyPropertiesCardComponent_Template_button_click_13_listener() {\n          i0.ɵɵrestoreView(_r1);\n          const fileInput_r2 = i0.ɵɵreference(12);\n          return i0.ɵɵresetView(fileInput_r2.click());\n        });\n        i0.ɵɵelement(14, \"img\", 11);\n        i0.ɵɵtext(15, \" Upload Property Units File \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"div\", 12)(17, \"button\", 13);\n        i0.ɵɵlistener(\"click\", function EmptyPropertiesCardComponent_Template_button_click_17_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.downloadTemplate());\n        });\n        i0.ɵɵtext(18, \" Download Ads-template.xls \");\n        i0.ɵɵelement(19, \"app-keenicon\", 14);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(9);\n        i0.ɵɵtextInterpolate(ctx.displayMessage);\n      }\n    },\n    dependencies: [i1.KeeniconComponent],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["EmptyPropertiesCardComponent", "userRole", "customMessage", "onFileUpload", "onDownloadTemplate", "displayMessage", "onFileSelected", "event", "file", "target", "files", "downloadTemplate", "selectors", "inputs", "decls", "vars", "consts", "template", "EmptyPropertiesCardComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "EmptyPropertiesCardComponent_Template_input_change_11_listener", "$event", "ɵɵrestoreView", "_r1", "ɵɵresetView", "EmptyPropertiesCardComponent_Template_button_click_13_listener", "fileInput_r2", "ɵɵreference", "click", "EmptyPropertiesCardComponent_Template_button_click_17_listener", "ɵɵadvance", "ɵɵtextInterpolate"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\dataandproperties\\components\\empty-properties-card\\empty-properties-card.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\dataandproperties\\components\\empty-properties-card\\empty-properties-card.component.html"], "sourcesContent": ["import { Component, Input } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-empty-properties-card',\r\n  templateUrl: './empty-properties-card.component.html',\r\n  styleUrl: './empty-properties-card.component.scss',\r\n})\r\nexport class EmptyPropertiesCardComponent {\r\n  @Input() userRole: string ;\r\n  @Input() customMessage: string = '';\r\n  @Input() onFileUpload!: (file: File) => void;\r\n  @Input() onDownloadTemplate!: () => void;\r\n\r\n  get displayMessage(): string {\r\n    if (this.customMessage) {\r\n      return this.customMessage;\r\n    }\r\n\r\n    switch (this.userRole) {\r\n      case 'broker':\r\n        return \"Create a new property or upload the property units you're working on.\";\r\n      case 'developer':\r\n        return 'Create a new project or start building your development portfolio.';\r\n      default:\r\n        return \"Create a new property or upload the property units you're working on.\";\r\n    }\r\n  }\r\n\r\n  onFileSelected(event: any) {\r\n    const file = event.target.files[0];\r\n    if (file && this.onFileUpload) {\r\n      this.onFileUpload(file);\r\n    }\r\n  }\r\n\r\n  downloadTemplate() {\r\n    if (this.onDownloadTemplate) {\r\n      this.onDownloadTemplate();\r\n    }\r\n  }\r\n}\r\n", "<div\r\n  class=\"card-body d-flex flex-column justify-content-between mt-9 bgi-no-repeat bgi-size-cover bgi-position-x-center pb-0\">\r\n  <img class=\"mx-auto h-150px h-lg-200px theme-light-show\"\r\n    src=\"../../../../assets/media/broker/empty-data-and-properties.png\" alt=\"\" />\r\n  <img class=\"mx-auto h-150px h-lg-200px theme-dark-show\"\r\n    src=\"../../../../assets/media/broker/empty-data-and-properties.png\" alt=\"\" />\r\n  <div class=\"mb-10 mt-10\">\r\n    <div class=\"fs-2hx fw-bold text-dark-blue text-center mb-1\">\r\n      <span class=\"me-2\">There is nothing currently!</span>\r\n    </div>\r\n\r\n    <div class=\"fs-4 fw-bold text-gray-500 text-center mb-5\">\r\n      <span>{{ displayMessage }}</span>\r\n    </div>\r\n\r\n\r\n    <div class=\"text-center mb-2\">\r\n      <input type=\"file\" #fileInput (change)=\"onFileSelected($event)\" accept=\".xlsx,.xls\" hidden />\r\n      <button type=\"button\" class=\"btn btn-md btn-dark-blue fw-bold\" (click)=\"fileInput.click()\">\r\n        <img class=\"mx-auto h-30px\" src=\"../../../../assets/media/broker/excellogo.png\" alt=\"\" />\r\n        Upload Property Units File\r\n      </button>\r\n    </div>\r\n\r\n    <div class=\"text-center\">\r\n      <button type=\"button\" class=\"fw-bold text-success bg-transparent border-0 cursor-pointer\"\r\n        (click)=\"downloadTemplate()\">\r\n        Download Ads-template.xls\r\n        <app-keenicon name=\"folder-down\" class=\"text-success fs-3\" type=\"outline\"></app-keenicon>\r\n      </button>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;AAOA,OAAM,MAAOA,4BAA4B;EAC9BC,QAAQ;EACRC,aAAa,GAAW,EAAE;EAC1BC,YAAY;EACZC,kBAAkB;EAE3B,IAAIC,cAAcA,CAAA;IAChB,IAAI,IAAI,CAACH,aAAa,EAAE;MACtB,OAAO,IAAI,CAACA,aAAa;IAC3B;IAEA,QAAQ,IAAI,CAACD,QAAQ;MACnB,KAAK,QAAQ;QACX,OAAO,uEAAuE;MAChF,KAAK,WAAW;QACd,OAAO,oEAAoE;MAC7E;QACE,OAAO,uEAAuE;IAClF;EACF;EAEAK,cAAcA,CAACC,KAAU;IACvB,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,IAAI,IAAI,CAACL,YAAY,EAAE;MAC7B,IAAI,CAACA,YAAY,CAACK,IAAI,CAAC;IACzB;EACF;EAEAG,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACP,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,EAAE;IAC3B;EACF;;qCAhCWJ,4BAA4B;EAAA;;UAA5BA,4BAA4B;IAAAY,SAAA;IAAAC,MAAA;MAAAZ,QAAA;MAAAC,aAAA;MAAAC,YAAA;MAAAC,kBAAA;IAAA;IAAAU,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCPzCE,EAAA,CAAAC,cAAA,aAC4H;QAG1HD,EAFA,CAAAE,SAAA,aAC+E,aAEA;QAG3EF,EAFJ,CAAAC,cAAA,aAAyB,aACqC,cACvC;QAAAD,EAAA,CAAAG,MAAA,kCAA2B;QAChDH,EADgD,CAAAI,YAAA,EAAO,EACjD;QAGJJ,EADF,CAAAC,cAAA,aAAyD,WACjD;QAAAD,EAAA,CAAAG,MAAA,GAAoB;QAC5BH,EAD4B,CAAAI,YAAA,EAAO,EAC7B;QAIJJ,EADF,CAAAC,cAAA,cAA8B,mBACiE;QAA/DD,EAAA,CAAAK,UAAA,oBAAAC,+DAAAC,MAAA;UAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;UAAA,OAAAT,EAAA,CAAAU,WAAA,CAAUX,GAAA,CAAAd,cAAA,CAAAsB,MAAA,CAAsB;QAAA,EAAC;QAA/DP,EAAA,CAAAI,YAAA,EAA6F;QAC7FJ,EAAA,CAAAC,cAAA,kBAA2F;QAA5BD,EAAA,CAAAK,UAAA,mBAAAM,+DAAA;UAAAX,EAAA,CAAAQ,aAAA,CAAAC,GAAA;UAAA,MAAAG,YAAA,GAAAZ,EAAA,CAAAa,WAAA;UAAA,OAAAb,EAAA,CAAAU,WAAA,CAASE,YAAA,CAAAE,KAAA,EAAiB;QAAA,EAAC;QACxFd,EAAA,CAAAE,SAAA,eAAyF;QACzFF,EAAA,CAAAG,MAAA,oCACF;QACFH,EADE,CAAAI,YAAA,EAAS,EACL;QAGJJ,EADF,CAAAC,cAAA,eAAyB,kBAEQ;QAA7BD,EAAA,CAAAK,UAAA,mBAAAU,+DAAA;UAAAf,EAAA,CAAAQ,aAAA,CAAAC,GAAA;UAAA,OAAAT,EAAA,CAAAU,WAAA,CAASX,GAAA,CAAAT,gBAAA,EAAkB;QAAA,EAAC;QAC5BU,EAAA,CAAAG,MAAA,mCACA;QAAAH,EAAA,CAAAE,SAAA,wBAAyF;QAIjGF,EAHM,CAAAI,YAAA,EAAS,EACL,EACF,EACF;;;QApBMJ,EAAA,CAAAgB,SAAA,GAAoB;QAApBhB,EAAA,CAAAiB,iBAAA,CAAAlB,GAAA,CAAAf,cAAA,CAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}