{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ProjectsComponent } from './projects/projects.component';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from 'src/app/_metronic/shared/shared.module';\nimport { BrokerModule } from '../broker/broker.module';\nimport { ModelsComponent } from './projects/components/models/models.component';\nimport { UnitsComponent } from './projects/components/units/units.component';\nimport { DevelopersComponent } from './developers.component';\nimport { PaginationComponent } from '../../pagination/pagination.component';\nimport { DeveloperDashboardComponent } from './developer-dashboard/developer-dashboard.component';\nimport { DeveloperDashboardModule } from './developer-dashboard/developer-dashboard.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { AddPropertyComponent } from './add-property-dev/add-property.component';\nimport { UpdatePropertyComponent } from './update-property-dev/update-property.component';\nimport { ProjectsDropdownActionMenuComponent } from './projects/components/projects-dropdown-action-menu/projects-dropdown-action-menu.component';\n// import { DropdownActionMenuComponent } from './projects/components/dropdown-action-menu/dropdown-action-menu.component';\nimport { ContractRequestsComponent } from './projects/components/contract-requests/contract-requests.component';\nimport { ContractRequestsDropdownActionMenuComponent } from './projects/components/contract-requests/contract-requests-dropdown-action-menu/contract-requests-dropdown-action-menu.component';\nimport { UnitDetailsComponent } from './projects/components/unit-details/unit-details.component';\nimport { NgbModalModule } from '@ng-bootstrap/ng-bootstrap';\nimport { ModelUnitFilterComponent } from './projects/components/model-unit-filter/model-unit-filter.component';\nimport { ModelFilterComponent } from './projects/components/model-filter/model-filter.component';\nimport { ProjectFilterComponent } from './projects/components/project-filter/project-filter.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class DeveloperModule {\n  static ɵfac = function DeveloperModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DeveloperModule)();\n  };\n  static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: DeveloperModule\n  });\n  static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, BrokerModule, DeveloperDashboardModule, ReactiveFormsModule, FormsModule, NgbModalModule, RouterModule.forChild([{\n      path: '',\n      component: DevelopersComponent\n    }, {\n      path: 'dashboards',\n      component: DeveloperDashboardComponent\n    }, {\n      path: 'projects',\n      component: ProjectsComponent\n    },\n    // { path: 'projects/:developerId', component: ProjectsComponent },\n    {\n      path: 'projects/create',\n      component: AddPropertyComponent\n    }, {\n      path: 'projects/edit',\n      component: UpdatePropertyComponent\n    }, {\n      path: 'projects/models',\n      component: ModelsComponent\n    }, {\n      path: 'projects/models/units',\n      component: UnitsComponent\n    }, {\n      path: 'projects/models/units/details',\n      component: UnitDetailsComponent\n    }, {\n      path: 'projects/requests',\n      component: ContractRequestsComponent\n    }]), SharedModule, PaginationComponent]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DeveloperModule, {\n    declarations: [ProjectsComponent, ProjectsDropdownActionMenuComponent, ModelsComponent, UnitsComponent, UnitDetailsComponent, DevelopersComponent, ContractRequestsComponent, ContractRequestsDropdownActionMenuComponent, ModelUnitFilterComponent, ModelFilterComponent, ProjectFilterComponent],\n    imports: [CommonModule, BrokerModule, DeveloperDashboardModule, ReactiveFormsModule, FormsModule, NgbModalModule, i1.RouterModule, SharedModule, PaginationComponent],\n    exports: [\n    //DropdownActionMenuComponent,\n    ModelUnitFilterComponent, ModelFilterComponent, ProjectFilterComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ProjectsComponent", "RouterModule", "SharedModule", "BrokerModule", "ModelsComponent", "UnitsComponent", "DevelopersComponent", "PaginationComponent", "DeveloperDashboardComponent", "DeveloperDashboardModule", "FormsModule", "ReactiveFormsModule", "AddPropertyComponent", "UpdatePropertyComponent", "ProjectsDropdownActionMenuComponent", "ContractRequestsComponent", "ContractRequestsDropdownActionMenuComponent", "UnitDetailsComponent", "NgbModalModule", "ModelUnitFilterComponent", "ModelFilterComponent", "ProjectFilterComponent", "DeveloperModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "declarations", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\developer\\developer.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ProjectsComponent } from './projects/projects.component';\r\nimport { RouterModule } from '@angular/router';\r\nimport { SharedModule } from 'src/app/_metronic/shared/shared.module';\r\nimport { BrokerModule } from '../broker/broker.module';\r\nimport { ModelsComponent } from './projects/components/models/models.component';\r\nimport { UnitsComponent } from './projects/components/units/units.component';\r\nimport { DevelopersComponent } from './developers.component';\r\nimport { PaginationComponent } from '../../pagination/pagination.component';\r\nimport { DeveloperDashboardComponent } from './developer-dashboard/developer-dashboard.component';\r\nimport { DeveloperDashboardModule } from './developer-dashboard/developer-dashboard.module';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { AddPropertyComponent } from './add-property-dev/add-property.component';\r\nimport { UpdatePropertyComponent } from './update-property-dev/update-property.component';\r\nimport { ProjectsDropdownActionMenuComponent } from './projects/components/projects-dropdown-action-menu/projects-dropdown-action-menu.component';\r\n// import { DropdownActionMenuComponent } from './projects/components/dropdown-action-menu/dropdown-action-menu.component';\r\nimport { ContractRequestsComponent } from './projects/components/contract-requests/contract-requests.component';\r\nimport { ContractRequestsDropdownActionMenuComponent } from './projects/components/contract-requests/contract-requests-dropdown-action-menu/contract-requests-dropdown-action-menu.component';\r\nimport { UnitDetailsComponent } from './projects/components/unit-details/unit-details.component';\r\nimport { NgbModalModule } from '@ng-bootstrap/ng-bootstrap';\r\nimport { ModelUnitFilterComponent } from './projects/components/model-unit-filter/model-unit-filter.component';\r\nimport { ModelFilterComponent } from './projects/components/model-filter/model-filter.component';\r\nimport { ProjectFilterComponent } from './projects/components/project-filter/project-filter.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ProjectsComponent,\r\n    ProjectsDropdownActionMenuComponent,\r\n    ModelsComponent,\r\n    UnitsComponent,\r\n    UnitDetailsComponent,\r\n    DevelopersComponent,\r\n    ContractRequestsComponent,\r\n    ContractRequestsDropdownActionMenuComponent,\r\n    ModelUnitFilterComponent,\r\n    ModelFilterComponent,\r\n    ProjectFilterComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    BrokerModule,\r\n    DeveloperDashboardModule,\r\n    ReactiveFormsModule,\r\n    FormsModule,\r\n    NgbModalModule,\r\n    RouterModule.forChild([\r\n      { path: '', component: DevelopersComponent },\r\n      { path: 'dashboards', component: DeveloperDashboardComponent },\r\n      { path: 'projects', component: ProjectsComponent },\r\n      // { path: 'projects/:developerId', component: ProjectsComponent },\r\n      { path: 'projects/create', component: AddPropertyComponent },\r\n      { path: 'projects/edit', component: UpdatePropertyComponent },\r\n\r\n      { path: 'projects/models', component: ModelsComponent },\r\n      { path: 'projects/models/units', component: UnitsComponent },\r\n      {\r\n        path: 'projects/models/units/details',\r\n        component: UnitDetailsComponent,\r\n      },\r\n      { path: 'projects/requests', component: ContractRequestsComponent },\r\n    ]),\r\n    SharedModule,\r\n    PaginationComponent,\r\n  ],\r\n  exports: [\r\n    //DropdownActionMenuComponent,\r\n    ModelUnitFilterComponent,\r\n    ModelFilterComponent,\r\n    ProjectFilterComponent,\r\n  ],\r\n})\r\nexport class DeveloperModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,eAAe,QAAQ,+CAA+C;AAC/E,SAASC,cAAc,QAAQ,6CAA6C;AAC5E,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,mBAAmB,QAAQ,uCAAuC;AAC3E,SAASC,2BAA2B,QAAQ,qDAAqD;AACjG,SAASC,wBAAwB,QAAQ,kDAAkD;AAC3F,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,oBAAoB,QAAQ,2CAA2C;AAChF,SAASC,uBAAuB,QAAQ,iDAAiD;AACzF,SAASC,mCAAmC,QAAQ,6FAA6F;AACjJ;AACA,SAASC,yBAAyB,QAAQ,qEAAqE;AAC/G,SAASC,2CAA2C,QAAQ,iIAAiI;AAC7L,SAASC,oBAAoB,QAAQ,2DAA2D;AAChG,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,wBAAwB,QAAQ,qEAAqE;AAC9G,SAASC,oBAAoB,QAAQ,2DAA2D;AAChG,SAASC,sBAAsB,QAAQ,+DAA+D;;;AAiDtG,OAAM,MAAOC,eAAe;;qCAAfA,eAAe;EAAA;;UAAfA;EAAe;;cAhCxBvB,YAAY,EACZI,YAAY,EACZM,wBAAwB,EACxBE,mBAAmB,EACnBD,WAAW,EACXQ,cAAc,EACdjB,YAAY,CAACsB,QAAQ,CAAC,CACpB;MAAEC,IAAI,EAAE,EAAE;MAAEC,SAAS,EAAEnB;IAAmB,CAAE,EAC5C;MAAEkB,IAAI,EAAE,YAAY;MAAEC,SAAS,EAAEjB;IAA2B,CAAE,EAC9D;MAAEgB,IAAI,EAAE,UAAU;MAAEC,SAAS,EAAEzB;IAAiB,CAAE;IAClD;IACA;MAAEwB,IAAI,EAAE,iBAAiB;MAAEC,SAAS,EAAEb;IAAoB,CAAE,EAC5D;MAAEY,IAAI,EAAE,eAAe;MAAEC,SAAS,EAAEZ;IAAuB,CAAE,EAE7D;MAAEW,IAAI,EAAE,iBAAiB;MAAEC,SAAS,EAAErB;IAAe,CAAE,EACvD;MAAEoB,IAAI,EAAE,uBAAuB;MAAEC,SAAS,EAAEpB;IAAc,CAAE,EAC5D;MACEmB,IAAI,EAAE,+BAA+B;MACrCC,SAAS,EAAER;KACZ,EACD;MAAEO,IAAI,EAAE,mBAAmB;MAAEC,SAAS,EAAEV;IAAyB,CAAE,CACpE,CAAC,EACFb,YAAY,EACZK,mBAAmB;EAAA;;;2EASVe,eAAe;IAAAI,YAAA,GA7CxB1B,iBAAiB,EACjBc,mCAAmC,EACnCV,eAAe,EACfC,cAAc,EACdY,oBAAoB,EACpBX,mBAAmB,EACnBS,yBAAyB,EACzBC,2CAA2C,EAC3CG,wBAAwB,EACxBC,oBAAoB,EACpBC,sBAAsB;IAAAM,OAAA,GAGtB5B,YAAY,EACZI,YAAY,EACZM,wBAAwB,EACxBE,mBAAmB,EACnBD,WAAW,EACXQ,cAAc,EAAAU,EAAA,CAAA3B,YAAA,EAiBdC,YAAY,EACZK,mBAAmB;IAAAsB,OAAA;IAGnB;IACAV,wBAAwB,EACxBC,oBAAoB,EACpBC,sBAAsB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}