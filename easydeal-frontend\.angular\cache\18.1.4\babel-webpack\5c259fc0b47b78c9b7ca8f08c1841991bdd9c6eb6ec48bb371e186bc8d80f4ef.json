{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/taskes/New folder/easydeal-frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { BaseGridComponent } from '../../shared/base-grid/base-grid.component';\nimport Swal from 'sweetalert2';\nimport { MenuComponent } from 'src/app/_metronic/kt/components';\nlet ProjectsComponent = class ProjectsComponent extends BaseGridComponent {\n  cd;\n  projectsService;\n  route;\n  user;\n  showEmptyCard = false;\n  appliedFilters = {};\n  searchText = '';\n  searchTimeout;\n  isFilterDropdownVisible = false;\n  constructor(cd, projectsService, route) {\n    super(cd);\n    this.cd = cd;\n    this.projectsService = projectsService;\n    this.route = route;\n    this.setService(projectsService);\n    this.orderBy = 'id';\n    this.orderDir = 'desc';\n  }\n  ngOnInit() {\n    super.ngOnInit();\n    const userJson = localStorage.getItem('currentUser');\n    this.user = userJson ? JSON.parse(userJson) : null;\n    this.route.queryParams.subscribe(params => {\n      const queryDeveloperId = params['developerId'];\n      const developerId = queryDeveloperId ? parseInt(queryDeveloperId) : this.user.developerId;\n      this.page.filters = {\n        developerId: developerId\n      };\n      this.reloadTable(this.page);\n    });\n  }\n  updateCardVisibility() {\n    this.showEmptyCard = this.rows.length === 0;\n  }\n  onSearchTextChange(value) {\n    clearTimeout(this.searchTimeout); // Clear previous timeout\n    this.searchTimeout = setTimeout(() => {\n      this.page.filters = {\n        ...this.page.filters,\n        searchName: value.trim()\n      };\n      console.log(this.page.filters);\n      this.reloadTable(this.page);\n    }, 300);\n  }\n  toggleFilterDropdown() {\n    this.isFilterDropdownVisible = !this.isFilterDropdownVisible;\n  }\n  onFiltersApplied(filters) {\n    this.toggleFilterDropdown();\n    this.page.filters = {\n      ...this.page.filters,\n      ...filters\n    };\n    this.reloadTable(this.page);\n  }\n  reloadTable(pageInfo) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.page.pageNumber = pageInfo.pageNumber ?? pageInfo;\n      _this.loading = true;\n      yield _this._service.getAll(_this.page).subscribe(response => {\n        console.log(response.data);\n        _this.rows = Array.isArray(response.data) ? response.data : [];\n        _this.rows = [..._this.rows];\n        _this.page.totalElements = response.count;\n        _this.page.count = Math.ceil(response.count / _this.page.size);\n        _this.cd.markForCheck();\n        _this.loading = false;\n        _this.updateCardVisibility();\n        _this.afterGridLoaded();\n        setTimeout(() => {\n          MenuComponent.reinitialization();\n        }, 150);\n      }, error => {\n        console.log(error);\n        _this.cd.markForCheck();\n        _this.loading = false;\n        Swal.fire('Failed to load data. please try again later.', '', 'error');\n      });\n    })();\n  }\n};\nProjectsComponent = __decorate([Component({\n  selector: 'app-projects',\n  templateUrl: './projects.component.html',\n  styleUrl: './projects.component.scss'\n})], ProjectsComponent);\nexport { ProjectsComponent };", "map": {"version": 3, "names": ["Component", "BaseGridComponent", "<PERSON><PERSON>", "MenuComponent", "ProjectsComponent", "cd", "projectsService", "route", "user", "showEmptyCard", "appliedFilters", "searchText", "searchTimeout", "isFilterDropdownVisible", "constructor", "setService", "orderBy", "orderDir", "ngOnInit", "userJson", "localStorage", "getItem", "JSON", "parse", "queryParams", "subscribe", "params", "queryDeveloperId", "developerId", "parseInt", "page", "filters", "reloadTable", "updateCardVisibility", "rows", "length", "onSearchTextChange", "value", "clearTimeout", "setTimeout", "searchName", "trim", "console", "log", "toggleFilterDropdown", "onFiltersApplied", "pageInfo", "_this", "_asyncToGenerator", "pageNumber", "loading", "_service", "getAll", "response", "data", "Array", "isArray", "totalElements", "count", "Math", "ceil", "size", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "afterGridLoaded", "reinitialization", "error", "fire", "__decorate", "selector", "templateUrl", "styleUrl"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\developer\\projects\\projects.component.ts"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { BaseGridComponent } from '../../shared/base-grid/base-grid.component';\r\nimport { ProjectsService } from '../services/projects.service';\r\nimport Swal from 'sweetalert2';\r\nimport { MenuComponent } from 'src/app/_metronic/kt/components';\r\n\r\n@Component({\r\n  selector: 'app-projects',\r\n  templateUrl: './projects.component.html',\r\n  styleUrl: './projects.component.scss',\r\n})\r\nexport class ProjectsComponent extends BaseGridComponent implements OnInit {\r\n  user: any;\r\n\r\n  showEmptyCard = false;\r\n  appliedFilters: any = {};\r\n  searchText: string = '';\r\n  private searchTimeout: any;\r\n  isFilterDropdownVisible = false;\r\n\r\n  constructor(\r\n    protected cd: ChangeDetectorRef,\r\n    private projectsService: ProjectsService,\r\n    private route: ActivatedRoute\r\n  ) {\r\n    super(cd);\r\n    this.setService(projectsService);\r\n    this.orderBy = 'id';\r\n    this.orderDir = 'desc';\r\n  }\r\n\r\n  ngOnInit() {\r\n    super.ngOnInit();\r\n    const userJson = localStorage.getItem('currentUser');\r\n    this.user = userJson ? JSON.parse(userJson) : null;\r\n\r\n     this.route.queryParams.subscribe(params => {\r\n      const queryDeveloperId = params['developerId'];\r\n      const developerId = queryDeveloperId ? parseInt(queryDeveloperId) : this.user.developerId;\r\n\r\n      this.page.filters = {developerId : developerId};\r\n      this.reloadTable(this.page);\r\n    });\r\n  }\r\n\r\n  updateCardVisibility() {\r\n    this.showEmptyCard = this.rows.length === 0;\r\n  }\r\n\r\n  onSearchTextChange(value: string): void {\r\n    clearTimeout(this.searchTimeout); // Clear previous timeout\r\n    this.searchTimeout = setTimeout(() => {\r\n      this.page.filters = {...this.page.filters, searchName: value.trim()};\r\n      console.log(this.page.filters);\r\n      this.reloadTable(this.page);\r\n    }, 300);\r\n  }\r\n\r\n  toggleFilterDropdown() {\r\n    this.isFilterDropdownVisible = !this.isFilterDropdownVisible;\r\n  }\r\n\r\n  onFiltersApplied(filters: any) {\r\n    this.toggleFilterDropdown();\r\n    this.page.filters = {...this.page.filters, ...filters};\r\n\r\n    this.reloadTable(this.page);\r\n  }\r\n\r\n  async reloadTable(pageInfo: any) {\r\n    this.page.pageNumber = pageInfo.pageNumber ?? pageInfo;\r\n\r\n    this.loading = true;\r\n    await this._service.getAll(this.page).subscribe(\r\n      (response: any) => {\r\n        console.log(response.data);\r\n        this.rows = Array.isArray(response.data) ? response.data : [];\r\n        this.rows = [...this.rows];\r\n\r\n        this.page.totalElements = response.count;\r\n        this.page.count = Math.ceil(response.count / this.page.size);\r\n\r\n        this.cd.markForCheck();\r\n        this.loading = false;\r\n\r\n        this.updateCardVisibility();\r\n\r\n        this.afterGridLoaded();\r\n\r\n        setTimeout(() => {\r\n          MenuComponent.reinitialization();\r\n        }, 150);\r\n      },\r\n      (error: any) => {\r\n        console.log(error);\r\n        this.cd.markForCheck();\r\n        this.loading = false;\r\n        Swal.fire('Failed to load data. please try again later.', '', 'error');\r\n      }\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": ";;AAAA,SAA4BA,SAAS,QAAgB,eAAe;AAEpE,SAASC,iBAAiB,QAAQ,4CAA4C;AAE9E,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,aAAa,QAAQ,iCAAiC;AAOxD,IAAMC,iBAAiB,GAAvB,MAAMA,iBAAkB,SAAQH,iBAAiB;EAU1CI,EAAA;EACFC,eAAA;EACAC,KAAA;EAXVC,IAAI;EAEJC,aAAa,GAAG,KAAK;EACrBC,cAAc,GAAQ,EAAE;EACxBC,UAAU,GAAW,EAAE;EACfC,aAAa;EACrBC,uBAAuB,GAAG,KAAK;EAE/BC,YACYT,EAAqB,EACvBC,eAAgC,EAChCC,KAAqB;IAE7B,KAAK,CAACF,EAAE,CAAC;IAJC,KAAAA,EAAE,GAAFA,EAAE;IACJ,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IAGb,IAAI,CAACQ,UAAU,CAACT,eAAe,CAAC;IAChC,IAAI,CAACU,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,QAAQ,GAAG,MAAM;EACxB;EAEAC,QAAQA,CAAA;IACN,KAAK,CAACA,QAAQ,EAAE;IAChB,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACpD,IAAI,CAACb,IAAI,GAAGW,QAAQ,GAAGG,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC,GAAG,IAAI;IAEjD,IAAI,CAACZ,KAAK,CAACiB,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACzC,MAAMC,gBAAgB,GAAGD,MAAM,CAAC,aAAa,CAAC;MAC9C,MAAME,WAAW,GAAGD,gBAAgB,GAAGE,QAAQ,CAACF,gBAAgB,CAAC,GAAG,IAAI,CAACnB,IAAI,CAACoB,WAAW;MAEzF,IAAI,CAACE,IAAI,CAACC,OAAO,GAAG;QAACH,WAAW,EAAGA;MAAW,CAAC;MAC/C,IAAI,CAACI,WAAW,CAAC,IAAI,CAACF,IAAI,CAAC;IAC7B,CAAC,CAAC;EACJ;EAEAG,oBAAoBA,CAAA;IAClB,IAAI,CAACxB,aAAa,GAAG,IAAI,CAACyB,IAAI,CAACC,MAAM,KAAK,CAAC;EAC7C;EAEAC,kBAAkBA,CAACC,KAAa;IAC9BC,YAAY,CAAC,IAAI,CAAC1B,aAAa,CAAC,CAAC,CAAC;IAClC,IAAI,CAACA,aAAa,GAAG2B,UAAU,CAAC,MAAK;MACnC,IAAI,CAACT,IAAI,CAACC,OAAO,GAAG;QAAC,GAAG,IAAI,CAACD,IAAI,CAACC,OAAO;QAAES,UAAU,EAAEH,KAAK,CAACI,IAAI;MAAE,CAAC;MACpEC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACb,IAAI,CAACC,OAAO,CAAC;MAC9B,IAAI,CAACC,WAAW,CAAC,IAAI,CAACF,IAAI,CAAC;IAC7B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAc,oBAAoBA,CAAA;IAClB,IAAI,CAAC/B,uBAAuB,GAAG,CAAC,IAAI,CAACA,uBAAuB;EAC9D;EAEAgC,gBAAgBA,CAACd,OAAY;IAC3B,IAAI,CAACa,oBAAoB,EAAE;IAC3B,IAAI,CAACd,IAAI,CAACC,OAAO,GAAG;MAAC,GAAG,IAAI,CAACD,IAAI,CAACC,OAAO;MAAE,GAAGA;IAAO,CAAC;IAEtD,IAAI,CAACC,WAAW,CAAC,IAAI,CAACF,IAAI,CAAC;EAC7B;EAEME,WAAWA,CAACc,QAAa;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC7BD,KAAI,CAACjB,IAAI,CAACmB,UAAU,GAAGH,QAAQ,CAACG,UAAU,IAAIH,QAAQ;MAEtDC,KAAI,CAACG,OAAO,GAAG,IAAI;MACnB,MAAMH,KAAI,CAACI,QAAQ,CAACC,MAAM,CAACL,KAAI,CAACjB,IAAI,CAAC,CAACL,SAAS,CAC5C4B,QAAa,IAAI;QAChBX,OAAO,CAACC,GAAG,CAACU,QAAQ,CAACC,IAAI,CAAC;QAC1BP,KAAI,CAACb,IAAI,GAAGqB,KAAK,CAACC,OAAO,CAACH,QAAQ,CAACC,IAAI,CAAC,GAAGD,QAAQ,CAACC,IAAI,GAAG,EAAE;QAC7DP,KAAI,CAACb,IAAI,GAAG,CAAC,GAAGa,KAAI,CAACb,IAAI,CAAC;QAE1Ba,KAAI,CAACjB,IAAI,CAAC2B,aAAa,GAAGJ,QAAQ,CAACK,KAAK;QACxCX,KAAI,CAACjB,IAAI,CAAC4B,KAAK,GAAGC,IAAI,CAACC,IAAI,CAACP,QAAQ,CAACK,KAAK,GAAGX,KAAI,CAACjB,IAAI,CAAC+B,IAAI,CAAC;QAE5Dd,KAAI,CAAC1C,EAAE,CAACyD,YAAY,EAAE;QACtBf,KAAI,CAACG,OAAO,GAAG,KAAK;QAEpBH,KAAI,CAACd,oBAAoB,EAAE;QAE3Bc,KAAI,CAACgB,eAAe,EAAE;QAEtBxB,UAAU,CAAC,MAAK;UACdpC,aAAa,CAAC6D,gBAAgB,EAAE;QAClC,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,EACAC,KAAU,IAAI;QACbvB,OAAO,CAACC,GAAG,CAACsB,KAAK,CAAC;QAClBlB,KAAI,CAAC1C,EAAE,CAACyD,YAAY,EAAE;QACtBf,KAAI,CAACG,OAAO,GAAG,KAAK;QACpBhD,IAAI,CAACgE,IAAI,CAAC,8CAA8C,EAAE,EAAE,EAAE,OAAO,CAAC;MACxE,CAAC,CACF;IAAC;EACJ;CAED;AA3FY9D,iBAAiB,GAAA+D,UAAA,EAL7BnE,SAAS,CAAC;EACToE,QAAQ,EAAE,cAAc;EACxBC,WAAW,EAAE,2BAA2B;EACxCC,QAAQ,EAAE;CACX,CAAC,C,EACWlE,iBAAiB,CA2F7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}