import { ChangeDetectorRef, Component } from '@angular/core';
import { BaseGridComponent } from 'src/app/pages/shared/base-grid/base-grid.component';
import { ActivatedRoute } from '@angular/router';
import { Modal } from 'bootstrap';
import { UnitsService } from '../../../services/units.service';
import { MenuComponent } from 'src/app/_metronic/kt/components';
import Swal from 'sweetalert2';
import { event } from 'jquery';

@Component({
  selector: 'app-units',
  templateUrl: './units.component.html',
  styleUrl: './units.component.scss',
})
export class UnitsComponent extends BaseGridComponent {
  user: any;
  showEmptyCard = false;
  appliedFilters: any = {};
  searchText: string = '';
  private searchTimeout: any;
  isFilterDropdownVisible = false;
  selectedUnitIds: number[] = [];

  constructor(
    protected cd: ChangeDetectorRef,
    protected unitService: UnitsService,
    private activatedRoute: ActivatedRoute
  ) {
    super(cd);
    this.setService(unitService);
    this.orderBy = 'id';
    this.orderDir = 'desc';
  }

  ngOnInit() {
    super.ngOnInit();

    // Get user from localStorage
    const userJson = localStorage.getItem('currentUser');
    this.user = userJson ? JSON.parse(userJson) : null;

    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (params['modelCode']) {
        this.page.filters = { modelCode: params['modelCode'] };
      }
    });
  }

  onSearchTextChange(value: string): void {
    clearTimeout(this.searchTimeout);

    this.searchTimeout = setTimeout(() => {
      this.page.filters = {...this.page.filters, unitType: value.trim()};
      this.reloadTable(this.page);
    }, 300);
  }

  toggleFilterDropdown() {
    this.isFilterDropdownVisible = !this.isFilterDropdownVisible;
  }

  toggleAll(event: Event): void {
  const checkbox = event.target as HTMLInputElement;
  const checked = checkbox.checked;

  if (checked) {
    // Select all row IDs
    this.selectedUnitIds = this.rows.map((row: any) => row.id);
  } else {
    this.selectedUnitIds = [];
  }
}

// Check if all are selected
  isAllSelected(): boolean {
    return this.rows.length > 0 && this.selectedUnitIds.length === this.rows.length;
  }

  toggleUnitSelection(unitId: number, $event: any) {
    const checkbox = $event.target as HTMLInputElement;
    const checked = checkbox.checked;
    if (checked) {
      this.selectedUnitIds.push(unitId);
    } else {
      this.selectedUnitIds = this.selectedUnitIds.filter(id => id !== unitId);
    }
  }

  onFiltersApplied(filters: any) {
    this.toggleFilterDropdown();
    this.page.filters = {...this.page.filters, ...filters};

    this.reloadTable(this.page);
  }

  async reloadTable(pageInfo: any): Promise<void> {
    this.page.pageNumber = pageInfo.pageNumber ?? pageInfo;

    this.loading = true;
    await this._service.getAll(this.page).subscribe(
      (pagedData: any) => {
        console.log(pagedData.data);
        this.rows = Array.isArray(pagedData.data)? pagedData.data : [];
        this.rows = [...this.rows];

        this.page.totalElements = pagedData.count;
        this.page.count = Math.ceil(pagedData.count / this.page.size);

        // Update empty card visibility
        this.updateEmptyCardVisibility();

        this.cd.markForCheck();
        this.loading = false;

        this.afterGridLoaded();
        MenuComponent.reinitialization();
      },
      (error: any) => {
        console.log(error);
        this.cd.markForCheck();
        this.loading = false;
        Swal.fire('Failed to load data. please try again later.', '', 'error');
      }
    )
  }

  updateEmptyCardVisibility() {
    // Show empty card when there are no units
    this.showEmptyCard = this.rows.length === 0;
  }

  selectedUnitPlanImage: string;
  showUnitPlanModal(diagramUrl: string) {
    this.selectedUnitPlanImage = diagramUrl;
    const modalElement = document.getElementById('viewUnitPlanModal');
    if (modalElement) {
      const modal = new Modal(modalElement);
      modal.show();
    }
  }

  uploadModel() {
    console.log('Upload model clicked');
  }

  makeSelectedAvailable()
  {
    if (this.selectedUnitIds.length === 0) {
      Swal.fire('Please select at least one unit.', '', 'warning');
      return;
    }
    console.log(this.selectedUnitIds);
    this.unitService.makeSelectedAvailable(this.selectedUnitIds).subscribe(
      async (response:any)  => {
        this.cd.detectChanges();
        await Swal.fire('Units updated successfully!', '', 'success');
        this.selectedUnitIds = [];
        this.reloadTable(this.page);
      },
      (error: any ) => {
        Swal.fire('Error making units available.', '', 'error');
        console.error(error);
      }
    );
  }

}
