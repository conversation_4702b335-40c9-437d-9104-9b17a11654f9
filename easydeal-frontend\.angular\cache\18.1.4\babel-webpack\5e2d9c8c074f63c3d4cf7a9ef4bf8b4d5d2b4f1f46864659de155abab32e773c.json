{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { BaseGridComponent } from '../../shared/base-grid/base-grid.component';\nimport { PaginationComponent } from \"../../../pagination/pagination.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/property.service\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"@angular/platform-browser\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nfunction MyAddsComponent_div_7_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"i\", 27);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MyAddsComponent_div_7_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"i\", 29);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MyAddsComponent_div_7_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵlistener(\"click\", function MyAddsComponent_div_7_div_7_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(1, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function MyAddsComponent_div_7_div_7_Template_button_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const post_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.prevImage(post_r3, $event));\n    });\n    i0.ɵɵelement(2, \"i\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function MyAddsComponent_div_7_div_7_Template_button_click_3_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const post_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.nextImage(post_r3, $event));\n    });\n    i0.ɵɵelement(4, \"i\", 34);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MyAddsComponent_div_7_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵlistener(\"click\", function MyAddsComponent_div_7_div_8_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ctx_r3.getCurrentImageIndex(post_r3) + 1, \"/\", ctx_r3.getAllImagesFromGallery(post_r3).length, \" \");\n  }\n}\nfunction MyAddsComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10);\n    i0.ɵɵlistener(\"click\", function MyAddsComponent_div_7_Template_div_click_1_listener() {\n      const post_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.viewPropertyDetails(post_r3));\n    });\n    i0.ɵɵelementStart(2, \"div\", 11)(3, \"div\", 12);\n    i0.ɵɵlistener(\"click\", function MyAddsComponent_div_7_Template_div_click_3_listener($event) {\n      const post_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      const mediaModal_r5 = i0.ɵɵreference(11);\n      return i0.ɵɵresetView(ctx_r3.openMediaModal(mediaModal_r5, post_r3, $event));\n    });\n    i0.ɵɵelement(4, \"img\", 13);\n    i0.ɵɵtemplate(5, MyAddsComponent_div_7_div_5_Template, 2, 0, \"div\", 14)(6, MyAddsComponent_div_7_div_6_Template, 2, 0, \"div\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, MyAddsComponent_div_7_div_7_Template, 5, 0, \"div\", 16)(8, MyAddsComponent_div_7_div_8_Template, 2, 2, \"div\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 18)(10, \"h5\", 19);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 20)(13, \"div\", 21)(14, \"div\", 22);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 23);\n    i0.ɵɵtext(17, \"Area\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 21)(19, \"div\", 22);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 23);\n    i0.ɵɵtext(22, \"City\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"a\", 24);\n    i0.ɵɵlistener(\"click\", function MyAddsComponent_div_7_Template_a_click_23_listener() {\n      const post_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.viewPropertyDetails(post_r3));\n    });\n    i0.ɵɵelement(24, \"i\", 25);\n    i0.ɵɵtext(25, \"View \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const post_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"mediaContainer_\", post_r3.id, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"alt\", \"\", post_r3.type, \" image\");\n    i0.ɵɵproperty(\"src\", ctx_r3.getImageFromGallery(post_r3), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isCurrentItemVideo(post_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isCurrentItemVideo(post_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.hasMultipleImages(post_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.hasMultipleImages(post_r3));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", post_r3.type, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(post_r3.area.name_en);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(post_r3.city.name_en);\n  }\n}\nfunction MyAddsComponent_ng_template_10_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 46);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" (\", ctx_r3.currentModalIndex + 1, \"/\", ctx_r3.modalMediaItems.length, \") \");\n  }\n}\nfunction MyAddsComponent_ng_template_10_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"video\", 48);\n    i0.ɵɵelementStart(2, \"div\", 49)(3, \"a\", 50);\n    i0.ɵɵelement(4, \"i\", 51);\n    i0.ɵɵtext(5, \" Open in new tab \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r3.selectedMediaUrl, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"href\", ctx_r3.selectedMediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction MyAddsComponent_ng_template_10_img_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 52);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r3.selectedMediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction MyAddsComponent_ng_template_10_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function MyAddsComponent_ng_template_10_div_9_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.prevModalMedia());\n    });\n    i0.ɵɵelement(2, \"i\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function MyAddsComponent_ng_template_10_div_9_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.nextModalMedia());\n    });\n    i0.ɵɵelement(4, \"i\", 57);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MyAddsComponent_ng_template_10_div_10_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function MyAddsComponent_ng_template_10_div_10_button_2_Template_button_click_0_listener() {\n      const i_r12 = i0.ɵɵrestoreView(_r11).index;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      ctx_r3.currentModalIndex = i_r12;\n      return i0.ɵɵresetView(ctx_r3.updateModalMedia());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r12 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"btn-primary\", i_r12 === ctx_r3.currentModalIndex)(\"btn-light\", i_r12 !== ctx_r3.currentModalIndex);\n  }\n}\nfunction MyAddsComponent_ng_template_10_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 59);\n    i0.ɵɵtemplate(2, MyAddsComponent_ng_template_10_div_10_button_2_Template, 1, 4, \"button\", 60);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.modalMediaItems);\n  }\n}\nfunction MyAddsComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"h4\", 37);\n    i0.ɵɵtext(2);\n    i0.ɵɵtemplate(3, MyAddsComponent_ng_template_10_span_3_Template, 2, 2, \"span\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function MyAddsComponent_ng_template_10_Template_button_click_4_listener() {\n      const modal_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      return i0.ɵɵresetView(modal_r9.dismiss());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 40)(6, \"div\", 41);\n    i0.ɵɵtemplate(7, MyAddsComponent_ng_template_10_div_7_Template, 6, 2, \"div\", 42)(8, MyAddsComponent_ng_template_10_img_8_Template, 1, 1, \"img\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, MyAddsComponent_ng_template_10_div_9_Template, 5, 0, \"div\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, MyAddsComponent_ng_template_10_div_10_Template, 3, 1, \"div\", 45);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.selectedMediaType === \"video\" ? \"Video\" : \"Image\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.hasMultipleModalMedia());\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedMediaType === \"video\" && ctx_r3.selectedMediaUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedMediaType === \"image\" && ctx_r3.selectedMediaUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.hasMultipleModalMedia());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.hasMultipleModalMedia());\n  }\n}\nexport class MyAddsComponent extends BaseGridComponent {\n  cd;\n  propertyService;\n  modalService;\n  sanitizer;\n  router;\n  // Track current image index for each post\n  currentImageIndexes = {};\n  // Media modal properties\n  selectedMediaUrl = null;\n  selectedMediaType = 'image';\n  safeVideoUrl = null;\n  modalMediaItems = [];\n  currentModalIndex = 0;\n  constructor(cd, propertyService, modalService, sanitizer, router) {\n    super(cd);\n    this.cd = cd;\n    this.propertyService = propertyService;\n    this.modalService = modalService;\n    this.sanitizer = sanitizer;\n    this.router = router;\n    const userJson = localStorage.getItem('currentUser');\n    let user = userJson ? JSON.parse(userJson) : null;\n    this.setService(propertyService);\n    this.orderBy = 'id';\n    this.orderDir = 'desc';\n    this.page.filters = {\n      brokerId: user?.brokerId,\n      isAdvertisement: 1\n    };\n  }\n  // Open media modal\n  openMediaModal(content, post, event) {\n    // Stop event propagation to prevent card click\n    if (event) {\n      event.stopPropagation();\n    }\n    console.log('Opening modal for post:', post.id);\n    // Get all media items and set current index\n    this.modalMediaItems = this.getAllImagesFromGallery(post);\n    this.currentModalIndex = this.getCurrentImageIndex(post);\n    // Set current media item\n    this.updateModalMedia();\n    // Open the modal\n    this.modalService.open(content, {\n      centered: true,\n      size: 'lg',\n      windowClass: 'media-modal',\n      backdrop: 'static'\n    });\n    console.log('Modal opened with:', this.selectedMediaType, this.selectedMediaUrl);\n  }\n  // Get all media items from gallery\n  getAllImagesFromGallery(post) {\n    const allImages = [];\n    // Add gallery items if they exist\n    if (post.gallery && post.gallery.length > 0) {\n      const mediaItems = post.gallery.filter(item => item.url);\n      allImages.push(...mediaItems);\n    }\n    // Add diagram if it exists\n    if (post.diagram) {\n      console.log('Adding diagram to images:', post.diagram);\n      allImages.push({\n        url: post.diagram,\n        type: 'image'\n      });\n    }\n    // Add location in master plan if it exists\n    if (post.locationInMasterPlan) {\n      console.log('Adding locationInMasterPlan to images:', post.locationInMasterPlan);\n      allImages.push({\n        url: post.locationInMasterPlan,\n        type: 'image'\n      });\n    }\n    console.log('Total images for post', post.id, ':', allImages.length);\n    // Return default image if no images found\n    if (allImages.length === 0) {\n      return [{\n        url: '/assets/media/auth/404-error.png',\n        type: 'image'\n      }];\n    }\n    return allImages;\n  }\n  // Get current media URL for display\n  getImageFromGallery(post) {\n    const mediaItems = this.getAllImagesFromGallery(post);\n    const index = this.getCurrentImageIndex(post);\n    const currentItem = mediaItems[index];\n    return currentItem.url;\n  }\n  // Check if current item is a video\n  isCurrentItemVideo(post) {\n    const mediaItems = this.getAllImagesFromGallery(post);\n    const index = this.getCurrentImageIndex(post);\n    return mediaItems[index].type === 'video';\n  }\n  // Get video URL if current item is a video\n  getCurrentVideoUrl(post) {\n    const mediaItems = this.getAllImagesFromGallery(post);\n    const index = this.getCurrentImageIndex(post);\n    const currentItem = mediaItems[index];\n    return currentItem.type === 'video' ? currentItem.url : null;\n  }\n  // Check if post has multiple images\n  hasMultipleImages(post) {\n    return this.getAllImagesFromGallery(post).length > 1;\n  }\n  // Get current image index for a post\n  getCurrentImageIndex(post) {\n    if (!this.currentImageIndexes[post.id]) {\n      this.currentImageIndexes[post.id] = 0;\n    }\n    return this.currentImageIndexes[post.id];\n  }\n  // Navigate to next image\n  nextImage(post, event) {\n    event.stopPropagation();\n    const images = this.getAllImagesFromGallery(post);\n    this.currentImageIndexes[post.id] = (this.getCurrentImageIndex(post) + 1) % images.length;\n    this.cd.markForCheck();\n  }\n  // Navigate to previous image\n  prevImage(post, event) {\n    event.stopPropagation();\n    const images = this.getAllImagesFromGallery(post);\n    const currentIndex = this.getCurrentImageIndex(post);\n    this.currentImageIndexes[post.id] = currentIndex > 0 ? currentIndex - 1 : images.length - 1;\n    this.cd.markForCheck();\n  }\n  viewPropertyDetails(unitService) {\n    this.router.navigate(['/developer/projects/models/units/details'], {\n      queryParams: {\n        unitId: unitService.id\n      }\n    });\n  }\n  // Update modal media based on current index\n  updateModalMedia() {\n    if (this.modalMediaItems.length > 0 && this.currentModalIndex >= 0 && this.currentModalIndex < this.modalMediaItems.length) {\n      const currentItem = this.modalMediaItems[this.currentModalIndex];\n      this.selectedMediaUrl = currentItem.url;\n      this.selectedMediaType = currentItem.type;\n      // Create a safe URL for video content\n      if (this.selectedMediaType === 'video' && this.selectedMediaUrl) {\n        this.safeVideoUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.selectedMediaUrl);\n      } else {\n        this.safeVideoUrl = null;\n      }\n    }\n  }\n  // Navigate to next media in modal\n  nextModalMedia() {\n    if (this.modalMediaItems.length > 1) {\n      this.currentModalIndex = (this.currentModalIndex + 1) % this.modalMediaItems.length;\n      this.updateModalMedia();\n    }\n  }\n  // Navigate to previous media in modal\n  prevModalMedia() {\n    if (this.modalMediaItems.length > 1) {\n      this.currentModalIndex = this.currentModalIndex > 0 ? this.currentModalIndex - 1 : this.modalMediaItems.length - 1;\n      this.updateModalMedia();\n    }\n  }\n  // Check if modal has multiple media items\n  hasMultipleModalMedia() {\n    return this.modalMediaItems.length > 1;\n  }\n  static ɵfac = function MyAddsComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MyAddsComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PropertyService), i0.ɵɵdirectiveInject(i2.NgbModal), i0.ɵɵdirectiveInject(i3.DomSanitizer), i0.ɵɵdirectiveInject(i4.Router));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MyAddsComponent,\n    selectors: [[\"app-my-adds\"]],\n    standalone: true,\n    features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n    decls: 12,\n    vars: 5,\n    consts: [[\"mediaModal\", \"\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-between\", \"py-4\"], [1, \"fw-bold\", \"text-dark-blue\", \"mb-0\"], [1, \"fs-6\", \"text-dark-blue\", \"fw-semibold\", \"ms-0\"], [\"id\", \"kt_tab_pane_1\", 1, \"tab-pane\", \"fade\", \"show\", \"active\"], [1, \"row\", \"g-4\"], [\"class\", \"col-md-3 col-sm-6 col-12\", 4, \"ngFor\", \"ngForOf\"], [1, \"mt-5\"], [3, \"pageChange\", \"totalItems\", \"itemsPerPage\", \"currentPage\"], [1, \"col-md-3\", \"col-sm-6\", \"col-12\"], [\"role\", \"button\", \"tabindex\", \"0\", 1, \"card\", \"h-100\", \"border-0\", \"shadow-sm\", \"overflow-hidden\", \"transition-all\", \"duration-300\", \"hover:shadow-lg\", \"hover:-translate-y-1\", 3, \"click\"], [1, \"position-relative\"], [1, \"media-container\", \"cursor-pointer\", 3, \"click\", \"id\"], [1, \"img-fluid\", \"rounded-top\", 2, \"height\", \"180px\", \"object-fit\", \"cover\", \"width\", \"100%\", \"transition\", \"transform 0.3s ease\", 3, \"src\", \"alt\"], [\"class\", \"video-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-black bg-opacity-30\", 4, \"ngIf\"], [\"class\", \"gallery-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-black bg-opacity-20\", 4, \"ngIf\"], [\"class\", \"image-navigation position-absolute top-50 start-0 w-100 d-flex justify-content-between px-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"image-counter position-absolute bottom-0 end-0 bg-dark bg-opacity-75 text-white px-2 py-1 rounded-top-start fs-7\", 3, \"click\", 4, \"ngIf\"], [1, \"card-body\", \"p-4\"], [1, \"fs-5\", \"fw-bold\", \"text-dark\", \"mb-3\", \"text-truncate\", \"cursor-pointer\", \"text-hover-dark-blue\"], [1, \"d-flex\", \"flex-wrap\", \"gap-3\", \"mb-4\"], [1, \"border\", \"border-gray-200\", \"rounded\", \"p-3\", \"flex-grow-1\"], [1, \"fs-6\", \"fw-semibold\", \"text-dark\"], [1, \"fs-7\", \"text-muted\"], [\"role\", \"button\", \"aria-label\", \"View property details\", 1, \"btn\", \"btn-sm\", \"btn-light-dark-blue\", \"fw-semibold\", \"w-100\", 3, \"click\"], [1, \"fa\", \"fa-eye\", \"me-2\"], [1, \"video-overlay\", \"position-absolute\", \"top-0\", \"start-0\", \"w-100\", \"h-100\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"bg-black\", \"bg-opacity-30\"], [1, \"bi\", \"bi-play-circle-fill\", \"text-white\", \"fs-2\"], [1, \"gallery-overlay\", \"position-absolute\", \"top-0\", \"start-0\", \"w-100\", \"h-100\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"bg-black\", \"bg-opacity-20\"], [1, \"bi\", \"bi-images\", \"text-white\", \"fs-3\"], [1, \"image-navigation\", \"position-absolute\", \"top-50\", \"start-0\", \"w-100\", \"d-flex\", \"justify-content-between\", \"px-2\", 3, \"click\"], [\"aria-label\", \"Previous image\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"btn-white\", \"shadow-sm\", \"rounded-circle\", \"bg-light-dark-blue\", 3, \"click\"], [1, \"bi\", \"bi-chevron-left\", \"fs-6\"], [\"aria-label\", \"Next image\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"btn-white\", \"shadow-sm\", \"rounded-circle\", \"bg-light-dark-blue\", 3, \"click\"], [1, \"bi\", \"bi-chevron-right\", \"fs-6\"], [1, \"image-counter\", \"position-absolute\", \"bottom-0\", \"end-0\", \"bg-dark\", \"bg-opacity-75\", \"text-white\", \"px-2\", \"py-1\", \"rounded-top-start\", \"fs-7\", 3, \"click\"], [1, \"modal-header\"], [1, \"modal-title\"], [\"class\", \"text-muted ms-2 fs-6\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn-close\", 3, \"click\"], [1, \"modal-body\", \"position-relative\"], [1, \"text-center\"], [\"class\", \"video-container\", 4, \"ngIf\"], [\"class\", \"img-fluid\", \"style\", \"max-height: 70vh\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"modal-navigation\", 4, \"ngIf\"], [\"class\", \"modal-footer justify-content-center\", 4, \"ngIf\"], [1, \"text-muted\", \"ms-2\", \"fs-6\"], [1, \"video-container\"], [\"controls\", \"\", \"autoplay\", \"\", \"controlsList\", \"nodownload\", \"preload\", \"auto\", 1, \"w-100\", 2, \"max-height\", \"70vh\", 3, \"src\"], [1, \"mt-3\", \"text-muted\", \"small\"], [\"target\", \"_blank\", 3, \"href\"], [1, \"bi\", \"bi-box-arrow-up-right\", \"me-1\"], [1, \"img-fluid\", 2, \"max-height\", \"70vh\", 3, \"src\"], [1, \"modal-navigation\"], [1, \"btn\", \"btn-primary\", \"btn-icon\", \"position-absolute\", \"top-50\", \"start-0\", \"translate-middle-y\", \"ms-3\", 2, \"z-index\", \"1050\", 3, \"click\"], [1, \"bi\", \"bi-chevron-left\"], [1, \"btn\", \"btn-primary\", \"btn-icon\", \"position-absolute\", \"top-50\", \"end-0\", \"translate-middle-y\", \"me-3\", 2, \"z-index\", \"1050\", 3, \"click\"], [1, \"bi\", \"bi-chevron-right\"], [1, \"modal-footer\", \"justify-content-center\"], [1, \"d-flex\", \"gap-2\"], [\"class\", \"btn btn-sm rounded-circle p-1\", \"style\", \"width: 12px; height: 12px\", 3, \"btn-primary\", \"btn-light\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"btn\", \"btn-sm\", \"rounded-circle\", \"p-1\", 2, \"width\", \"12px\", \"height\", \"12px\", 3, \"click\"]],\n    template: function MyAddsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"h1\", 2);\n        i0.ɵɵtext(2, \" My Advertisements \");\n        i0.ɵɵelementStart(3, \"span\", 3);\n        i0.ɵɵtext(4);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5);\n        i0.ɵɵtemplate(7, MyAddsComponent_div_7_Template, 26, 12, \"div\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 7)(9, \"app-pagination\", 8);\n        i0.ɵɵlistener(\"pageChange\", function MyAddsComponent_Template_app_pagination_pageChange_9_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onPageChange($event));\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(10, MyAddsComponent_ng_template_10_Template, 11, 6, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\"(\", ctx.page.totalElements, \")\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.rows);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"totalItems\", ctx.page.totalElements)(\"itemsPerPage\", ctx.page.limit)(\"currentPage\", ctx.page.pageNumber);\n      }\n    },\n    dependencies: [CommonModule, i5.NgForOf, i5.NgIf, PaginationComponent],\n    styles: [\".image-navigation[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0 5px;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.position-relative[_ngcontent-%COMP%]:hover   .image-navigation[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.image-nav-btn[_ngcontent-%COMP%] {\\n  width: 30px;\\n  height: 30px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 10;\\n  opacity: 0.8;\\n  transition: opacity 0.3s, transform 0.3s;\\n}\\n.image-nav-btn[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n  transform: scale(1.1);\\n}\\n.image-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n\\n.prev-btn[_ngcontent-%COMP%] {\\n  margin-right: auto;\\n}\\n\\n.next-btn[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n}\\n\\n.image-counter[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 10px;\\n  right: 10px;\\n  background-color: rgba(0, 0, 0, 0.6);\\n  color: white;\\n  padding: 2px 8px;\\n  border-radius: 10px;\\n  font-size: 12px;\\n  z-index: 10;\\n}\\n\\n.video-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  background-color: rgba(0, 0, 0, 0.3);\\n  border-radius: 0.25rem;\\n}\\n.video-overlay[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 40px;\\n  color: white;\\n  opacity: 0.9;\\n}\\n\\n.gallery-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  right: 8px;\\n  background-color: rgba(253, 0, 0, 0.08);\\n  border-radius: 5%;\\n  width: 30px;\\n  height: 30px;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.gallery-overlay[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: white;\\n}\\n\\n.media-container[_ngcontent-%COMP%]:hover   .gallery-overlay[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.media-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  transition: transform 0.2s;\\n}\\n.media-container[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.02);\\n}\\n.media-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  transition: filter 0.2s;\\n}\\n.media-container[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  filter: brightness(1.1);\\n}\\n.media-container[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(45deg, transparent 0%, rgba(0, 123, 255, 0.1) 50%, transparent 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n  pointer-events: none;\\n  border-radius: 0.25rem;\\n}\\n.media-container[_ngcontent-%COMP%]:hover::after {\\n  opacity: 1;\\n}\\n\\n.card.cursor-pointer[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.card.cursor-pointer[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n  .media-modal .modal-dialog {\\n  max-width: 800px;\\n}\\n  .media-modal .modal-content {\\n  background-color: #fff;\\n}\\n  .media-modal .modal-header {\\n  border-bottom-color: #333;\\n}\\n  .media-modal .btn-close {\\n  filter: invert(1);\\n}\\n  .media-modal video {\\n  width: 100%;\\n  max-height: 70vh;\\n  outline: none;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["CommonModule", "BaseGridComponent", "PaginationComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "MyAddsComponent_div_7_div_7_Template_div_click_0_listener", "$event", "ɵɵrestoreView", "_r6", "ɵɵresetView", "stopPropagation", "MyAddsComponent_div_7_div_7_Template_button_click_1_listener", "post_r3", "ɵɵnextContext", "$implicit", "ctx_r3", "prevImage", "MyAddsComponent_div_7_div_7_Template_button_click_3_listener", "nextImage", "MyAddsComponent_div_7_div_8_Template_div_click_0_listener", "_r7", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate2", "getCurrentImageIndex", "getAllImagesFromGallery", "length", "MyAddsComponent_div_7_Template_div_click_1_listener", "_r2", "viewPropertyDetails", "MyAddsComponent_div_7_Template_div_click_3_listener", "mediaModal_r5", "ɵɵreference", "openMediaModal", "ɵɵtemplate", "MyAddsComponent_div_7_div_5_Template", "MyAddsComponent_div_7_div_6_Template", "MyAddsComponent_div_7_div_7_Template", "MyAddsComponent_div_7_div_8_Template", "MyAddsComponent_div_7_Template_a_click_23_listener", "ɵɵpropertyInterpolate1", "id", "type", "ɵɵproperty", "getImageFromGallery", "ɵɵsanitizeUrl", "isCurrentItemVideo", "hasMultipleImages", "ɵɵtextInterpolate1", "ɵɵtextInterpolate", "area", "name_en", "city", "currentModalIndex", "modalMediaItems", "selectedMediaUrl", "MyAddsComponent_ng_template_10_div_9_Template_button_click_1_listener", "_r10", "prevModalMedia", "MyAddsComponent_ng_template_10_div_9_Template_button_click_3_listener", "nextModalMedia", "MyAddsComponent_ng_template_10_div_10_button_2_Template_button_click_0_listener", "i_r12", "_r11", "index", "updateModalMedia", "ɵɵclassProp", "MyAddsComponent_ng_template_10_div_10_button_2_Template", "MyAddsComponent_ng_template_10_span_3_Template", "MyAddsComponent_ng_template_10_Template_button_click_4_listener", "modal_r9", "_r8", "dismiss", "MyAddsComponent_ng_template_10_div_7_Template", "MyAddsComponent_ng_template_10_img_8_Template", "MyAddsComponent_ng_template_10_div_9_Template", "MyAddsComponent_ng_template_10_div_10_Template", "selectedMediaType", "hasMultipleModalMedia", "MyAddsComponent", "cd", "propertyService", "modalService", "sanitizer", "router", "currentImageIndexes", "safeVideoUrl", "constructor", "userJson", "localStorage", "getItem", "user", "JSON", "parse", "setService", "orderBy", "orderDir", "page", "filters", "brokerId", "isAdvertisement", "content", "post", "event", "console", "log", "open", "centered", "size", "windowClass", "backdrop", "allImages", "gallery", "mediaItems", "filter", "item", "url", "push", "diagram", "locationInMasterPlan", "currentItem", "getCurrentVideoUrl", "images", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentIndex", "unitService", "navigate", "queryParams", "unitId", "bypassSecurityTrustResourceUrl", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "PropertyService", "i2", "NgbModal", "i3", "Dom<PERSON><PERSON><PERSON>zer", "i4", "Router", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "MyAddsComponent_Template", "rf", "ctx", "MyAddsComponent_div_7_Template", "MyAddsComponent_Template_app_pagination_pageChange_9_listener", "_r1", "onPageChange", "MyAddsComponent_ng_template_10_Template", "ɵɵtemplateRefExtractor", "totalElements", "rows", "limit", "pageNumber", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\my-adds\\my-adds.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\my-adds\\my-adds.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, HostBinding, Input } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { BaseGridComponent } from '../../shared/base-grid/base-grid.component';\r\nimport { PropertyService } from '../services/property.service';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';\r\nimport { Router } from '@angular/router';\r\nimport { PaginationComponent } from \"../../../pagination/pagination.component\";\r\n\r\n@Component({\r\n  selector: 'app-my-adds',\r\n  standalone: true,\r\n  imports: [CommonModule, PaginationComponent],\r\n  templateUrl: './my-adds.component.html',\r\n  styleUrl: './my-adds.component.scss',\r\n})\r\nexport class MyAddsComponent extends BaseGridComponent {\r\n  // Track current image index for each post\r\n  currentImageIndexes: { [postId: number]: number } = {};\r\n\r\n  // Media modal properties\r\n  selectedMediaUrl: string | null = null;\r\n  selectedMediaType: string = 'image';\r\n  safeVideoUrl: SafeResourceUrl | null = null;\r\n  modalMediaItems: any[] = [];\r\n  currentModalIndex: number = 0;\r\n\r\n  constructor(\r\n    protected cd: ChangeDetectorRef,\r\n    protected propertyService: PropertyService,\r\n    private modalService: NgbModal,\r\n    private sanitizer: DomSanitizer,\r\n    private router: Router\r\n  ) {\r\n    super(cd);\r\n    const userJson = localStorage.getItem('currentUser');\r\n    let user = userJson ? JSON.parse(userJson) : null;\r\n\r\n    this.setService(propertyService);\r\n    this.orderBy = 'id';\r\n    this.orderDir = 'desc';\r\n    this.page.filters = { brokerId: user?.brokerId, isAdvertisement: 1 };\r\n  }\r\n\r\n  // Open media modal\r\n  openMediaModal(content: any, post: any, event?: Event): void {\r\n    // Stop event propagation to prevent card click\r\n    if (event) {\r\n      event.stopPropagation();\r\n    }\r\n\r\n    console.log('Opening modal for post:', post.id);\r\n\r\n    // Get all media items and set current index\r\n    this.modalMediaItems = this.getAllImagesFromGallery(post);\r\n    this.currentModalIndex = this.getCurrentImageIndex(post);\r\n\r\n    // Set current media item\r\n    this.updateModalMedia();\r\n\r\n    // Open the modal\r\n    this.modalService.open(content, {\r\n      centered: true,\r\n      size: 'lg',\r\n      windowClass: 'media-modal',\r\n      backdrop: 'static',\r\n    });\r\n\r\n    console.log(\r\n      'Modal opened with:',\r\n      this.selectedMediaType,\r\n      this.selectedMediaUrl\r\n    );\r\n  }\r\n\r\n  // Get all media items from gallery\r\n  getAllImagesFromGallery(post: any): any[] {\r\n    const allImages: any[] = [];\r\n\r\n    // Add gallery items if they exist\r\n    if (post.gallery && post.gallery.length > 0) {\r\n      const mediaItems = post.gallery.filter((item: any) => item.url);\r\n      allImages.push(...mediaItems);\r\n    }\r\n\r\n    // Add diagram if it exists\r\n    if (post.diagram) {\r\n      console.log('Adding diagram to images:', post.diagram);\r\n      allImages.push({\r\n        url: post.diagram,\r\n        type: 'image'\r\n      });\r\n    }\r\n\r\n    // Add location in master plan if it exists\r\n    if (post.locationInMasterPlan) {\r\n      console.log('Adding locationInMasterPlan to images:', post.locationInMasterPlan);\r\n      allImages.push({\r\n        url: post.locationInMasterPlan,\r\n        type: 'image'\r\n      });\r\n    }\r\n\r\n    console.log('Total images for post', post.id, ':', allImages.length);\r\n\r\n    // Return default image if no images found\r\n    if (allImages.length === 0) {\r\n      return [{ url: '/assets/media/auth/404-error.png', type: 'image' }];\r\n    }\r\n\r\n    return allImages;\r\n  }\r\n\r\n  // Get current media URL for display\r\n  getImageFromGallery(post: any): string {\r\n    const mediaItems = this.getAllImagesFromGallery(post);\r\n    const index = this.getCurrentImageIndex(post);\r\n    const currentItem = mediaItems[index];\r\n\r\n    return currentItem.url;\r\n  }\r\n\r\n  // Check if current item is a video\r\n  isCurrentItemVideo(post: any): boolean {\r\n    const mediaItems = this.getAllImagesFromGallery(post);\r\n    const index = this.getCurrentImageIndex(post);\r\n    return mediaItems[index].type === 'video';\r\n  }\r\n\r\n  // Get video URL if current item is a video\r\n  getCurrentVideoUrl(post: any): string | null {\r\n    const mediaItems = this.getAllImagesFromGallery(post);\r\n    const index = this.getCurrentImageIndex(post);\r\n    const currentItem = mediaItems[index];\r\n\r\n    return currentItem.type === 'video' ? currentItem.url : null;\r\n  }\r\n\r\n  // Check if post has multiple images\r\n  hasMultipleImages(post: any): boolean {\r\n    return this.getAllImagesFromGallery(post).length > 1;\r\n  }\r\n\r\n  // Get current image index for a post\r\n  getCurrentImageIndex(post: any): number {\r\n    if (!this.currentImageIndexes[post.id]) {\r\n      this.currentImageIndexes[post.id] = 0;\r\n    }\r\n    return this.currentImageIndexes[post.id];\r\n  }\r\n\r\n  // Navigate to next image\r\n  nextImage(post: any, event: Event): void {\r\n    event.stopPropagation();\r\n    const images = this.getAllImagesFromGallery(post);\r\n    this.currentImageIndexes[post.id] =\r\n      (this.getCurrentImageIndex(post) + 1) % images.length;\r\n    this.cd.markForCheck();\r\n  }\r\n\r\n  // Navigate to previous image\r\n  prevImage(post: any, event: Event): void {\r\n    event.stopPropagation();\r\n    const images = this.getAllImagesFromGallery(post);\r\n    const currentIndex = this.getCurrentImageIndex(post);\r\n    this.currentImageIndexes[post.id] =\r\n      currentIndex > 0 ? currentIndex - 1 : images.length - 1;\r\n    this.cd.markForCheck();\r\n  }\r\n\r\n  viewPropertyDetails(unitService: any) {\r\n      this.router.navigate(['/developer/projects/models/units/details'], {\r\n        queryParams: { unitId: unitService.id }\r\n      });\r\n\r\n  }\r\n  // Update modal media based on current index\r\n  updateModalMedia(): void {\r\n    if (\r\n      this.modalMediaItems.length > 0 &&\r\n      this.currentModalIndex >= 0 &&\r\n      this.currentModalIndex < this.modalMediaItems.length\r\n    ) {\r\n      const currentItem = this.modalMediaItems[this.currentModalIndex];\r\n      this.selectedMediaUrl = currentItem.url;\r\n      this.selectedMediaType = currentItem.type;\r\n\r\n      // Create a safe URL for video content\r\n      if (this.selectedMediaType === 'video' && this.selectedMediaUrl) {\r\n        this.safeVideoUrl = this.sanitizer.bypassSecurityTrustResourceUrl(\r\n          this.selectedMediaUrl\r\n        );\r\n      } else {\r\n        this.safeVideoUrl = null;\r\n      }\r\n    }\r\n  }\r\n\r\n  // Navigate to next media in modal\r\n  nextModalMedia(): void {\r\n    if (this.modalMediaItems.length > 1) {\r\n      this.currentModalIndex =\r\n        (this.currentModalIndex + 1) % this.modalMediaItems.length;\r\n      this.updateModalMedia();\r\n    }\r\n  }\r\n\r\n  // Navigate to previous media in modal\r\n  prevModalMedia(): void {\r\n    if (this.modalMediaItems.length > 1) {\r\n      this.currentModalIndex =\r\n        this.currentModalIndex > 0\r\n          ? this.currentModalIndex - 1\r\n          : this.modalMediaItems.length - 1;\r\n      this.updateModalMedia();\r\n    }\r\n  }\r\n\r\n  // Check if modal has multiple media items\r\n  hasMultipleModalMedia(): boolean {\r\n    return this.modalMediaItems.length > 1;\r\n  }\r\n}\r\n", "<div class=\"d-flex align-items-center justify-content-between py-4\">\r\n  <h1 class=\"fw-bold text-dark-blue mb-0\">\r\n    My Advertisements\r\n    <span class=\"fs-6 text-dark-blue fw-semibold ms-0\">({{ this.page.totalElements }})</span>\r\n  </h1>\r\n</div>\r\n\r\n<div class=\"tab-pane fade show active\" id=\"kt_tab_pane_1\">\r\n  <div class=\"row g-4\">\r\n    <!-- Advertisement card for each post -->\r\n    <div class=\"col-md-3 col-sm-6 col-12\" *ngFor=\"let post of rows; let i = index\">\r\n      <div\r\n        class=\"card h-100 border-0 shadow-sm overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1\"\r\n        (click)=\"viewPropertyDetails(post)\" role=\"button\" tabindex=\"0\">\r\n        <!-- Image slider section -->\r\n        <div class=\"position-relative\">\r\n          <div class=\"media-container cursor-pointer\" (click)=\"openMediaModal(mediaModal, post, $event)\"\r\n            id=\"mediaContainer_{{ post.id }}\">\r\n            <img [src]=\"getImageFromGallery(post)\" class=\"img-fluid rounded-top\"\r\n              style=\"height: 180px; object-fit: cover; width: 100%; transition: transform 0.3s ease;\"\r\n              alt=\"{{ post.type }} image\" />\r\n\r\n            <!-- Video play icon overlay -->\r\n            <div *ngIf=\"isCurrentItemVideo(post)\"\r\n              class=\"video-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-black bg-opacity-30\">\r\n              <i class=\"bi bi-play-circle-fill text-white fs-2\"></i>\r\n            </div>\r\n\r\n            <!-- Gallery icon overlay -->\r\n            <div *ngIf=\"!isCurrentItemVideo(post)\"\r\n              class=\"gallery-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-black bg-opacity-20\">\r\n              <i class=\"bi bi-images text-white fs-3\"></i>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Image navigation controls -->\r\n          <div *ngIf=\"hasMultipleImages(post)\"\r\n            class=\"image-navigation position-absolute top-50 start-0 w-100 d-flex justify-content-between px-2\"\r\n            (click)=\"$event.stopPropagation()\">\r\n            <button class=\"btn btn-icon btn-sm btn-white shadow-sm rounded-circle bg-light-dark-blue\"\r\n              (click)=\"prevImage(post, $event)\" aria-label=\"Previous image\">\r\n              <i class=\"bi bi-chevron-left fs-6\"></i>\r\n            </button>\r\n            <button class=\"btn btn-icon btn-sm btn-white shadow-sm rounded-circle bg-light-dark-blue\"\r\n              (click)=\"nextImage(post, $event)\" aria-label=\"Next image\">\r\n              <i class=\"bi bi-chevron-right fs-6\"></i>\r\n            </button>\r\n          </div>\r\n\r\n          <!-- Image counter -->\r\n          <div *ngIf=\"hasMultipleImages(post)\"\r\n            class=\"image-counter position-absolute bottom-0 end-0 bg-dark bg-opacity-75 text-white px-2 py-1 rounded-top-start fs-7\"\r\n            (click)=\"$event.stopPropagation()\">\r\n            {{ getCurrentImageIndex(post) + 1 }}/{{ getAllImagesFromGallery(post).length }}\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Card content -->\r\n        <div class=\"card-body p-4\">\r\n          <h5 class=\"fs-5 fw-bold text-dark mb-3 text-truncate cursor-pointer text-hover-dark-blue\">\r\n            {{ post.type }}\r\n          </h5>\r\n\r\n          <!-- Property details -->\r\n          <div class=\"d-flex flex-wrap gap-3 mb-4\">\r\n            <div class=\"border border-gray-200 rounded p-3 flex-grow-1\">\r\n              <div class=\"fs-6 fw-semibold text-dark\">{{ post.area.name_en }}</div>\r\n              <div class=\"fs-7 text-muted\">Area</div>\r\n            </div>\r\n            <div class=\"border border-gray-200 rounded p-3 flex-grow-1\">\r\n              <div class=\"fs-6 fw-semibold text-dark\">{{ post.city.name_en }}</div>\r\n              <div class=\"fs-7 text-muted\">City</div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- View button -->\r\n          <a class=\"btn btn-sm btn-light-dark-blue fw-semibold w-100\" (click)=\"viewPropertyDetails(post)\" role=\"button\"\r\n            aria-label=\"View property details\">\r\n            <i class=\"fa fa-eye me-2\"></i>View\r\n          </a>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"mt-5\">\r\n    <app-pagination [totalItems]=\"page.totalElements\" [itemsPerPage]=\"page.limit\" [currentPage]=\"page.pageNumber\"\r\n      (pageChange)=\"onPageChange($event)\">\r\n    </app-pagination>\r\n  </div>\r\n</div>\r\n\r\n<!-- Media Modal -->\r\n<ng-template #mediaModal let-modal>\r\n  <div class=\"modal-header\">\r\n    <h4 class=\"modal-title\">\r\n      {{ selectedMediaType === \"video\" ? \"Video\" : \"Image\" }}\r\n      <span *ngIf=\"hasMultipleModalMedia()\" class=\"text-muted ms-2 fs-6\">\r\n        ({{ currentModalIndex + 1 }}/{{ modalMediaItems.length }})\r\n      </span>\r\n    </h4>\r\n    <button type=\"button\" class=\"btn-close\" (click)=\"modal.dismiss()\"></button>\r\n  </div>\r\n  <div class=\"modal-body position-relative\">\r\n    <div class=\"text-center\">\r\n      <!-- Video player -->\r\n      <div *ngIf=\"selectedMediaType === 'video' && selectedMediaUrl\" class=\"video-container\">\r\n        <!-- Native video player -->\r\n        <video [src]=\"selectedMediaUrl\" controls autoplay class=\"w-100\" style=\"max-height: 70vh\"\r\n          controlsList=\"nodownload\" preload=\"auto\"></video>\r\n\r\n        <!-- Fallback message if video doesn't play -->\r\n        <div class=\"mt-3 text-muted small\">\r\n          <a [href]=\"selectedMediaUrl\" target=\"_blank\">\r\n            <i class=\"bi bi-box-arrow-up-right me-1\"></i>\r\n            Open in new tab\r\n          </a>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Image viewer -->\r\n      <img *ngIf=\"selectedMediaType === 'image' && selectedMediaUrl\" [src]=\"selectedMediaUrl\" class=\"img-fluid\"\r\n        style=\"max-height: 70vh\" />\r\n    </div>\r\n\r\n    <!-- Navigation buttons for multiple media items -->\r\n    <div *ngIf=\"hasMultipleModalMedia()\" class=\"modal-navigation\">\r\n      <!-- Previous button -->\r\n      <button class=\"btn btn-primary btn-icon position-absolute top-50 start-0 translate-middle-y ms-3\"\r\n        (click)=\"prevModalMedia()\" style=\"z-index: 1050\">\r\n        <i class=\"bi bi-chevron-left\"></i>\r\n      </button>\r\n\r\n      <!-- Next button -->\r\n      <button class=\"btn btn-primary btn-icon position-absolute top-50 end-0 translate-middle-y me-3\"\r\n        (click)=\"nextModalMedia()\" style=\"z-index: 1050\">\r\n        <i class=\"bi bi-chevron-right\"></i>\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Modal footer with navigation dots -->\r\n  <div *ngIf=\"hasMultipleModalMedia()\" class=\"modal-footer justify-content-center\">\r\n    <div class=\"d-flex gap-2\">\r\n      <button *ngFor=\"let item of modalMediaItems; let i = index\" class=\"btn btn-sm rounded-circle p-1\"\r\n        [class.btn-primary]=\"i === currentModalIndex\" [class.btn-light]=\"i !== currentModalIndex\"\r\n        (click)=\"currentModalIndex = i; updateModalMedia()\" style=\"width: 12px; height: 12px\"></button>\r\n    </div>\r\n  </div>\r\n</ng-template>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,4CAA4C;AAK9E,SAASC,mBAAmB,QAAQ,0CAA0C;;;;;;;;;ICgBlEC,EAAA,CAAAC,cAAA,cAC4I;IAC1ID,EAAA,CAAAE,SAAA,YAAsD;IACxDF,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,cAC8I;IAC5ID,EAAA,CAAAE,SAAA,YAA4C;IAC9CF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAIRH,EAAA,CAAAC,cAAA,cAEqC;IAAnCD,EAAA,CAAAI,UAAA,mBAAAC,0DAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASH,MAAA,CAAAI,eAAA,EAAwB;IAAA,EAAC;IAClCV,EAAA,CAAAC,cAAA,iBACgE;IAA9DD,EAAA,CAAAI,UAAA,mBAAAO,6DAAAL,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAI,OAAA,GAAAZ,EAAA,CAAAa,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAS,WAAA,CAASM,MAAA,CAAAC,SAAA,CAAAJ,OAAA,EAAAN,MAAA,CAAuB;IAAA,EAAC;IACjCN,EAAA,CAAAE,SAAA,YAAuC;IACzCF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAC4D;IAA1DD,EAAA,CAAAI,UAAA,mBAAAa,6DAAAX,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAI,OAAA,GAAAZ,EAAA,CAAAa,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAS,WAAA,CAASM,MAAA,CAAAG,SAAA,CAAAN,OAAA,EAAAN,MAAA,CAAuB;IAAA,EAAC;IACjCN,EAAA,CAAAE,SAAA,YAAwC;IAE5CF,EADE,CAAAG,YAAA,EAAS,EACL;;;;;;IAGNH,EAAA,CAAAC,cAAA,cAEqC;IAAnCD,EAAA,CAAAI,UAAA,mBAAAe,0DAAAb,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,OAAApB,EAAA,CAAAS,WAAA,CAASH,MAAA,CAAAI,eAAA,EAAwB;IAAA,EAAC;IAClCV,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAG,YAAA,EAAM;;;;;IADJH,EAAA,CAAAsB,SAAA,EACF;IADEtB,EAAA,CAAAuB,kBAAA,MAAAR,MAAA,CAAAS,oBAAA,CAAAZ,OAAA,YAAAG,MAAA,CAAAU,uBAAA,CAAAb,OAAA,EAAAc,MAAA,MACF;;;;;;IA3CJ1B,EADF,CAAAC,cAAA,aAA+E,cAGZ;IAA/DD,EAAA,CAAAI,UAAA,mBAAAuB,oDAAA;MAAA,MAAAf,OAAA,GAAAZ,EAAA,CAAAO,aAAA,CAAAqB,GAAA,EAAAd,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAS,WAAA,CAASM,MAAA,CAAAc,mBAAA,CAAAjB,OAAA,CAAyB;IAAA,EAAC;IAGjCZ,EADF,CAAAC,cAAA,cAA+B,cAEO;IADQD,EAAA,CAAAI,UAAA,mBAAA0B,oDAAAxB,MAAA;MAAA,MAAAM,OAAA,GAAAZ,EAAA,CAAAO,aAAA,CAAAqB,GAAA,EAAAd,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,MAAAkB,aAAA,GAAA/B,EAAA,CAAAgC,WAAA;MAAA,OAAAhC,EAAA,CAAAS,WAAA,CAASM,MAAA,CAAAkB,cAAA,CAAAF,aAAA,EAAAnB,OAAA,EAAAN,MAAA,CAAwC;IAAA,EAAC;IAE5FN,EAAA,CAAAE,SAAA,cAEgC;IAShCF,EANA,CAAAkC,UAAA,IAAAC,oCAAA,kBAC4I,IAAAC,oCAAA,kBAME;IAGhJpC,EAAA,CAAAG,YAAA,EAAM;IAiBNH,EAdA,CAAAkC,UAAA,IAAAG,oCAAA,kBAEqC,IAAAC,oCAAA,kBAcA;IAGvCtC,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,cAA2B,cACiE;IACxFD,EAAA,CAAAqB,MAAA,IACF;IAAArB,EAAA,CAAAG,YAAA,EAAK;IAKDH,EAFJ,CAAAC,cAAA,eAAyC,eACqB,eAClB;IAAAD,EAAA,CAAAqB,MAAA,IAAuB;IAAArB,EAAA,CAAAG,YAAA,EAAM;IACrEH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAqB,MAAA,YAAI;IACnCrB,EADmC,CAAAG,YAAA,EAAM,EACnC;IAEJH,EADF,CAAAC,cAAA,eAA4D,eAClB;IAAAD,EAAA,CAAAqB,MAAA,IAAuB;IAAArB,EAAA,CAAAG,YAAA,EAAM;IACrEH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAqB,MAAA,YAAI;IAErCrB,EAFqC,CAAAG,YAAA,EAAM,EACnC,EACF;IAGNH,EAAA,CAAAC,cAAA,aACqC;IADuBD,EAAA,CAAAI,UAAA,mBAAAmC,mDAAA;MAAA,MAAA3B,OAAA,GAAAZ,EAAA,CAAAO,aAAA,CAAAqB,GAAA,EAAAd,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAS,WAAA,CAASM,MAAA,CAAAc,mBAAA,CAAAjB,OAAA,CAAyB;IAAA,EAAC;IAE7FZ,EAAA,CAAAE,SAAA,aAA8B;IAAAF,EAAA,CAAAqB,MAAA,aAChC;IAGNrB,EAHM,CAAAG,YAAA,EAAI,EACA,EACF,EACF;;;;;IAjEEH,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAAwC,sBAAA,0BAAA5B,OAAA,CAAA6B,EAAA,KAAiC;IAG/BzC,EAAA,CAAAsB,SAAA,EAA2B;IAA3BtB,EAAA,CAAAwC,sBAAA,YAAA5B,OAAA,CAAA8B,IAAA,WAA2B;IAFxB1C,EAAA,CAAA2C,UAAA,QAAA5B,MAAA,CAAA6B,mBAAA,CAAAhC,OAAA,GAAAZ,EAAA,CAAA6C,aAAA,CAAiC;IAKhC7C,EAAA,CAAAsB,SAAA,EAA8B;IAA9BtB,EAAA,CAAA2C,UAAA,SAAA5B,MAAA,CAAA+B,kBAAA,CAAAlC,OAAA,EAA8B;IAM9BZ,EAAA,CAAAsB,SAAA,EAA+B;IAA/BtB,EAAA,CAAA2C,UAAA,UAAA5B,MAAA,CAAA+B,kBAAA,CAAAlC,OAAA,EAA+B;IAOjCZ,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAA2C,UAAA,SAAA5B,MAAA,CAAAgC,iBAAA,CAAAnC,OAAA,EAA6B;IAc7BZ,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAA2C,UAAA,SAAA5B,MAAA,CAAAgC,iBAAA,CAAAnC,OAAA,EAA6B;IAUjCZ,EAAA,CAAAsB,SAAA,GACF;IADEtB,EAAA,CAAAgD,kBAAA,MAAApC,OAAA,CAAA8B,IAAA,MACF;IAK4C1C,EAAA,CAAAsB,SAAA,GAAuB;IAAvBtB,EAAA,CAAAiD,iBAAA,CAAArC,OAAA,CAAAsC,IAAA,CAAAC,OAAA,CAAuB;IAIvBnD,EAAA,CAAAsB,SAAA,GAAuB;IAAvBtB,EAAA,CAAAiD,iBAAA,CAAArC,OAAA,CAAAwC,IAAA,CAAAD,OAAA,CAAuB;;;;;IA2BvEnD,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAsB,SAAA,EACF;IADEtB,EAAA,CAAAuB,kBAAA,OAAAR,MAAA,CAAAsC,iBAAA,WAAAtC,MAAA,CAAAuC,eAAA,CAAA5B,MAAA,OACF;;;;;IAOA1B,EAAA,CAAAC,cAAA,cAAuF;IAErFD,EAAA,CAAAE,SAAA,gBACmD;IAIjDF,EADF,CAAAC,cAAA,cAAmC,YACY;IAC3CD,EAAA,CAAAE,SAAA,YAA6C;IAC7CF,EAAA,CAAAqB,MAAA,wBACF;IAEJrB,EAFI,CAAAG,YAAA,EAAI,EACA,EACF;;;;IAVGH,EAAA,CAAAsB,SAAA,EAAwB;IAAxBtB,EAAA,CAAA2C,UAAA,QAAA5B,MAAA,CAAAwC,gBAAA,EAAAvD,EAAA,CAAA6C,aAAA,CAAwB;IAK1B7C,EAAA,CAAAsB,SAAA,GAAyB;IAAzBtB,EAAA,CAAA2C,UAAA,SAAA5B,MAAA,CAAAwC,gBAAA,EAAAvD,EAAA,CAAA6C,aAAA,CAAyB;;;;;IAQhC7C,EAAA,CAAAE,SAAA,cAC6B;;;;IADkCF,EAAA,CAAA2C,UAAA,QAAA5B,MAAA,CAAAwC,gBAAA,EAAAvD,EAAA,CAAA6C,aAAA,CAAwB;;;;;;IAOvF7C,EAFF,CAAAC,cAAA,cAA8D,iBAGT;IAAjDD,EAAA,CAAAI,UAAA,mBAAAoD,sEAAA;MAAAxD,EAAA,CAAAO,aAAA,CAAAkD,IAAA;MAAA,MAAA1C,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAS,WAAA,CAASM,MAAA,CAAA2C,cAAA,EAAgB;IAAA,EAAC;IAC1B1D,EAAA,CAAAE,SAAA,YAAkC;IACpCF,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,iBACmD;IAAjDD,EAAA,CAAAI,UAAA,mBAAAuD,sEAAA;MAAA3D,EAAA,CAAAO,aAAA,CAAAkD,IAAA;MAAA,MAAA1C,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAS,WAAA,CAASM,MAAA,CAAA6C,cAAA,EAAgB;IAAA,EAAC;IAC1B5D,EAAA,CAAAE,SAAA,YAAmC;IAEvCF,EADE,CAAAG,YAAA,EAAS,EACL;;;;;;IAMJH,EAAA,CAAAC,cAAA,iBAEwF;IAAtFD,EAAA,CAAAI,UAAA,mBAAAyD,gFAAA;MAAA,MAAAC,KAAA,GAAA9D,EAAA,CAAAO,aAAA,CAAAwD,IAAA,EAAAC,KAAA;MAAA,MAAAjD,MAAA,GAAAf,EAAA,CAAAa,aAAA;MAAAE,MAAA,CAAAsC,iBAAA,GAAAS,KAAA;MAAA,OAAA9D,EAAA,CAAAS,WAAA,CAAgCM,MAAA,CAAAkD,gBAAA,EAAkB;IAAA,EAAC;IAAmCjE,EAAA,CAAAG,YAAA,EAAS;;;;;IADjDH,EAA9C,CAAAkE,WAAA,gBAAAJ,KAAA,KAAA/C,MAAA,CAAAsC,iBAAA,CAA6C,cAAAS,KAAA,KAAA/C,MAAA,CAAAsC,iBAAA,CAA4C;;;;;IAF7FrD,EADF,CAAAC,cAAA,cAAiF,cACrD;IACxBD,EAAA,CAAAkC,UAAA,IAAAiC,uDAAA,qBAEwF;IAE5FnE,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAJuBH,EAAA,CAAAsB,SAAA,GAAoB;IAApBtB,EAAA,CAAA2C,UAAA,YAAA5B,MAAA,CAAAuC,eAAA,CAAoB;;;;;;IAjD/CtD,EADF,CAAAC,cAAA,cAA0B,aACA;IACtBD,EAAA,CAAAqB,MAAA,GACA;IAAArB,EAAA,CAAAkC,UAAA,IAAAkC,8CAAA,mBAAmE;IAGrEpE,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,iBAAkE;IAA1BD,EAAA,CAAAI,UAAA,mBAAAiE,gEAAA;MAAA,MAAAC,QAAA,GAAAtE,EAAA,CAAAO,aAAA,CAAAgE,GAAA,EAAAzD,SAAA;MAAA,OAAAd,EAAA,CAAAS,WAAA,CAAS6D,QAAA,CAAAE,OAAA,EAAe;IAAA,EAAC;IACnExE,EADoE,CAAAG,YAAA,EAAS,EACvE;IAEJH,EADF,CAAAC,cAAA,cAA0C,cACf;IAiBvBD,EAfA,CAAAkC,UAAA,IAAAuC,6CAAA,kBAAuF,IAAAC,6CAAA,kBAgB1D;IAC/B1E,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAkC,UAAA,IAAAyC,6CAAA,kBAA8D;IAahE3E,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAkC,UAAA,KAAA0C,8CAAA,kBAAiF;;;;IA9C7E5E,EAAA,CAAAsB,SAAA,GACA;IADAtB,EAAA,CAAAgD,kBAAA,MAAAjC,MAAA,CAAA8D,iBAAA,sCACA;IAAO7E,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAA2C,UAAA,SAAA5B,MAAA,CAAA+D,qBAAA,GAA6B;IAS9B9E,EAAA,CAAAsB,SAAA,GAAuD;IAAvDtB,EAAA,CAAA2C,UAAA,SAAA5B,MAAA,CAAA8D,iBAAA,gBAAA9D,MAAA,CAAAwC,gBAAA,CAAuD;IAevDvD,EAAA,CAAAsB,SAAA,EAAuD;IAAvDtB,EAAA,CAAA2C,UAAA,SAAA5B,MAAA,CAAA8D,iBAAA,gBAAA9D,MAAA,CAAAwC,gBAAA,CAAuD;IAKzDvD,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAA2C,UAAA,SAAA5B,MAAA,CAAA+D,qBAAA,GAA6B;IAgB/B9E,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAA2C,UAAA,SAAA5B,MAAA,CAAA+D,qBAAA,GAA6B;;;AD9HrC,OAAM,MAAOC,eAAgB,SAAQjF,iBAAiB;EAYxCkF,EAAA;EACAC,eAAA;EACFC,YAAA;EACAC,SAAA;EACAC,MAAA;EAfV;EACAC,mBAAmB,GAAiC,EAAE;EAEtD;EACA9B,gBAAgB,GAAkB,IAAI;EACtCsB,iBAAiB,GAAW,OAAO;EACnCS,YAAY,GAA2B,IAAI;EAC3ChC,eAAe,GAAU,EAAE;EAC3BD,iBAAiB,GAAW,CAAC;EAE7BkC,YACYP,EAAqB,EACrBC,eAAgC,EAClCC,YAAsB,EACtBC,SAAuB,EACvBC,MAAc;IAEtB,KAAK,CAACJ,EAAE,CAAC;IANC,KAAAA,EAAE,GAAFA,EAAE;IACF,KAAAC,eAAe,GAAfA,eAAe;IACjB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,MAAM,GAANA,MAAM;IAGd,MAAMI,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACpD,IAAIC,IAAI,GAAGH,QAAQ,GAAGI,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC,GAAG,IAAI;IAEjD,IAAI,CAACM,UAAU,CAACb,eAAe,CAAC;IAChC,IAAI,CAACc,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,QAAQ,GAAG,MAAM;IACtB,IAAI,CAACC,IAAI,CAACC,OAAO,GAAG;MAAEC,QAAQ,EAAER,IAAI,EAAEQ,QAAQ;MAAEC,eAAe,EAAE;IAAC,CAAE;EACtE;EAEA;EACAnE,cAAcA,CAACoE,OAAY,EAAEC,IAAS,EAAEC,KAAa;IACnD;IACA,IAAIA,KAAK,EAAE;MACTA,KAAK,CAAC7F,eAAe,EAAE;IACzB;IAEA8F,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEH,IAAI,CAAC7D,EAAE,CAAC;IAE/C;IACA,IAAI,CAACa,eAAe,GAAG,IAAI,CAAC7B,uBAAuB,CAAC6E,IAAI,CAAC;IACzD,IAAI,CAACjD,iBAAiB,GAAG,IAAI,CAAC7B,oBAAoB,CAAC8E,IAAI,CAAC;IAExD;IACA,IAAI,CAACrC,gBAAgB,EAAE;IAEvB;IACA,IAAI,CAACiB,YAAY,CAACwB,IAAI,CAACL,OAAO,EAAE;MAC9BM,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE,aAAa;MAC1BC,QAAQ,EAAE;KACX,CAAC;IAEFN,OAAO,CAACC,GAAG,CACT,oBAAoB,EACpB,IAAI,CAAC5B,iBAAiB,EACtB,IAAI,CAACtB,gBAAgB,CACtB;EACH;EAEA;EACA9B,uBAAuBA,CAAC6E,IAAS;IAC/B,MAAMS,SAAS,GAAU,EAAE;IAE3B;IACA,IAAIT,IAAI,CAACU,OAAO,IAAIV,IAAI,CAACU,OAAO,CAACtF,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAMuF,UAAU,GAAGX,IAAI,CAACU,OAAO,CAACE,MAAM,CAAEC,IAAS,IAAKA,IAAI,CAACC,GAAG,CAAC;MAC/DL,SAAS,CAACM,IAAI,CAAC,GAAGJ,UAAU,CAAC;IAC/B;IAEA;IACA,IAAIX,IAAI,CAACgB,OAAO,EAAE;MAChBd,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEH,IAAI,CAACgB,OAAO,CAAC;MACtDP,SAAS,CAACM,IAAI,CAAC;QACbD,GAAG,EAAEd,IAAI,CAACgB,OAAO;QACjB5E,IAAI,EAAE;OACP,CAAC;IACJ;IAEA;IACA,IAAI4D,IAAI,CAACiB,oBAAoB,EAAE;MAC7Bf,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEH,IAAI,CAACiB,oBAAoB,CAAC;MAChFR,SAAS,CAACM,IAAI,CAAC;QACbD,GAAG,EAAEd,IAAI,CAACiB,oBAAoB;QAC9B7E,IAAI,EAAE;OACP,CAAC;IACJ;IAEA8D,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEH,IAAI,CAAC7D,EAAE,EAAE,GAAG,EAAEsE,SAAS,CAACrF,MAAM,CAAC;IAEpE;IACA,IAAIqF,SAAS,CAACrF,MAAM,KAAK,CAAC,EAAE;MAC1B,OAAO,CAAC;QAAE0F,GAAG,EAAE,kCAAkC;QAAE1E,IAAI,EAAE;MAAO,CAAE,CAAC;IACrE;IAEA,OAAOqE,SAAS;EAClB;EAEA;EACAnE,mBAAmBA,CAAC0D,IAAS;IAC3B,MAAMW,UAAU,GAAG,IAAI,CAACxF,uBAAuB,CAAC6E,IAAI,CAAC;IACrD,MAAMtC,KAAK,GAAG,IAAI,CAACxC,oBAAoB,CAAC8E,IAAI,CAAC;IAC7C,MAAMkB,WAAW,GAAGP,UAAU,CAACjD,KAAK,CAAC;IAErC,OAAOwD,WAAW,CAACJ,GAAG;EACxB;EAEA;EACAtE,kBAAkBA,CAACwD,IAAS;IAC1B,MAAMW,UAAU,GAAG,IAAI,CAACxF,uBAAuB,CAAC6E,IAAI,CAAC;IACrD,MAAMtC,KAAK,GAAG,IAAI,CAACxC,oBAAoB,CAAC8E,IAAI,CAAC;IAC7C,OAAOW,UAAU,CAACjD,KAAK,CAAC,CAACtB,IAAI,KAAK,OAAO;EAC3C;EAEA;EACA+E,kBAAkBA,CAACnB,IAAS;IAC1B,MAAMW,UAAU,GAAG,IAAI,CAACxF,uBAAuB,CAAC6E,IAAI,CAAC;IACrD,MAAMtC,KAAK,GAAG,IAAI,CAACxC,oBAAoB,CAAC8E,IAAI,CAAC;IAC7C,MAAMkB,WAAW,GAAGP,UAAU,CAACjD,KAAK,CAAC;IAErC,OAAOwD,WAAW,CAAC9E,IAAI,KAAK,OAAO,GAAG8E,WAAW,CAACJ,GAAG,GAAG,IAAI;EAC9D;EAEA;EACArE,iBAAiBA,CAACuD,IAAS;IACzB,OAAO,IAAI,CAAC7E,uBAAuB,CAAC6E,IAAI,CAAC,CAAC5E,MAAM,GAAG,CAAC;EACtD;EAEA;EACAF,oBAAoBA,CAAC8E,IAAS;IAC5B,IAAI,CAAC,IAAI,CAACjB,mBAAmB,CAACiB,IAAI,CAAC7D,EAAE,CAAC,EAAE;MACtC,IAAI,CAAC4C,mBAAmB,CAACiB,IAAI,CAAC7D,EAAE,CAAC,GAAG,CAAC;IACvC;IACA,OAAO,IAAI,CAAC4C,mBAAmB,CAACiB,IAAI,CAAC7D,EAAE,CAAC;EAC1C;EAEA;EACAvB,SAASA,CAACoF,IAAS,EAAEC,KAAY;IAC/BA,KAAK,CAAC7F,eAAe,EAAE;IACvB,MAAMgH,MAAM,GAAG,IAAI,CAACjG,uBAAuB,CAAC6E,IAAI,CAAC;IACjD,IAAI,CAACjB,mBAAmB,CAACiB,IAAI,CAAC7D,EAAE,CAAC,GAC/B,CAAC,IAAI,CAACjB,oBAAoB,CAAC8E,IAAI,CAAC,GAAG,CAAC,IAAIoB,MAAM,CAAChG,MAAM;IACvD,IAAI,CAACsD,EAAE,CAAC2C,YAAY,EAAE;EACxB;EAEA;EACA3G,SAASA,CAACsF,IAAS,EAAEC,KAAY;IAC/BA,KAAK,CAAC7F,eAAe,EAAE;IACvB,MAAMgH,MAAM,GAAG,IAAI,CAACjG,uBAAuB,CAAC6E,IAAI,CAAC;IACjD,MAAMsB,YAAY,GAAG,IAAI,CAACpG,oBAAoB,CAAC8E,IAAI,CAAC;IACpD,IAAI,CAACjB,mBAAmB,CAACiB,IAAI,CAAC7D,EAAE,CAAC,GAC/BmF,YAAY,GAAG,CAAC,GAAGA,YAAY,GAAG,CAAC,GAAGF,MAAM,CAAChG,MAAM,GAAG,CAAC;IACzD,IAAI,CAACsD,EAAE,CAAC2C,YAAY,EAAE;EACxB;EAEA9F,mBAAmBA,CAACgG,WAAgB;IAChC,IAAI,CAACzC,MAAM,CAAC0C,QAAQ,CAAC,CAAC,0CAA0C,CAAC,EAAE;MACjEC,WAAW,EAAE;QAAEC,MAAM,EAAEH,WAAW,CAACpF;MAAE;KACtC,CAAC;EAEN;EACA;EACAwB,gBAAgBA,CAAA;IACd,IACE,IAAI,CAACX,eAAe,CAAC5B,MAAM,GAAG,CAAC,IAC/B,IAAI,CAAC2B,iBAAiB,IAAI,CAAC,IAC3B,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACC,eAAe,CAAC5B,MAAM,EACpD;MACA,MAAM8F,WAAW,GAAG,IAAI,CAAClE,eAAe,CAAC,IAAI,CAACD,iBAAiB,CAAC;MAChE,IAAI,CAACE,gBAAgB,GAAGiE,WAAW,CAACJ,GAAG;MACvC,IAAI,CAACvC,iBAAiB,GAAG2C,WAAW,CAAC9E,IAAI;MAEzC;MACA,IAAI,IAAI,CAACmC,iBAAiB,KAAK,OAAO,IAAI,IAAI,CAACtB,gBAAgB,EAAE;QAC/D,IAAI,CAAC+B,YAAY,GAAG,IAAI,CAACH,SAAS,CAAC8C,8BAA8B,CAC/D,IAAI,CAAC1E,gBAAgB,CACtB;MACH,CAAC,MAAM;QACL,IAAI,CAAC+B,YAAY,GAAG,IAAI;MAC1B;IACF;EACF;EAEA;EACA1B,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACN,eAAe,CAAC5B,MAAM,GAAG,CAAC,EAAE;MACnC,IAAI,CAAC2B,iBAAiB,GACpB,CAAC,IAAI,CAACA,iBAAiB,GAAG,CAAC,IAAI,IAAI,CAACC,eAAe,CAAC5B,MAAM;MAC5D,IAAI,CAACuC,gBAAgB,EAAE;IACzB;EACF;EAEA;EACAP,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACJ,eAAe,CAAC5B,MAAM,GAAG,CAAC,EAAE;MACnC,IAAI,CAAC2B,iBAAiB,GACpB,IAAI,CAACA,iBAAiB,GAAG,CAAC,GACtB,IAAI,CAACA,iBAAiB,GAAG,CAAC,GAC1B,IAAI,CAACC,eAAe,CAAC5B,MAAM,GAAG,CAAC;MACrC,IAAI,CAACuC,gBAAgB,EAAE;IACzB;EACF;EAEA;EACAa,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACxB,eAAe,CAAC5B,MAAM,GAAG,CAAC;EACxC;;qCA7MWqD,eAAe,EAAA/E,EAAA,CAAAkI,iBAAA,CAAAlI,EAAA,CAAAmI,iBAAA,GAAAnI,EAAA,CAAAkI,iBAAA,CAAAE,EAAA,CAAAC,eAAA,GAAArI,EAAA,CAAAkI,iBAAA,CAAAI,EAAA,CAAAC,QAAA,GAAAvI,EAAA,CAAAkI,iBAAA,CAAAM,EAAA,CAAAC,YAAA,GAAAzI,EAAA,CAAAkI,iBAAA,CAAAQ,EAAA,CAAAC,MAAA;EAAA;;UAAf5D,eAAe;IAAA6D,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAA9I,EAAA,CAAA+I,0BAAA,EAAA/I,EAAA,CAAAgJ,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCf1BtJ,EADF,CAAAC,cAAA,aAAoE,YAC1B;QACtCD,EAAA,CAAAqB,MAAA,0BACA;QAAArB,EAAA,CAAAC,cAAA,cAAmD;QAAAD,EAAA,CAAAqB,MAAA,GAA+B;QAEtFrB,EAFsF,CAAAG,YAAA,EAAO,EACtF,EACD;QAGJH,EADF,CAAAC,cAAA,aAA0D,aACnC;QAEnBD,EAAA,CAAAkC,UAAA,IAAAsH,8BAAA,mBAA+E;QAyEjFxJ,EAAA,CAAAG,YAAA,EAAM;QAGJH,EADF,CAAAC,cAAA,aAAkB,wBAEsB;QAApCD,EAAA,CAAAI,UAAA,wBAAAqJ,8DAAAnJ,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAmJ,GAAA;UAAA,OAAA1J,EAAA,CAAAS,WAAA,CAAc8I,GAAA,CAAAI,YAAA,CAAArJ,MAAA,CAAoB;QAAA,EAAC;QAGzCN,EAFI,CAAAG,YAAA,EAAiB,EACb,EACF;QAGNH,EAAA,CAAAkC,UAAA,KAAA0H,uCAAA,iCAAA5J,EAAA,CAAA6J,sBAAA,CAAmC;;;QA1FoB7J,EAAA,CAAAsB,SAAA,GAA+B;QAA/BtB,EAAA,CAAAgD,kBAAA,MAAAuG,GAAA,CAAAtD,IAAA,CAAA6D,aAAA,MAA+B;QAO3B9J,EAAA,CAAAsB,SAAA,GAAS;QAATtB,EAAA,CAAA2C,UAAA,YAAA4G,GAAA,CAAAQ,IAAA,CAAS;QA4EhD/J,EAAA,CAAAsB,SAAA,GAAiC;QAA6BtB,EAA9D,CAAA2C,UAAA,eAAA4G,GAAA,CAAAtD,IAAA,CAAA6D,aAAA,CAAiC,iBAAAP,GAAA,CAAAtD,IAAA,CAAA+D,KAAA,CAA4B,gBAAAT,GAAA,CAAAtD,IAAA,CAAAgE,UAAA,CAAgC;;;mBD1ErGpK,YAAY,EAAAqK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAErK,mBAAmB;IAAAsK,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}