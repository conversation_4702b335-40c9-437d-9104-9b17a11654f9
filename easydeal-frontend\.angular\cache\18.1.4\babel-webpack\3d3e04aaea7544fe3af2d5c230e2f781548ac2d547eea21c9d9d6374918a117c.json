{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../../_metronic/shared/keenicon/keenicon.component\";\nimport * as i2 from \"@angular/common\";\nfunction EmptyPropertiesCardComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"input\", 11, 0);\n    i0.ɵɵlistener(\"change\", function EmptyPropertiesCardComponent_div_10_Template_input_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function EmptyPropertiesCardComponent_div_10_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const fileInput_r3 = i0.ɵɵreference(2);\n      return i0.ɵɵresetView(fileInput_r3.click());\n    });\n    i0.ɵɵelement(4, \"img\", 13);\n    i0.ɵɵtext(5, \" Upload Property Units File \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EmptyPropertiesCardComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function EmptyPropertiesCardComponent_div_11_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.downloadTemplate());\n    });\n    i0.ɵɵtext(2, \" Download Ads-template.xls \");\n    i0.ɵɵelement(3, \"app-keenicon\", 16);\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class EmptyPropertiesCardComponent {\n  userRole;\n  customMessage = '';\n  onFileUpload;\n  onDownloadTemplate;\n  get displayMessage() {\n    if (this.customMessage) {\n      return this.customMessage;\n    }\n    switch (this.userRole) {\n      case 'broker':\n        return \"Create a new property or upload the property units you're working on.\";\n      case 'developer':\n        return 'Create a new project or start building your development portfolio.';\n      default:\n        return \"Create a new property or upload the property units you're working on.\";\n    }\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (file && this.onFileUpload) {\n      this.onFileUpload(file);\n    }\n  }\n  downloadTemplate() {\n    if (this.onDownloadTemplate) {\n      this.onDownloadTemplate();\n    }\n  }\n  static ɵfac = function EmptyPropertiesCardComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || EmptyPropertiesCardComponent)();\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: EmptyPropertiesCardComponent,\n    selectors: [[\"app-empty-properties-card\"]],\n    inputs: {\n      userRole: \"userRole\",\n      customMessage: \"customMessage\",\n      onFileUpload: \"onFileUpload\",\n      onDownloadTemplate: \"onDownloadTemplate\"\n    },\n    decls: 12,\n    vars: 3,\n    consts: [[\"fileInput\", \"\"], [1, \"card-body\", \"d-flex\", \"flex-column\", \"justify-content-between\", \"mt-9\", \"bgi-no-repeat\", \"bgi-size-cover\", \"bgi-position-x-center\", \"pb-0\"], [\"src\", \"../../../../assets/media/broker/empty-data-and-properties.png\", \"alt\", \"\", 1, \"mx-auto\", \"h-150px\", \"h-lg-200px\", \"theme-light-show\"], [\"src\", \"../../../../assets/media/broker/empty-data-and-properties.png\", \"alt\", \"\", 1, \"mx-auto\", \"h-150px\", \"h-lg-200px\", \"theme-dark-show\"], [1, \"mb-10\", \"mt-10\"], [1, \"fs-2hx\", \"fw-bold\", \"text-dark-blue\", \"text-center\", \"mb-1\"], [1, \"me-2\"], [1, \"fs-4\", \"fw-bold\", \"text-gray-500\", \"text-center\", \"mb-5\"], [\"class\", \"text-center mb-2\", 4, \"ngIf\"], [\"class\", \"text-center\", 4, \"ngIf\"], [1, \"text-center\", \"mb-2\"], [\"type\", \"file\", \"accept\", \".xlsx,.xls\", \"hidden\", \"\", 3, \"change\"], [\"type\", \"button\", 1, \"btn\", \"btn-md\", \"btn-dark-blue\", \"fw-bold\", 3, \"click\"], [\"src\", \"../../../../assets/media/broker/excellogo.png\", \"alt\", \"\", 1, \"mx-auto\", \"h-30px\"], [1, \"text-center\"], [\"type\", \"button\", 1, \"fw-bold\", \"text-success\", \"bg-transparent\", \"border-0\", \"cursor-pointer\", 3, \"click\"], [\"name\", \"folder-down\", \"type\", \"outline\", 1, \"text-success\", \"fs-3\"]],\n    template: function EmptyPropertiesCardComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1);\n        i0.ɵɵelement(1, \"img\", 2)(2, \"img\", 3);\n        i0.ɵɵelementStart(3, \"div\", 4)(4, \"div\", 5)(5, \"span\", 6);\n        i0.ɵɵtext(6, \"There is nothing currently!\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"div\", 7)(8, \"span\");\n        i0.ɵɵtext(9);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(10, EmptyPropertiesCardComponent_div_10_Template, 6, 0, \"div\", 8)(11, EmptyPropertiesCardComponent_div_11_Template, 4, 0, \"div\", 9);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(9);\n        i0.ɵɵtextInterpolate(ctx.displayMessage);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.userRole !== \"broker\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.userRole !== \"broker\");\n      }\n    },\n    dependencies: [i1.KeeniconComponent, i2.NgIf],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵlistener", "EmptyPropertiesCardComponent_div_10_Template_input_change_1_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onFileSelected", "ɵɵelementEnd", "EmptyPropertiesCardComponent_div_10_Template_button_click_3_listener", "fileInput_r3", "ɵɵreference", "click", "ɵɵelement", "ɵɵtext", "EmptyPropertiesCardComponent_div_11_Template_button_click_1_listener", "_r4", "downloadTemplate", "EmptyPropertiesCardComponent", "userRole", "customMessage", "onFileUpload", "onDownloadTemplate", "displayMessage", "event", "file", "target", "files", "selectors", "inputs", "decls", "vars", "consts", "template", "EmptyPropertiesCardComponent_Template", "rf", "ctx", "ɵɵtemplate", "EmptyPropertiesCardComponent_div_10_Template", "EmptyPropertiesCardComponent_div_11_Template", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\dataandproperties\\components\\empty-properties-card\\empty-properties-card.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\dataandproperties\\components\\empty-properties-card\\empty-properties-card.component.html"], "sourcesContent": ["import { Component, Input } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-empty-properties-card',\r\n  templateUrl: './empty-properties-card.component.html',\r\n  styleUrl: './empty-properties-card.component.scss',\r\n})\r\nexport class EmptyPropertiesCardComponent {\r\n  @Input() userRole: string ;\r\n  @Input() customMessage: string = '';\r\n  @Input() onFileUpload!: (file: File) => void;\r\n  @Input() onDownloadTemplate!: () => void;\r\n\r\n  get displayMessage(): string {\r\n    if (this.customMessage) {\r\n      return this.customMessage;\r\n    }\r\n\r\n    switch (this.userRole) {\r\n      case 'broker':\r\n        return \"Create a new property or upload the property units you're working on.\";\r\n      case 'developer':\r\n        return 'Create a new project or start building your development portfolio.';\r\n      default:\r\n        return \"Create a new property or upload the property units you're working on.\";\r\n    }\r\n  }\r\n\r\n  onFileSelected(event: any) {\r\n    const file = event.target.files[0];\r\n    if (file && this.onFileUpload) {\r\n      this.onFileUpload(file);\r\n    }\r\n  }\r\n\r\n  downloadTemplate() {\r\n    if (this.onDownloadTemplate) {\r\n      this.onDownloadTemplate();\r\n    }\r\n  }\r\n}\r\n", "<div\r\n  class=\"card-body d-flex flex-column justify-content-between mt-9 bgi-no-repeat bgi-size-cover bgi-position-x-center pb-0\">\r\n  <img class=\"mx-auto h-150px h-lg-200px theme-light-show\"\r\n    src=\"../../../../assets/media/broker/empty-data-and-properties.png\" alt=\"\" />\r\n  <img class=\"mx-auto h-150px h-lg-200px theme-dark-show\"\r\n    src=\"../../../../assets/media/broker/empty-data-and-properties.png\" alt=\"\" />\r\n  <div class=\"mb-10 mt-10\">\r\n    <div class=\"fs-2hx fw-bold text-dark-blue text-center mb-1\">\r\n      <span class=\"me-2\">There is nothing currently!</span>\r\n    </div>\r\n\r\n    <div class=\"fs-4 fw-bold text-gray-500 text-center mb-5\">\r\n      <span>{{ displayMessage }}</span>\r\n    </div>\r\n\r\n    <!-- Broker-only buttons -->\r\n    <div *ngIf=\"userRole !== 'broker'\" class=\"text-center mb-2\">\r\n      <input type=\"file\" #fileInput (change)=\"onFileSelected($event)\" accept=\".xlsx,.xls\" hidden />\r\n      <button type=\"button\" class=\"btn btn-md btn-dark-blue fw-bold\" (click)=\"fileInput.click()\">\r\n        <img class=\"mx-auto h-30px\" src=\"../../../../assets/media/broker/excellogo.png\" alt=\"\" />\r\n        Upload Property Units File\r\n      </button>\r\n    </div>\r\n\r\n    <div *ngIf=\"userRole !== 'broker'\" class=\"text-center\">\r\n      <button type=\"button\" class=\"fw-bold text-success bg-transparent border-0 cursor-pointer\"\r\n        (click)=\"downloadTemplate()\">\r\n        Download Ads-template.xls\r\n        <app-keenicon name=\"folder-down\" class=\"text-success fs-3\" type=\"outline\"></app-keenicon>\r\n      </button>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;ICiBMA,EADF,CAAAC,cAAA,cAA4D,mBACmC;IAA/DD,EAAA,CAAAE,UAAA,oBAAAC,qEAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAUF,MAAA,CAAAG,cAAA,CAAAN,MAAA,CAAsB;IAAA,EAAC;IAA/DJ,EAAA,CAAAW,YAAA,EAA6F;IAC7FX,EAAA,CAAAC,cAAA,iBAA2F;IAA5BD,EAAA,CAAAE,UAAA,mBAAAU,qEAAA;MAAAZ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAO,YAAA,GAAAb,EAAA,CAAAc,WAAA;MAAA,OAAAd,EAAA,CAAAS,WAAA,CAASI,YAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;IACxFf,EAAA,CAAAgB,SAAA,cAAyF;IACzFhB,EAAA,CAAAiB,MAAA,mCACF;IACFjB,EADE,CAAAW,YAAA,EAAS,EACL;;;;;;IAGJX,EADF,CAAAC,cAAA,cAAuD,iBAEtB;IAA7BD,EAAA,CAAAE,UAAA,mBAAAgB,qEAAA;MAAAlB,EAAA,CAAAK,aAAA,CAAAc,GAAA;MAAA,MAAAZ,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAa,gBAAA,EAAkB;IAAA,EAAC;IAC5BpB,EAAA,CAAAiB,MAAA,kCACA;IAAAjB,EAAA,CAAAgB,SAAA,uBAAyF;IAE7FhB,EADE,CAAAW,YAAA,EAAS,EACL;;;ADvBV,OAAM,MAAOU,4BAA4B;EAC9BC,QAAQ;EACRC,aAAa,GAAW,EAAE;EAC1BC,YAAY;EACZC,kBAAkB;EAE3B,IAAIC,cAAcA,CAAA;IAChB,IAAI,IAAI,CAACH,aAAa,EAAE;MACtB,OAAO,IAAI,CAACA,aAAa;IAC3B;IAEA,QAAQ,IAAI,CAACD,QAAQ;MACnB,KAAK,QAAQ;QACX,OAAO,uEAAuE;MAChF,KAAK,WAAW;QACd,OAAO,oEAAoE;MAC7E;QACE,OAAO,uEAAuE;IAClF;EACF;EAEAZ,cAAcA,CAACiB,KAAU;IACvB,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,IAAI,IAAI,CAACJ,YAAY,EAAE;MAC7B,IAAI,CAACA,YAAY,CAACI,IAAI,CAAC;IACzB;EACF;EAEAR,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACK,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,EAAE;IAC3B;EACF;;qCAhCWJ,4BAA4B;EAAA;;UAA5BA,4BAA4B;IAAAU,SAAA;IAAAC,MAAA;MAAAV,QAAA;MAAAC,aAAA;MAAAC,YAAA;MAAAC,kBAAA;IAAA;IAAAQ,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCPzCtC,EAAA,CAAAC,cAAA,aAC4H;QAG1HD,EAFA,CAAAgB,SAAA,aAC+E,aAEA;QAG3EhB,EAFJ,CAAAC,cAAA,aAAyB,aACqC,cACvC;QAAAD,EAAA,CAAAiB,MAAA,kCAA2B;QAChDjB,EADgD,CAAAW,YAAA,EAAO,EACjD;QAGJX,EADF,CAAAC,cAAA,aAAyD,WACjD;QAAAD,EAAA,CAAAiB,MAAA,GAAoB;QAC5BjB,EAD4B,CAAAW,YAAA,EAAO,EAC7B;QAWNX,EARA,CAAAwC,UAAA,KAAAC,4CAAA,iBAA4D,KAAAC,4CAAA,iBAQL;QAQ3D1C,EADE,CAAAW,YAAA,EAAM,EACF;;;QApBMX,EAAA,CAAA2C,SAAA,GAAoB;QAApB3C,EAAA,CAAA4C,iBAAA,CAAAL,GAAA,CAAAb,cAAA,CAAoB;QAItB1B,EAAA,CAAA2C,SAAA,EAA2B;QAA3B3C,EAAA,CAAA6C,UAAA,SAAAN,GAAA,CAAAjB,QAAA,cAA2B;QAQ3BtB,EAAA,CAAA2C,SAAA,EAA2B;QAA3B3C,EAAA,CAAA6C,UAAA,SAAAN,GAAA,CAAAjB,QAAA,cAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}