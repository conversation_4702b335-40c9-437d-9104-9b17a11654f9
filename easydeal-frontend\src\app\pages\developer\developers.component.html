<div class="mb-5 mt-0">
  <app-broker-title></app-broker-title>
</div>

<div class="card mb-5 mb-xl-10">
  <div class="card-body pt-3 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap">
      <div class="flex-grow-1">
        <div class="d-flex justify-content-between align-items-start flex-wrap">
          <div class="d-flex my-4">
            <h1 class="text-dark-blue fs-2 fw-bolder me-1 mt-3">Developers</h1>
          </div>

          <div class="d-flex my-4">
            <form data-kt-search-element="form" class="w-300px position-relative mb-3" autocomplete="off">
              <app-keenicon name="magnifier"
                class="fs-2 fs-lg-1 text-gray-500 position-absolute top-50 translate-middle-y ms-3"
                type="outline"></app-keenicon>
              <input type="text" class="form-control form-control-flush ps-10 bg-light border rounded-pill"
                name="searchInput" [(ngModel)]="searchInput" placeholder="Search..." data-kt-search-element="input"
                (keyup)="onSearch($event)" />
            </form>
          </div>

          <div class="d-flex h-40px my-4">
            <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bolder flex-nowrap">
              <li class="nav-item">
                <a class="nav-link me-2 pt-0 pb-0 btn" [class.btn-dark-blue]="activeTab === 'all'"
                  [class.text-white]="activeTab === 'all'" [class.btn-light-dark-blue]="activeTab !== 'all'"
                  routerLink="/developer" (click)="setActiveTab('all')">
                  All Developers
                </a>
              </li>

              <li class="nav-item">
                <a class="nav-link me-2 pt-0 pb-0 btn" [class.btn-active-dark-blue]="activeTab === 'contracted'"
                  [class.btn-light-dark-blue]="activeTab !== 'contracted'" routerLink="/developer"
                  (click)="setActiveTab('contracted')">
                  Contracted Developers
                </a>
              </li>

              <li class="nav-item">
                <a class="nav-link me-2 pt-0 pb-0 btn" [class.btn-active-dark-blue]="activeTab === 'not-contracted'"
                  [class.btn-light-dark-blue]="activeTab !== 'not-contracted'" routerLink="/developer"
                  (click)="setActiveTab('not-contracted')">
                  Not Contracted Developers
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <router-outlet></router-outlet>

    <div class="table-responsive mb-5">
      <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5">
        <thead>
          <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1">
            <th class="w-25px ps-4 rounded-start">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input" type="checkbox" value="1" data-kt-check="true"
                  data-kt-check-target=".widget-13-check" />
              </div>
            </th>
            <th class="min-w-150px">
              Developer
            </th>
            <th class="min-w-100px cursor-pointer" (click)="sortData('contract_duration')">
              Contract Duration
              <span class="ms-1 text-primary fw-bold">{{ getSortArrow('contract_duration') }}</span>
            </th>
            <th class="min-w-150px cursor-pointer" (click)="sortData('contract_start_date')">
              Contract Date
              <span class="ms-1 text-primary fw-bold">{{ getSortArrow('contract_start_date') }}</span>
            </th>
            <th class="min-w-150px cursor-pointer" (click)="sortData('contract_end_date')">
              Contract End-Date
              <span class="ms-1 text-primary fw-bold">{{ getSortArrow('contract_end_date') }}</span>
            </th>
            <th class="min-w-100px">
              Status
            </th>
            <th class="min-w-100px text-end rounded-end pe-4">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let row of rows">
            <td class="ps-4">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input widget-13-check" type="checkbox" value="1" />
              </div>
            </td>
            <td>
              <div class="d-flex align-items-center">
                <div class="symbol symbol-45px me-5">
                  <img [src]="row.image" alt="img" class="rounded-circle" />
                </div>
                <div class="d-flex justify-content-start flex-column">
                  <a [routerLink]="['/developer/projects']" [queryParams]="{ developerId: row.developerId }"
                    class="text-gray-900 fw-bold text-hover-dark-blue fs-6">
                    {{ row.fullName }}
                  </a>
                  <span class="text-muted fs-7">{{ row.numberOfProjects }} projects</span>
                </div>
              </div>
            </td>
            <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                {{ row.contractDuration }}
              </span>
            </td>
            <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                {{ row.contractStartDate }}
              </span>
            </td>
            <td>
              <span class="fw-bold badge fs-6 fw-semibold">
                {{ row.contractEndDate }}
              </span>
            </td>
            <td>
              <ng-container [ngSwitch]="getDeveloperStatus(row.brokers)">
                <button *ngSwitchCase="'send contract request'" (click)="openContractModal(contractRequestModal, row)"
                  class="btn btn-sm btn-primary">
                  Send Contract Request
                </button>

                <span *ngSwitchDefault class="fw-bold badge fs-6 fw-semibold fs-4 badge-light-success">
                  Contracted
                </span>
              </ng-container>
            </td>
            <td class="text-end pe-4">
              <div class="dropdown">
                <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
                  data-bs-toggle="dropdown" aria-expanded="false">
                  <i class="fa-solid fa-ellipsis-vertical"></i>
                </button>
                <ul class="dropdown-menu">
                  <li>
                    <a class="dropdown-item" [routerLink]="['/developer/projects']"
                      [queryParams]="{ developerId: row.developerId }">
                      <i class=" fa-solid fa-eye me-2"></i> View Developer
                    </a>
                  </li>
                </ul>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <div class="m-2">
        <app-pagination [totalItems]="page.totalElements" [itemsPerPage]="page.limit" [currentPage]="page.pageNumber"
          (pageChange)="onPageChange($event)">
        </app-pagination>
      </div>
    </div>
  </div>
</div>

<router-outlet></router-outlet>

<!-- Contract Request Modal -->
<ng-template #contractRequestModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">Send Contract Request</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss('closed')"></button>
  </div>
  <div class="modal-body">
    <div class="row">
      <div class="">
        <!-- Document Upload Section -->
        <div class="container p-4 bg-white rounded shadow-sm">
          <!-- Header -->
          <div class="text-center mb-4">
            <h5 class="fw-bold text-primary" style="color: #2e2a7e !important">
              Please Upload The Required Documents
            </h5>
            <p class="text-success mt-2" style="font-size: 12px">
              You can upload the required documents now or skip and upload them
              later. Please note: <br />
              You will not be able to perform any transaction before completing
              the required documents.
            </p>
          </div>

          <!-- Document Upload Cards -->
          <div class="mb-4">
            <!-- Personal Photo -->
            <div class="border border-1 border-primary rounded p-3 mb-3" style="border-style: dashed !important">
              <label for="image" class="d-flex flex-column align-items-center cursor-pointer w-100 m-0">
                <div class="upload-icon mb-2">
                  <i class="fas fa-cloud-upload-alt text-primary" style="font-size: 20px"></i>
                </div>
                <div class="text-center">
                  <p class="mb-1 fw-bold">
                    Profile picture for account

                    <span *ngIf="getFileCount('image') > 0" class="badge bg-success ms-2">
                      {{ getFileCount("image") }}
                    </span>
                  </p>

                  <p class="text-muted small mb-0">
                    <!-- <span
                      *ngIf="getFileCount('personalPhoto') > 0"
                      class="badge bg-success me-1"
                    >
                      {{ getFileCount("personalPhoto") }} files
                    </span>
                    <span *ngIf="getFileCount('personalPhoto') === 0"
                      >Size 12 KB · Max 1M</span> -->

                    <span>Size 12 KB · Max 1M</span>
                  </p>
                </div>
                <input type="file" id="image" class="d-none" (change)="onFileChange($event, 'image')"
                  accept="image/*" />
              </label>
            </div>

            <!-- ID Front -->
            <div class="border border-1 border-primary rounded p-3 mb-3" style="border-style: dashed !important">
              <label for="idFront" class="d-flex flex-column align-items-center cursor-pointer w-100 m-0">
                <div class="upload-icon mb-2">
                  <i class="fas fa-cloud-upload-alt text-primary" style="font-size: 20px"></i>
                </div>
                <div class="text-center">
                  <p class="mb-1 fw-bold">
                    National ID photo from the front
                    <span *ngIf="getFileCount('idFront') > 0" class="badge bg-success ms-2">
                      {{ getFileCount("idFront") }}
                    </span>
                  </p>
                  <p class="text-muted small mb-0">
                    <!-- <span
                      *ngIf="getFileCount('idFront') > 0"
                      class="badge bg-success me-1"
                    >
                      {{ getFileCount("idFront") }} files
                    </span>
                    <span *ngIf="getFileCount('idFront') === 0"
                      >Size 12 KB · Max 1M</span
                    > -->

                    <span>Size 12 KB · Max 1M</span>
                  </p>
                </div>
                <input type="file" id="idFront" class="d-none" (change)="onFileChange($event, 'idFront')"
                  accept="image/*" />
              </label>
            </div>

            <!-- ID Back -->
            <div class="border border-1 border-primary rounded p-3 mb-3" style="border-style: dashed !important">
              <label for="idBack" class="d-flex flex-column align-items-center cursor-pointer w-100 m-0">
                <div class="upload-icon mb-2">
                  <i class="fas fa-cloud-upload-alt text-primary" style="font-size: 20px"></i>
                </div>
                <div class="text-center">
                  <p class="mb-1 fw-bold">
                    National ID card photo from the back
                    <span *ngIf="getFileCount('idBack') > 0" class="badge bg-success ms-2">
                      {{ getFileCount("idBack") }}
                    </span>
                  </p>
                  <p class="text-muted small mb-0">
                    <!-- <span
                      *ngIf="getFileCount('idBack') > 0"
                      class="badge bg-success me-1"
                    >
                      {{ getFileCount("idBack") }} files
                    </span>
                    <span *ngIf="getFileCount('idBack') === 0"
                      >Size 12 KB · Max 1M</span
                    > -->

                    <span>Size 12 KB · Max 1M</span>
                  </p>
                </div>
                <input type="file" id="idBack" class="d-none" (change)="onFileChange($event, 'idBack')"
                  accept="image/*" />
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-light" (click)="modal.dismiss('Cancel')">
      Cancel
    </button>
    <button type="button" class="btn btn-dark-blue" [disabled]="isSendingRequest || !areAllFilesUploaded()"
      (click)="sendContractRequest()">
      Send Request
      <span *ngIf="isSendingRequest" class="spinner-border spinner-border-sm align-middle ms-2"></span>
    </button>
  </div>
</ng-template>
