{"ast": null, "code": "import { Validators, ReactiveFormsModule } from '@angular/forms';\nimport { DeveloperDashboardModule } from '../developer-dashboard/developer-dashboard.module';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../services/projects.service\";\nimport * as i4 from \"../developer-dashboard/components/header/header.component\";\nimport * as i5 from \"@angular/common\";\nfunction UpdatePropertyComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h2\")(2, \"span\", 20);\n    i0.ɵɵtext(3, \"Update Project - \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 21);\n    i0.ɵɵtext(5, \"Basic Data\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction UpdatePropertyComponent_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h2\")(2, \"span\", 20);\n    i0.ɵɵtext(3, \"Update Project - \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 21);\n    i0.ɵɵtext(5, \"Location \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction UpdatePropertyComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h2\")(2, \"span\", 20);\n    i0.ɵɵtext(3, \"Update Project - \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 21);\n    i0.ɵɵtext(5, \" Project type\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction UpdatePropertyComponent_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h2\")(2, \"span\", 20);\n    i0.ɵɵtext(3, \"Update Project - \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 21);\n    i0.ɵɵtext(5, \"Project Documents\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction UpdatePropertyComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵlistener(\"click\", function UpdatePropertyComponent_div_22_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.prevStep());\n    });\n    i0.ɵɵtext(1, \" Back to previous step \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UpdatePropertyComponent_div_26_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldErrorMessage(\"name\"), \" \");\n  }\n}\nfunction UpdatePropertyComponent_div_26_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldErrorMessage(\"designer\"), \" \");\n  }\n}\nfunction UpdatePropertyComponent_div_26_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldErrorMessage(\"projectExecutor\"), \" \");\n  }\n}\nfunction UpdatePropertyComponent_div_26_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldErrorMessage(\"managementTeam\"), \" \");\n  }\n}\nfunction UpdatePropertyComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"label\", 25);\n    i0.ɵɵtext(3, \"Project name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 26);\n    i0.ɵɵtemplate(5, UpdatePropertyComponent_div_26_div_5_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 24)(7, \"label\", 25);\n    i0.ɵɵtext(8, \" Project Designer \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"input\", 28);\n    i0.ɵɵtemplate(10, UpdatePropertyComponent_div_26_div_10_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 24)(12, \"label\", 25);\n    i0.ɵɵtext(13, \" Project implementer \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 29);\n    i0.ɵɵtemplate(15, UpdatePropertyComponent_div_26_div_15_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 24)(17, \"label\", 25);\n    i0.ɵɵtext(18, \" Project management \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"input\", 30);\n    i0.ɵɵtemplate(20, UpdatePropertyComponent_div_26_div_20_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.step1Form);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.hasFieldError(\"name\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasFieldError(\"name\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.hasFieldError(\"designer\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasFieldError(\"designer\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.hasFieldError(\"projectExecutor\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasFieldError(\"projectExecutor\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.hasFieldError(\"managementTeam\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasFieldError(\"managementTeam\"));\n  }\n}\nfunction UpdatePropertyComponent_div_27_li_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 44);\n    i0.ɵɵlistener(\"click\", function UpdatePropertyComponent_div_27_li_10_Template_a_click_1_listener() {\n      const city_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectCity(city_r4.id, city_r4.name_en || city_r4.name));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const city_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", city_r4.name_en || city_r4.name, \" \");\n  }\n}\nfunction UpdatePropertyComponent_div_27_li_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 44);\n    i0.ɵɵlistener(\"click\", function UpdatePropertyComponent_div_27_li_20_Template_a_click_1_listener() {\n      const area_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectArea(area_r6.id, area_r6.name_en || area_r6.name));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const area_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", area_r6.name_en || area_r6.name, \" \");\n  }\n}\nfunction UpdatePropertyComponent_div_27_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldErrorMessage(\"address\"), \" \");\n  }\n}\nfunction UpdatePropertyComponent_div_27_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldErrorMessage(\"googleMapsLink\"), \" \");\n  }\n}\nfunction UpdatePropertyComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"label\", 25);\n    i0.ɵɵtext(3, \" City\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 32)(5, \"button\", 33)(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"ul\", 35);\n    i0.ɵɵtemplate(10, UpdatePropertyComponent_div_27_li_10_Template, 3, 1, \"li\", 36);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 24)(12, \"label\", 25);\n    i0.ɵɵtext(13, \"Area\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 32)(15, \"button\", 37)(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"i\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"ul\", 38);\n    i0.ɵɵtemplate(20, UpdatePropertyComponent_div_27_li_20_Template, 3, 1, \"li\", 36);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 24)(22, \"label\", 25);\n    i0.ɵɵtext(23, \" Details address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(24, \"input\", 39);\n    i0.ɵɵtemplate(25, UpdatePropertyComponent_div_27_div_25_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 24)(27, \"label\", 25);\n    i0.ɵɵtext(28, \"Website link on Google Maps\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(29, \"input\", 40);\n    i0.ɵɵtemplate(30, UpdatePropertyComponent_div_27_div_30_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(31, \"input\", 41)(32, \"input\", 42)(33, \"input\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.step2Form);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedCityName || \"Choose the city\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.cities);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedAreaName || \"Choose the Area\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.areas);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.hasFieldError(\"address\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasFieldError(\"address\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.hasFieldError(\"googleMapsLink\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasFieldError(\"googleMapsLink\"));\n  }\n}\nfunction UpdatePropertyComponent_div_28_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldErrorMessage(\"buildingsCount\"), \" \");\n  }\n}\nfunction UpdatePropertyComponent_div_28_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldErrorMessage(\"apartmentsCount\"), \" \");\n  }\n}\nfunction UpdatePropertyComponent_div_28_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldErrorMessage(\"villasCount\"), \" \");\n  }\n}\nfunction UpdatePropertyComponent_div_28_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldErrorMessage(\"duplexCount\"), \" \");\n  }\n}\nfunction UpdatePropertyComponent_div_28_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldErrorMessage(\"administrativeUnitsCount\"), \" \");\n  }\n}\nfunction UpdatePropertyComponent_div_28_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldErrorMessage(\"commercialUnitsCount\"), \" \");\n  }\n}\nfunction UpdatePropertyComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"label\", 25);\n    i0.ɵɵtext(3, \" Project type \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 32)(5, \"button\", 45)(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"ul\", 46)(10, \"li\")(11, \"a\", 44);\n    i0.ɵɵlistener(\"click\", function UpdatePropertyComponent_div_28_Template_a_click_11_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectProjectType(\" residential\"));\n    });\n    i0.ɵɵtext(12, \" residential\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"li\")(14, \"a\", 44);\n    i0.ɵɵlistener(\"click\", function UpdatePropertyComponent_div_28_Template_a_click_14_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectProjectType(\"commercial\"));\n    });\n    i0.ɵɵtext(15, \"commercial\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"li\")(17, \"a\", 44);\n    i0.ɵɵlistener(\"click\", function UpdatePropertyComponent_div_28_Template_a_click_17_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectProjectType(\"mixed\"));\n    });\n    i0.ɵɵtext(18, \"mixed\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(19, \"div\", 24)(20, \"label\", 25);\n    i0.ɵɵtext(21, \" Number of project units\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 47)(23, \"button\", 48);\n    i0.ɵɵtext(24, \" Buildings \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 49);\n    i0.ɵɵelement(26, \"input\", 50);\n    i0.ɵɵtemplate(27, UpdatePropertyComponent_div_28_div_27_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 47)(29, \"button\", 48);\n    i0.ɵɵtext(30, \" Apartments \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 49);\n    i0.ɵɵelement(32, \"input\", 51);\n    i0.ɵɵtemplate(33, UpdatePropertyComponent_div_28_div_33_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 47)(35, \"button\", 48);\n    i0.ɵɵtext(36, \" Villas \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 49);\n    i0.ɵɵelement(38, \"input\", 52);\n    i0.ɵɵtemplate(39, UpdatePropertyComponent_div_28_div_39_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 47)(41, \"button\", 48);\n    i0.ɵɵtext(42, \" Duplexes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 49);\n    i0.ɵɵelement(44, \"input\", 53);\n    i0.ɵɵtemplate(45, UpdatePropertyComponent_div_28_div_45_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 47)(47, \"button\", 48);\n    i0.ɵɵtext(48, \" Administrative Units \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 49);\n    i0.ɵɵelement(50, \"input\", 54);\n    i0.ɵɵtemplate(51, UpdatePropertyComponent_div_28_div_51_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(52, \"div\", 47)(53, \"button\", 48);\n    i0.ɵɵtext(54, \" Commercial Units \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"div\", 49);\n    i0.ɵɵelement(56, \"input\", 55);\n    i0.ɵɵtemplate(57, UpdatePropertyComponent_div_28_div_57_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.step3Form);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(((tmp_2_0 = ctx_r1.step3Form.get(\"projectType\")) == null ? null : tmp_2_0.value) || \"commercial...\");\n    i0.ɵɵadvance(19);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.hasFieldError(\"buildingsCount\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasFieldError(\"buildingsCount\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.hasFieldError(\"apartmentsCount\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasFieldError(\"apartmentsCount\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.hasFieldError(\"villasCount\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasFieldError(\"villasCount\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.hasFieldError(\"duplexCount\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasFieldError(\"duplexCount\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.hasFieldError(\"administrativeUnitsCount\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasFieldError(\"administrativeUnitsCount\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.hasFieldError(\"commercialUnitsCount\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasFieldError(\"commercialUnitsCount\"));\n  }\n}\nfunction UpdatePropertyComponent_div_29_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵtext(1, \" 1 existing \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UpdatePropertyComponent_div_29_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileCount(\"logoImage\"), \" new \");\n  }\n}\nfunction UpdatePropertyComponent_div_29_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"p\", 77);\n    i0.ɵɵtext(2, \"Current Logo:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 78);\n    i0.ɵɵelement(4, \"img\", 79);\n    i0.ɵɵelementStart(5, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function UpdatePropertyComponent_div_29_div_11_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeImage(\"logoImage\"));\n    });\n    i0.ɵɵelement(6, \"i\", 81);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.existingFiles.logoImage, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction UpdatePropertyComponent_div_29_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵtext(1, \" 1 existing \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UpdatePropertyComponent_div_29_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileCount(\"coverImage\"), \" new \");\n  }\n}\nfunction UpdatePropertyComponent_div_29_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"p\", 77);\n    i0.ɵɵtext(2, \"Current Cover Image:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 78);\n    i0.ɵɵelement(4, \"img\", 82);\n    i0.ɵɵelementStart(5, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function UpdatePropertyComponent_div_29_div_21_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeImage(\"coverImage\"));\n    });\n    i0.ɵɵelement(6, \"i\", 81);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.existingFiles.coverImage, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction UpdatePropertyComponent_div_29_span_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵtext(1, \" 1 existing \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UpdatePropertyComponent_div_29_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileCount(\"masterPlan\"), \" new \");\n  }\n}\nfunction UpdatePropertyComponent_div_29_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"p\", 77);\n    i0.ɵɵtext(2, \"Current Master Plan:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 78);\n    i0.ɵɵelement(4, \"img\", 83);\n    i0.ɵɵelementStart(5, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function UpdatePropertyComponent_div_29_div_31_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeImage(\"masterPlan\"));\n    });\n    i0.ɵɵelement(6, \"i\", 81);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.existingFiles.masterPlan, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction UpdatePropertyComponent_div_29_span_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.existingFiles.galleryImages.length, \" existing \");\n  }\n}\nfunction UpdatePropertyComponent_div_29_span_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileCount(\"gallery\"), \" new \");\n  }\n}\nfunction UpdatePropertyComponent_div_29_div_41_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87);\n    i0.ɵɵelement(2, \"img\", 88);\n    i0.ɵɵelementStart(3, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function UpdatePropertyComponent_div_29_div_41_div_4_Template_button_click_3_listener() {\n      const i_r13 = i0.ɵɵrestoreView(_r12).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.removeGalleryItem(\"image\", i_r13));\n    });\n    i0.ɵɵelement(4, \"i\", 81);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const image_r14 = ctx.$implicit;\n    const i_r13 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", image_r14.url, i0.ɵɵsanitizeUrl)(\"alt\", \"Gallery Image \" + (i_r13 + 1));\n  }\n}\nfunction UpdatePropertyComponent_div_29_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"p\", 77);\n    i0.ɵɵtext(2, \"Current Gallery Images:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 84);\n    i0.ɵɵtemplate(4, UpdatePropertyComponent_div_29_div_41_div_4_Template, 5, 2, \"div\", 85);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.existingFiles.galleryImages);\n  }\n}\nfunction UpdatePropertyComponent_div_29_span_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.existingFiles.galleryVideos.length, \" existing \");\n  }\n}\nfunction UpdatePropertyComponent_div_29_span_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileCount(\"videos\"), \" new \");\n  }\n}\nfunction UpdatePropertyComponent_div_29_div_51_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87)(2, \"div\", 89);\n    i0.ɵɵelement(3, \"i\", 90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function UpdatePropertyComponent_div_29_div_51_div_4_Template_button_click_4_listener() {\n      const i_r16 = i0.ɵɵrestoreView(_r15).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.removeGalleryItem(\"video\", i_r16));\n    });\n    i0.ɵɵelement(5, \"i\", 81);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction UpdatePropertyComponent_div_29_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"p\", 77);\n    i0.ɵɵtext(2, \"Current Gallery Videos:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 84);\n    i0.ɵɵtemplate(4, UpdatePropertyComponent_div_29_div_51_div_4_Template, 6, 0, \"div\", 85);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.existingFiles.galleryVideos);\n  }\n}\nfunction UpdatePropertyComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 56)(2, \"div\", 57)(3, \"label\", 58)(4, \"div\", 59);\n    i0.ɵɵelement(5, \"i\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 61);\n    i0.ɵɵtext(7, \" upload project logo \");\n    i0.ɵɵtemplate(8, UpdatePropertyComponent_div_29_span_8_Template, 2, 0, \"span\", 62)(9, UpdatePropertyComponent_div_29_span_9_Template, 2, 1, \"span\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 64);\n    i0.ɵɵlistener(\"change\", function UpdatePropertyComponent_div_29_Template_input_change_10_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event, \"logoImage\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, UpdatePropertyComponent_div_29_div_11_Template, 7, 1, \"div\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 57)(13, \"label\", 66)(14, \"div\", 59);\n    i0.ɵɵelement(15, \"i\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 61);\n    i0.ɵɵtext(17, \" upload project layout \");\n    i0.ɵɵtemplate(18, UpdatePropertyComponent_div_29_span_18_Template, 2, 0, \"span\", 62)(19, UpdatePropertyComponent_div_29_span_19_Template, 2, 1, \"span\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"input\", 67);\n    i0.ɵɵlistener(\"change\", function UpdatePropertyComponent_div_29_Template_input_change_20_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event, \"coverImage\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(21, UpdatePropertyComponent_div_29_div_21_Template, 7, 1, \"div\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 57)(23, \"label\", 68)(24, \"div\", 59);\n    i0.ɵɵelement(25, \"i\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 61);\n    i0.ɵɵtext(27, \" upload project licenses \");\n    i0.ɵɵtemplate(28, UpdatePropertyComponent_div_29_span_28_Template, 2, 0, \"span\", 62)(29, UpdatePropertyComponent_div_29_span_29_Template, 2, 1, \"span\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"input\", 69);\n    i0.ɵɵlistener(\"change\", function UpdatePropertyComponent_div_29_Template_input_change_30_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event, \"masterPlan\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(31, UpdatePropertyComponent_div_29_div_31_Template, 7, 1, \"div\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 57)(33, \"label\", 70)(34, \"div\", 59);\n    i0.ɵɵelement(35, \"i\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"span\", 61);\n    i0.ɵɵtext(37, \" upload project images \");\n    i0.ɵɵtemplate(38, UpdatePropertyComponent_div_29_span_38_Template, 2, 1, \"span\", 62)(39, UpdatePropertyComponent_div_29_span_39_Template, 2, 1, \"span\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"input\", 71);\n    i0.ɵɵlistener(\"change\", function UpdatePropertyComponent_div_29_Template_input_change_40_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event, \"gallery\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(41, UpdatePropertyComponent_div_29_div_41_Template, 5, 1, \"div\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 57)(43, \"label\", 72)(44, \"div\", 59);\n    i0.ɵɵelement(45, \"i\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"span\", 61);\n    i0.ɵɵtext(47, \" upload project videos \");\n    i0.ɵɵtemplate(48, UpdatePropertyComponent_div_29_span_48_Template, 2, 1, \"span\", 62)(49, UpdatePropertyComponent_div_29_span_49_Template, 2, 1, \"span\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"input\", 73);\n    i0.ɵɵlistener(\"change\", function UpdatePropertyComponent_div_29_Template_input_change_50_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event, \"videos\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(51, UpdatePropertyComponent_div_29_div_51_Template, 5, 1, \"div\", 65);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.step4Form);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.existingFiles.logoImage);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileCount(\"logoImage\") > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.existingFiles == null ? null : ctx_r1.existingFiles.logoImage);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.existingFiles.coverImage);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileCount(\"coverImage\") > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.existingFiles.coverImage);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.existingFiles.masterPlan);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileCount(\"masterPlan\") > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.existingFiles.masterPlan);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.existingFiles.galleryImages == null ? null : ctx_r1.existingFiles.galleryImages.length) > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileCount(\"gallery\") > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.existingFiles.galleryImages == null ? null : ctx_r1.existingFiles.galleryImages.length) > 0);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.existingFiles.galleryVideos == null ? null : ctx_r1.existingFiles.galleryVideos.length) > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileCount(\"videos\") > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.existingFiles.galleryVideos == null ? null : ctx_r1.existingFiles.galleryVideos.length) > 0);\n  }\n}\nfunction UpdatePropertyComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 91)(1, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function UpdatePropertyComponent_div_30_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.cancel());\n    });\n    i0.ɵɵtext(2, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function UpdatePropertyComponent_div_30_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵelementStart(4, \"span\", 94);\n    i0.ɵɵtext(5, \" Next - Location Information \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isCurrentFormValid());\n  }\n}\nfunction UpdatePropertyComponent_div_31_ng_container_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" Next - Project type \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction UpdatePropertyComponent_div_31_ng_container_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" Next - Project Documents \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction UpdatePropertyComponent_div_31_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function UpdatePropertyComponent_div_31_ng_container_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵelementStart(2, \"span\", 94);\n    i0.ɵɵtemplate(3, UpdatePropertyComponent_div_31_ng_container_1_ng_container_3_Template, 2, 0, \"ng-container\", 8)(4, UpdatePropertyComponent_div_31_ng_container_1_ng_container_4_Template, 2, 0, \"ng-container\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isCurrentFormValid());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStep === 2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStep === 3);\n  }\n}\nfunction UpdatePropertyComponent_div_31_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 96)(2, \"button\", 97);\n    i0.ɵɵlistener(\"click\", function UpdatePropertyComponent_div_31_ng_container_2_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.submitForm());\n    });\n    i0.ɵɵelementStart(3, \"span\", 94);\n    i0.ɵɵtext(4, \" Update Project \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isCurrentFormValid());\n  }\n}\nfunction UpdatePropertyComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 95);\n    i0.ɵɵtemplate(1, UpdatePropertyComponent_div_31_ng_container_1_Template, 5, 3, \"ng-container\", 8)(2, UpdatePropertyComponent_div_31_ng_container_2_Template, 5, 1, \"ng-container\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStep !== ctx_r1.totalSteps);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStep === ctx_r1.totalSteps);\n  }\n}\nexport class UpdatePropertyComponent {\n  fb;\n  router;\n  route;\n  projectsService;\n  cdr;\n  totalSteps = 4;\n  currentStep = 1;\n  projectId = null;\n  existingFiles = {};\n  step1Form;\n  step2Form;\n  step3Form;\n  step4Form;\n  cities = [];\n  areas = [];\n  developerId;\n  selectedCityId = null;\n  selectedCityName = '';\n  selectedAreaName = '';\n  constructor(fb, router, route, projectsService, cdr) {\n    this.fb = fb;\n    this.router = router;\n    this.route = route;\n    this.projectsService = projectsService;\n    this.cdr = cdr;\n  }\n  ngOnInit() {\n    this.initForms();\n    this.loadCities();\n    const userJson = localStorage.getItem('currentUser');\n    let user = userJson ? JSON.parse(userJson) : null;\n    this.developerId = user?.developerId;\n    this.route.queryParams.subscribe(params => {\n      if (params['id']) {\n        this.projectId = +params['id'];\n        this.loadProjectData();\n      } else {\n        this.router.navigate(['/developer/projects']);\n      }\n    });\n  }\n  initForms() {\n    // Step 1: Basic Property Settings\n    this.step1Form = this.fb.group({\n      name: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(100)]],\n      designer: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n      projectExecutor: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n      managementTeam: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]]\n    });\n    // Step 2: Location Information\n    this.step2Form = this.fb.group({\n      cityId: ['', Validators.required],\n      areaId: ['', Validators.required],\n      address: ['', [Validators.required, Validators.minLength(10), Validators.maxLength(200)]],\n      googleMapsLink: ['', [Validators.pattern('https?://.+')]],\n      googleMapUrl: ['', [Validators.pattern('https?://.+')]]\n    });\n    // Step 3: Project type\n    this.step3Form = this.fb.group({\n      projectType: ['', Validators.required],\n      buildingsCount: ['', [Validators.required, Validators.min(0), Validators.pattern('^[0-9]+$')]],\n      apartmentsCount: ['', [Validators.required, Validators.min(0), Validators.pattern('^[0-9]+$')]],\n      villasCount: ['', [Validators.required, Validators.min(0), Validators.pattern('^[0-9]+$')]],\n      duplexCount: ['', [Validators.required, Validators.min(0), Validators.pattern('^[0-9]+$')]],\n      administrativeUnitsCount: ['', [Validators.required, Validators.min(0), Validators.pattern('^[0-9]+$')]],\n      commercialUnitsCount: ['', [Validators.required, Validators.min(0), Validators.pattern('^[0-9]+$')]]\n    });\n    // Step 4: Project Documents\n    this.step4Form = this.fb.group({\n      logoImage: [[]],\n      coverImage: [[]],\n      masterPlan: [[]],\n      gallery: [[]],\n      videos: [[]]\n    });\n  }\n  getCurrentForm() {\n    switch (this.currentStep) {\n      case 1:\n        return this.step1Form;\n      case 2:\n        return this.step2Form;\n      case 3:\n        return this.step3Form;\n      case 4:\n        return this.step4Form;\n      default:\n        return this.step1Form;\n    }\n  }\n  isCurrentFormValid() {\n    return this.getCurrentForm().valid;\n  }\n  hasFieldError(fieldName) {\n    const form = this.getCurrentForm();\n    const field = form.get(fieldName);\n    return field ? field.invalid && (field.dirty || field.touched) : false;\n  }\n  getFieldErrorMessage(fieldName) {\n    const field = this.getCurrentForm().get(fieldName);\n    if (!field?.errors) return '';\n    if (field.errors['required']) return 'Required';\n    if (field.errors['minlength']) return 'Too short';\n    if (field.errors['maxlength']) return 'Too long';\n    if (field.errors['min']) return 'Invalid number';\n    if (field.errors['pattern']) return 'Invalid format';\n    return 'Invalid';\n  }\n  markCurrentFormAsTouched() {\n    const form = this.getCurrentForm();\n    Object.keys(form.controls).forEach(key => {\n      form.get(key)?.markAsTouched();\n    });\n  }\n  areAllFormsValid() {\n    return this.step1Form.valid && this.step2Form.valid && this.step3Form.valid;\n  }\n  nextStep() {\n    this.markCurrentFormAsTouched();\n    if (!this.isCurrentFormValid()) {\n      return;\n    }\n    if (this.currentStep < this.totalSteps) {\n      this.currentStep++;\n    }\n  }\n  prevStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  loadCities() {\n    this.projectsService.getCities().subscribe({\n      next: response => {\n        console.log('Cities response:', response);\n        if (response && response.data && Array.isArray(response.data)) {\n          this.cities = response.data;\n        } else if (response && Array.isArray(response)) {\n          this.cities = response;\n        } else if (response && response.cities) {\n          this.cities = response.cities;\n        } else {\n          console.warn('No cities data in response:', response);\n          this.cities = [];\n        }\n      },\n      error: err => {\n        console.error('Error loading cities:', err);\n      },\n      complete: () => {\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  loadAreas(cityId) {\n    this.projectsService.getAreas(cityId).subscribe({\n      next: response => {\n        if (response && response.data && Array.isArray(response.data)) {\n          this.areas = response.data;\n        } else if (response && Array.isArray(response)) {\n          this.areas = response;\n        } else if (response && response.areas) {\n          this.areas = response.areas;\n        } else {\n          this.areas = [];\n        }\n      },\n      error: err => {\n        console.error('Error loading areas:', err);\n        this.areas = [];\n      },\n      complete: () => {\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  selectCity(cityId, cityName) {\n    this.selectedCityId = cityId;\n    this.selectedCityName = cityName;\n    this.step2Form.patchValue({\n      cityId: cityId\n    });\n    this.loadAreas(cityId);\n  }\n  selectArea(areaId, areaName) {\n    this.selectedAreaName = areaName;\n    this.step2Form.patchValue({\n      areaId: areaId\n    });\n  }\n  selectProjectType(type) {\n    this.step3Form.patchValue({\n      projectType: type\n    });\n  }\n  submitForm() {\n    this.markCurrentFormAsTouched();\n    if (this.areAllFormsValid()) {\n      const httpFormData = new FormData();\n      Object.keys(this.step1Form.value).forEach(key => {\n        httpFormData.append(key, this.step1Form.value[key]);\n      });\n      Object.keys(this.step2Form.value).forEach(key => {\n        if (this.step2Form.value[key] !== null && this.step2Form.value[key] !== '') {\n          httpFormData.append(key, this.step2Form.value[key]);\n        }\n      });\n      Object.keys(this.step3Form.value).forEach(key => {\n        httpFormData.append(key, this.step3Form.value[key]);\n      });\n      const fileFields = ['logoImage', 'coverImage', 'masterPlan', 'gallery', 'videos'];\n      fileFields.forEach(field => {\n        const files = this.step4Form.get(field)?.value;\n        const isMultiple = ['gallery', 'videos'].includes(field);\n        if (isMultiple && Array.isArray(files) && files.length > 0) {\n          files.forEach(file => {\n            if (file instanceof File) {\n              httpFormData.append(`${field}[]`, file);\n            }\n          });\n        } else if (!isMultiple && files[0] instanceof File) {\n          httpFormData.append(field, files[0]);\n        }\n      });\n      httpFormData.append('developerId', this.developerId);\n      if (this.projectId !== null) {\n        this.projectsService.updateProject(this.projectId, httpFormData).subscribe({\n          next: response => {\n            console.log('Project updated successfully:', response);\n            this.router.navigate(['/developer/projects']);\n          },\n          error: error => {\n            console.error('Error updating project:', error);\n          }\n        });\n      } else {\n        console.error('Project ID is null');\n      }\n    }\n  }\n  cancel() {\n    this.router.navigate(['/developer/projects']);\n  }\n  onFileChange(event, fieldName) {\n    if (event.target.files && event.target.files.length) {\n      const files = Array.from(event.target.files);\n      this.step4Form.patchValue({\n        [fieldName]: files\n      });\n      console.log(`${fieldName}: ${files.length} files selected`);\n    }\n  }\n  getFileCount(fieldName) {\n    const files = this.step4Form.get(fieldName)?.value;\n    return files && Array.isArray(files) ? files.length : 0;\n  }\n  loadProjectData() {\n    if (this.projectId) {\n      this.projectsService.getById(this.projectId).subscribe({\n        next: response => {\n          const data = response.data || response;\n          console.log('Project data:', data);\n          // Simple direct assignment\n          this.existingFiles = {\n            logoImage: data.logoImage || null,\n            coverImage: data.coverImage || null,\n            masterPlan: data.masterPlan || null,\n            galleryImages: data.gallery?.filter(item => item.type === 'image') || [],\n            galleryVideos: data.gallery?.filter(item => item.type === 'video') || []\n          };\n          setTimeout(() => {\n            this.step1Form.patchValue({\n              name: data.name || '',\n              designer: data.designer || '',\n              projectExecutor: data.projectExecutor || '',\n              managementTeam: data.managementTeam || ''\n            });\n            // Populate Step 2 form with existing data\n            this.step2Form.patchValue({\n              cityId: data.city?.id || data.cityId || '',\n              areaId: data.area?.id || data.areaId || '',\n              address: data.address || '',\n              googleMapsLink: data.googleMapUrl || '',\n              googleMapUrl: data.googleMapUrl || ''\n            });\n            // Set selected values for display\n            if (data.city) {\n              this.selectedCityId = data.city.id || data.cityId;\n              this.selectedCityName = data.city.name_en || data.city.name || '';\n              if (this.selectedCityId) {\n                this.loadAreas(this.selectedCityId);\n              }\n            }\n            if (data.area) {\n              this.selectedAreaName = data.area.name_en || data.area.name || '';\n            }\n            // Populate Step 3 form with existing data\n            this.step3Form.patchValue({\n              projectType: data.projectType || '',\n              buildingsCount: data.buildingsCount || 0,\n              apartmentsCount: data.apartmentsCount || 0,\n              villasCount: data.villasCount || 0,\n              duplexCount: data.duplexCount || 0,\n              administrativeUnitsCount: data.administrativeUnitsCount || 0,\n              commercialUnitsCount: data.commercialUnitsCount || 0\n            });\n            this.cdr.detectChanges();\n          }, 100);\n        },\n        error: error => {\n          console.error('Error loading project data:', error);\n          alert('Failed to load project data. Please try again later.');\n        }\n      });\n    }\n  }\n  removeImage(fieldName) {\n    this.existingFiles[fieldName] = null;\n    this.cdr.detectChanges();\n  }\n  removeGalleryItem(type, index) {\n    if (type === 'image') {\n      this.existingFiles.galleryImages.splice(index, 1);\n    } else if (type === 'video') {\n      this.existingFiles.galleryVideos.splice(index, 1);\n    }\n    this.cdr.detectChanges();\n  }\n  static ɵfac = function UpdatePropertyComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || UpdatePropertyComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ProjectsService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: UpdatePropertyComponent,\n    selectors: [[\"app-update-property\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 32,\n    vars: 17,\n    consts: [[1, \"mb-20\", \"mt-0\"], [3, \"title\", \"subtitle\"], [1, \"card\", \"rounded-4\"], [1, \"card-body\", \"p-10\"], [\"id\", \"update_property_stepper\", 1, \"stepper\", \"stepper-pills\", \"d-flex\", \"flex-column\"], [1, \"alert\", \"alert-warning\", \"text-center\", \"mb-4\"], [1, \"fas\", \"fa-edit\", \"me-2\"], [1, \"mb-5\", \"text-center\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"mb-2\"], [1, \"text-success\", \"fw-bold\"], [1, \"text-muted\", \"mx-1\"], [1, \"text-muted\"], [\"class\", \"text-primary cursor-pointer mb-2\", 3, \"click\", 4, \"ngIf\"], [1, \"progress\", \"h-8px\", \"bg-light-success\", \"w-75\", \"mx-auto\"], [\"role\", \"progressbar\", \"aria-valuenow\", \"50\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", 1, \"progress-bar\", \"bg-success\"], [1, \"mx-auto\", \"w-100\", \"pt-5\", \"pb-10\"], [3, \"formGroup\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-between pt-10\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center pt-10\", 4, \"ngIf\"], [1, \"text-dark-blue\", \"fw-bold\"], [1, \"text-dark-blue\", \"fw-normal\"], [1, \"text-primary\", \"cursor-pointer\", \"mb-2\", 3, \"click\"], [3, \"formGroup\"], [1, \"mb-10\"], [1, \"form-label\", \"fw-bold\", \"text-start\", \"d-block\"], [\"type\", \"text\", \"formControlName\", \"name\", 1, \"form-control\", \"text-start\"], [\"class\", \"invalid-feedback d-block\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"designer\", 1, \"form-control\", \"text-start\"], [\"type\", \"text\", \"formControlName\", \"projectExecutor\", 1, \"form-control\", \"text-start\"], [\"type\", \"text\", \"formControlName\", \"managementTeam\", 1, \"form-control\", \"text-start\"], [1, \"invalid-feedback\", \"d-block\"], [1, \"dropdown\"], [\"type\", \"button\", \"id\", \"cityDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"fas\", \"fa-chevron-down\"], [\"aria-labelledby\", \"cityDropdown\", 1, \"dropdown-menu\", \"w-100\", 2, \"max-height\", \"300px\", \"overflow-y\", \"auto\"], [4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"id\", \"areaDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"aria-labelledby\", \"areaDropdown\", 1, \"dropdown-menu\", \"w-100\", 2, \"max-height\", \"300px\", \"overflow-y\", \"auto\"], [\"type\", \"text\", \"formControlName\", \"address\", \"placeholder\", \" Enter the address in details\", 1, \"form-control\", \"text-start\"], [\"type\", \"text\", \"formControlName\", \"googleMapsLink\", \"placeholder\", \" Enter the map link\", 1, \"form-control\", \"text-start\"], [\"type\", \"hidden\", \"formControlName\", \"cityId\"], [\"type\", \"hidden\", \"formControlName\", \"areaId\"], [\"type\", \"hidden\", \"formControlName\", \"googleMapUrl\"], [1, \"dropdown-item\", \"text-start\", 3, \"click\"], [\"type\", \"button\", \"id\", \"projectTypeDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"aria-labelledby\", \"projectTypeDropdown\", 1, \"dropdown-menu\", \"w-100\", 2, \"max-height\", \"300px\", \"overflow-y\", \"auto\"], [1, \"d-flex\", \"mb-3\", \"align-items-center\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"me-3\", 2, \"width\", \"150px\", \"background-color\", \"#1e1e7c\"], [1, \"flex-grow-1\"], [\"type\", \"number\", \"formControlName\", \"buildingsCount\", \"placeholder\", \"00\", 1, \"form-control\", \"text-start\"], [\"type\", \"number\", \"formControlName\", \"apartmentsCount\", \"placeholder\", \"00\", 1, \"form-control\", \"text-start\"], [\"type\", \"number\", \"formControlName\", \"villasCount\", \"placeholder\", \"00\", 1, \"form-control\", \"text-start\"], [\"type\", \"number\", \"formControlName\", \"duplexCount\", \"placeholder\", \"00\", 1, \"form-control\", \"text-start\"], [\"type\", \"number\", \"formControlName\", \"administrativeUnitsCount\", \"placeholder\", \"00\", 1, \"form-control\", \"text-start\"], [\"type\", \"number\", \"formControlName\", \"commercialUnitsCount\", \"placeholder\", \"00\", 1, \"form-control\", \"text-start\"], [1, \"mb-10\", \"upload-card-container\"], [1, \"card\", \"mb-5\", \"cursor-pointer\"], [\"for\", \"projectLogo\", 1, \"card-body\", \"text-center\", \"py-3\"], [1, \"upload-icon\"], [1, \"fas\", \"fa-arrow-up\"], [1, \"upload-text\"], [\"class\", \"badge bg-info ms-2\", \"title\", \"Existing files\", 4, \"ngIf\"], [\"class\", \"badge bg-success ms-2\", \"title\", \"New files selected\", 4, \"ngIf\"], [\"type\", \"file\", \"id\", \"projectLogo\", \"accept\", \"image/*\", 1, \"d-none\", 3, \"change\"], [\"class\", \"mt-3\", 4, \"ngIf\"], [\"for\", \"projectLayout\", 1, \"card-body\", \"text-center\", \"py-3\"], [\"type\", \"file\", \"id\", \"projectLayout\", \"accept\", \"image/*\", 1, \"d-none\", 3, \"change\"], [\"for\", \"projectLicenses\", 1, \"card-body\", \"text-center\", \"py-3\"], [\"type\", \"file\", \"id\", \"projectLicenses\", \"accept\", \"image/*\", 1, \"d-none\", 3, \"change\"], [\"for\", \"projectImages\", 1, \"card-body\", \"text-center\", \"py-3\"], [\"type\", \"file\", \"id\", \"projectImages\", \"accept\", \"image/*\", \"multiple\", \"\", 1, \"d-none\", 3, \"change\"], [\"for\", \"projectVideos\", 1, \"card-body\", \"text-center\", \"py-3\"], [\"type\", \"file\", \"id\", \"projectVideos\", \"accept\", \"video/*\", \"multiple\", \"\", 1, \"d-none\", 3, \"change\"], [\"title\", \"Existing files\", 1, \"badge\", \"bg-info\", \"ms-2\"], [\"title\", \"New files selected\", 1, \"badge\", \"bg-success\", \"ms-2\"], [1, \"mt-3\"], [1, \"text-muted\", \"mb-2\"], [1, \"position-relative\", \"d-inline-block\"], [\"alt\", \"Current Logo\", 1, \"img-fluid\", \"rounded\", 2, \"height\", \"100px\", \"width\", \"auto\", \"max-width\", \"200px\", 3, \"src\"], [\"type\", \"button\", \"title\", \"Delete\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"position-absolute\", \"top-0\", \"end-0\", \"m-1\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"alt\", \"Current Cover Image\", 1, \"img-fluid\", \"rounded\", 2, \"height\", \"100px\", \"width\", \"auto\", \"max-width\", \"200px\", 3, \"src\"], [\"alt\", \"Current Master Plan\", 1, \"img-fluid\", \"rounded\", 2, \"height\", \"100px\", \"width\", \"auto\", \"max-width\", \"200px\", 3, \"src\"], [1, \"row\", \"g-2\"], [\"class\", \"col-6 col-md-4 col-lg-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-6\", \"col-md-4\", \"col-lg-3\"], [1, \"position-relative\"], [1, \"img-fluid\", \"rounded\", 2, \"height\", \"80px\", \"width\", \"100%\", \"object-fit\", \"cover\", 3, \"src\", \"alt\"], [1, \"video-thumbnail\", \"rounded\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"height\", \"80px\", \"width\", \"100%\", \"background-color\", \"#f8f9fa\", \"border\", \"1px solid #dee2e6\"], [1, \"fas\", \"fa-play-circle\", \"fa-2x\", \"text-primary\"], [1, \"d-flex\", \"justify-content-between\", \"pt-10\"], [\"type\", \"button\", 1, \"btn\", \"btn-light-dark\", \"btn-lg\", \"px-6\", \"py-3\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-lg\", \"btn-navy\", \"px-10\", \"py-3\", \"rounded-pill\", 3, \"click\", \"disabled\"], [1, \"indicator-label\", \"text-white\"], [1, \"d-flex\", \"justify-content-center\", \"pt-10\"], [1, \"d-flex\", \"flex-column\", \"align-items-center\"], [\"type\", \"button\", 1, \"btn\", \"btn-lg\", \"btn-blue-custom\", \"px-10\", \"py-3\", \"rounded-pill\", \"mb-3\", \"w-100\", 3, \"click\", \"disabled\"]],\n    template: function UpdatePropertyComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelement(1, \"app-developer-header\", 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n        i0.ɵɵelement(6, \"i\", 6);\n        i0.ɵɵelementStart(7, \"strong\");\n        i0.ɵɵtext(8, \"Edit mode :\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(9, \" You are in edit mode. Any changes you make will be saved to the existing project. \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 7);\n        i0.ɵɵtemplate(11, UpdatePropertyComponent_ng_container_11_Template, 6, 0, \"ng-container\", 8)(12, UpdatePropertyComponent_ng_container_12_Template, 6, 0, \"ng-container\", 8)(13, UpdatePropertyComponent_ng_container_13_Template, 6, 0, \"ng-container\", 8)(14, UpdatePropertyComponent_ng_container_14_Template, 6, 0, \"ng-container\", 8);\n        i0.ɵɵelementStart(15, \"div\", 9)(16, \"span\", 10);\n        i0.ɵɵtext(17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"span\", 11);\n        i0.ɵɵtext(19, \"of\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"span\", 12);\n        i0.ɵɵtext(21);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(22, UpdatePropertyComponent_div_22_Template, 2, 0, \"div\", 13);\n        i0.ɵɵelementStart(23, \"div\", 14);\n        i0.ɵɵelement(24, \"div\", 15);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(25, \"form\", 16);\n        i0.ɵɵtemplate(26, UpdatePropertyComponent_div_26_Template, 21, 13, \"div\", 17)(27, UpdatePropertyComponent_div_27_Template, 34, 11, \"div\", 17)(28, UpdatePropertyComponent_div_28_Template, 58, 20, \"div\", 17)(29, UpdatePropertyComponent_div_29_Template, 52, 16, \"div\", 17)(30, UpdatePropertyComponent_div_30_Template, 6, 1, \"div\", 18)(31, UpdatePropertyComponent_div_31_Template, 3, 2, \"div\", 19);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"title\", \"Update Project\")(\"subtitle\", \"Edit and update existing project information\");\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 3);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 4);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\"Step \", ctx.currentStep, \"\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(ctx.totalSteps);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵstyleProp(\"width\", ctx.currentStep / ctx.totalSteps * 100 + \"%\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 3);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 4);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n      }\n    },\n    dependencies: [DeveloperDashboardModule, i4.HeaderComponent, CommonModule, i5.NgForOf, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n    styles: [\".card[_ngcontent-%COMP%] {\\n  max-width: 550px;\\n  margin: 0 auto;\\n  border-radius: 8px;\\n  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);\\n  border: 1px solid #e4e6ef;\\n}\\n.card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n.card.cursor-pointer[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.card.cursor-pointer[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n}\\n\\n.text-dark-blue[_ngcontent-%COMP%] {\\n  color: #0d6efd; \\n\\n}\\n\\n.upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n  border-radius: 25px;\\n  border: 1px solid #e4e6ef;\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  font-size: 1rem;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 80px;\\n  margin-bottom: 0;\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   .upload-icon[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background-color: #0d6efd;\\n  border-radius: 50%;\\n  margin-bottom: 10px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   .upload-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 16px;\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   .upload-text[_ngcontent-%COMP%] {\\n  color: #0d6efd;\\n  font-weight: bold;\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:hover {\\n  border-color: #0d6efd;\\n  box-shadow: 0 0 10px rgba(13, 110, 253, 0.1);\\n}\\n\\n.btn-dark-blue[_ngcontent-%COMP%] {\\n  background-color: #1e1e2d;\\n  color: #ffffff;\\n}\\n\\n.btn-navy[_ngcontent-%COMP%] {\\n  background-color: #1e1e7c;\\n  color: #ffffff;\\n  border: none;\\n}\\n.btn-navy[_ngcontent-%COMP%]:hover {\\n  background-color: #16165a;\\n}\\n.btn-navy[_ngcontent-%COMP%]:disabled {\\n  background-color: #9999c9;\\n}\\n\\n.btn-blue-custom[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: #ffffff;\\n  border: none;\\n}\\n.btn-blue-custom[_ngcontent-%COMP%]:hover {\\n  background-color: #0056b3;\\n}\\n.btn-blue-custom[_ngcontent-%COMP%]:disabled {\\n  background-color: #6c757d;\\n  color: #ffffff;\\n}\\n\\n.btn-green-custom[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  color: #000000;\\n  border: 2px solid #28a745;\\n}\\n.btn-green-custom[_ngcontent-%COMP%]:hover {\\n  background-color: #28a745;\\n  color: #ffffff;\\n  border-color: #28a745;\\n}\\n.btn-green-custom[_ngcontent-%COMP%]:focus {\\n  background-color: #ffffff;\\n  color: #000000;\\n  border-color: #28a745;\\n  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);\\n}\\n\\n.progress[_ngcontent-%COMP%] {\\n  border-radius: 30px;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  border-radius: 30px;\\n}\\n\\n.cursor-pointer[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  padding: 0.75rem 1rem;\\n}\\n\\n.form-select[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  padding: 0.75rem 1rem;\\n}\\n\\n.dropdown[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  border: 1px solid #e4e6ef;\\n  background-color: #f5f8fa;\\n  color: #5e6278;\\n  padding: 0.75rem 1rem;\\n}\\n.dropdown[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]:hover, .dropdown[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]:focus {\\n  background-color: #f5f8fa;\\n  border-color: #e4e6ef;\\n}\\n.dropdown[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]::after {\\n  display: none;\\n}\\n.dropdown[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  box-shadow: 0 0 50px 0 rgba(82, 63, 105, 0.15);\\n  padding: 0.5rem 0;\\n}\\n.dropdown[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1.25rem;\\n  cursor: pointer;\\n}\\n.dropdown[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f8fa;\\n}\\n\\n.existing-files-container[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-top: 1px solid #e4e6ef;\\n}\\n.existing-files-container[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #5e6278;\\n  margin-bottom: 0.75rem;\\n}\\n.existing-files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.existing-files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n}\\n.existing-files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]:hover   .delete-btn[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.existing-files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  border: 2px solid #e4e6ef;\\n  transition: all 0.2s ease;\\n}\\n.existing-files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\\n  border-color: #0d6efd;\\n}\\n.existing-files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transition: all 0.2s ease;\\n  width: 5px;\\n  height: 5px;\\n  padding: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.existing-files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #dc3545 !important;\\n  border-color: #dc3545 !important;\\n  transform: scale(1.1);\\n}\\n.existing-files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .video-overlay-small[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  background-color: rgba(0, 0, 0, 0.7);\\n  border-radius: 50%;\\n  width: 30px;\\n  height: 30px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-size: 12px;\\n  pointer-events: none;\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.375rem 0.5rem;\\n}\\n.badge.bg-info[_ngcontent-%COMP%] {\\n  background-color: #0dcaf0 !important;\\n}\\n.badge.bg-success[_ngcontent-%COMP%] {\\n  background-color: #198754 !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvZGV2ZWxvcGVyL3VwZGF0ZS1wcm9wZXJ0eS1kZXYvdXBkYXRlLXByb3BlcnR5LmNvbXBvbmVudC5zY3NzIiwid2VicGFjazovLy4vLi4vLi4vLi4vLi4vLi4vY29tcHUlMjB6b25lL0Rlc2t0b3AvdGFza2VzL05ldyUyMGZvbGRlci9lYXN5ZGVhbC1mcm9udGVuZC9zcmMvYXBwL3BhZ2VzL2RldmVsb3Blci91cGRhdGUtcHJvcGVydHktZGV2L3VwZGF0ZS1wcm9wZXJ0eS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0Esd0NBQUE7RUFDQSx5QkFBQTtBQ0NGO0FEQ0U7RUFDRSxlQUFBO0FDQ0o7QURFRTtFQUNFLHlCQUFBO0FDQUo7QURFSTtFQUNFLDJCQUFBO0VBQ0EseUNBQUE7QUNBTjs7QURLQTtFQUNFLGNBQUEsRUFBQSw0QkFBQTtBQ0ZGOztBRE9FO0VBQ0UseUJBQUE7RUFDQSxtQkFBQTtFQUNBLHlCQUFBO0FDSko7QURNSTtFQUNFLGVBQUE7RUFDQSxlQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLFlBQUE7RUFDQSxnQkFBQTtBQ0pOO0FETU07RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0FDSlI7QURNUTtFQUNFLFlBQUE7RUFDQSxlQUFBO0FDSlY7QURRTTtFQUNFLGNBQUE7RUFDQSxpQkFBQTtBQ05SO0FEVUk7RUFDRSxxQkFBQTtFQUNBLDRDQUFBO0FDUk47O0FEYUE7RUFDRSx5QkFBQTtFQUNBLGNBQUE7QUNWRjs7QURhQTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLFlBQUE7QUNWRjtBRFdFO0VBQ0UseUJBQUE7QUNUSjtBRFdFO0VBQ0UseUJBQUE7QUNUSjs7QURhQTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLFlBQUE7QUNWRjtBRFdFO0VBQ0UseUJBQUE7QUNUSjtBRFdFO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0FDVEo7O0FEYUE7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQ1ZGO0FEV0U7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSxxQkFBQTtBQ1RKO0FEV0U7RUFDRSx5QkFBQTtFQUNBLGNBQUE7RUFDQSxxQkFBQTtFQUNBLGdEQUFBO0FDVEo7O0FEYUE7RUFDRSxtQkFBQTtBQ1ZGOztBRGFBO0VBQ0UsbUJBQUE7QUNWRjs7QURhQTtFQUNFLGVBQUE7QUNWRjs7QURjQTtFQUNFLGtCQUFBO0VBQ0EscUJBQUE7QUNYRjs7QURjQTtFQUNFLGtCQUFBO0VBQ0EscUJBQUE7QUNYRjs7QURnQkU7RUFDRSxrQkFBQTtFQUNBLHlCQUFBO0VBQ0EseUJBQUE7RUFDQSxjQUFBO0VBQ0EscUJBQUE7QUNiSjtBRGVJO0VBRUUseUJBQUE7RUFDQSxxQkFBQTtBQ2ROO0FEaUJJO0VBQ0UsYUFBQTtBQ2ZOO0FEbUJFO0VBQ0Usa0JBQUE7RUFDQSw4Q0FBQTtFQUNBLGlCQUFBO0FDakJKO0FEbUJJO0VBQ0Usd0JBQUE7RUFDQSxlQUFBO0FDakJOO0FEbUJNO0VBQ0UseUJBQUE7QUNqQlI7O0FEd0JBO0VBQ0UseUJBQUE7RUFDQSw2QkFBQTtBQ3JCRjtBRHVCRTtFQUNFLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esc0JBQUE7QUNyQko7QUR3QkU7RUFDRSx5QkFBQTtBQ3RCSjtBRHdCSTtFQUNFLDJCQUFBO0FDdEJOO0FEd0JNO0VBQ0UsVUFBQTtBQ3RCUjtBRDBCSTtFQUNFLHlCQUFBO0VBQ0EseUJBQUE7QUN4Qk47QUQwQk07RUFDRSxxQkFBQTtBQ3hCUjtBRDRCSTtFQUNFLFVBQUE7RUFDQSx5QkFBQTtFQUNBLFVBQUE7RUFDQSxXQUFBO0VBQ0EsVUFBQTtFQUVBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0FDM0JOO0FENkJNO0VBQ0Usb0NBQUE7RUFDQSxnQ0FBQTtFQUNBLHFCQUFBO0FDM0JSO0FEK0JJO0VBQ0Usa0JBQUE7RUFDQSxRQUFBO0VBQ0EsU0FBQTtFQUNBLGdDQUFBO0VBQ0Esb0NBQUE7RUFDQSxrQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxZQUFBO0VBQ0EsZUFBQTtFQUNBLG9CQUFBO0FDN0JOOztBRG1DQTtFQUNFLGtCQUFBO0VBQ0Esd0JBQUE7QUNoQ0Y7QURrQ0U7RUFDRSxvQ0FBQTtBQ2hDSjtBRG1DRTtFQUNFLG9DQUFBO0FDakNKIiwic291cmNlc0NvbnRlbnQiOlsiLmNhcmQge1xyXG4gIG1heC13aWR0aDogNTUwcHg7XHJcbiAgbWFyZ2luOiAwIGF1dG87XHJcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gIGJveC1zaGFkb3c6IDAgMCAxNXB4IHJnYmEoMCwgMCwgMCwgMC4wNSk7XHJcbiAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTZlZjtcclxuXHJcbiAgLmNhcmQtYm9keSB7XHJcbiAgICBwYWRkaW5nOiAxLjVyZW07XHJcbiAgfVxyXG5cclxuICAmLmN1cnNvci1wb2ludGVyIHtcclxuICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcblxyXG4gICAgJjpob3ZlciB7XHJcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtM3B4KTtcclxuICAgICAgYm94LXNoYWRvdzogMCA1cHggMTVweCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4udGV4dC1kYXJrLWJsdWUge1xyXG4gIGNvbG9yOiAjMGQ2ZWZkOyAvKiBNb3JlIHZpYnJhbnQgYmx1ZSBjb2xvciAqL1xyXG59XHJcblxyXG4vLyBVcGxvYWQgY2FyZCBzdHlsaW5nXHJcbi51cGxvYWQtY2FyZC1jb250YWluZXIge1xyXG4gIC5jYXJkIHtcclxuICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XHJcbiAgICBib3JkZXItcmFkaXVzOiAyNXB4O1xyXG4gICAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTZlZjtcclxuXHJcbiAgICBsYWJlbCB7XHJcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgICAgZm9udC1zaXplOiAxcmVtO1xyXG4gICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgICAgaGVpZ2h0OiA4MHB4O1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiAwO1xyXG5cclxuICAgICAgLnVwbG9hZC1pY29uIHtcclxuICAgICAgICB3aWR0aDogMzJweDtcclxuICAgICAgICBoZWlnaHQ6IDMycHg7XHJcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzBkNmVmZDtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7XHJcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogMTBweDtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcblxyXG4gICAgICAgIGkge1xyXG4gICAgICAgICAgY29sb3I6IHdoaXRlO1xyXG4gICAgICAgICAgZm9udC1zaXplOiAxNnB4O1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLnVwbG9hZC10ZXh0IHtcclxuICAgICAgICBjb2xvcjogIzBkNmVmZDtcclxuICAgICAgICBmb250LXdlaWdodDogYm9sZDtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgICY6aG92ZXIge1xyXG4gICAgICBib3JkZXItY29sb3I6ICMwZDZlZmQ7XHJcbiAgICAgIGJveC1zaGFkb3c6IDAgMCAxMHB4IHJnYmEoMTMsIDExMCwgMjUzLCAwLjEpO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLmJ0bi1kYXJrLWJsdWUge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICMxZTFlMmQ7XHJcbiAgY29sb3I6ICNmZmZmZmY7XHJcbn1cclxuXHJcbi5idG4tbmF2eSB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogIzFlMWU3YztcclxuICBjb2xvcjogI2ZmZmZmZjtcclxuICBib3JkZXI6IG5vbmU7XHJcbiAgJjpob3ZlciB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTYxNjVhO1xyXG4gIH1cclxuICAmOmRpc2FibGVkIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICM5OTk5Yzk7XHJcbiAgfVxyXG59XHJcblxyXG4uYnRuLWJsdWUtY3VzdG9tIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjMDA3YmZmO1xyXG4gIGNvbG9yOiAjZmZmZmZmO1xyXG4gIGJvcmRlcjogbm9uZTtcclxuICAmOmhvdmVyIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICMwMDU2YjM7XHJcbiAgfVxyXG4gICY6ZGlzYWJsZWQge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogIzZjNzU3ZDtcclxuICAgIGNvbG9yOiAjZmZmZmZmO1xyXG4gIH1cclxufVxyXG5cclxuLmJ0bi1ncmVlbi1jdXN0b20ge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7XHJcbiAgY29sb3I6ICMwMDAwMDA7XHJcbiAgYm9yZGVyOiAycHggc29saWQgIzI4YTc0NTtcclxuICAmOmhvdmVyIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICMyOGE3NDU7XHJcbiAgICBjb2xvcjogI2ZmZmZmZjtcclxuICAgIGJvcmRlci1jb2xvcjogIzI4YTc0NTtcclxuICB9XHJcbiAgJjpmb2N1cyB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmO1xyXG4gICAgY29sb3I6ICMwMDAwMDA7XHJcbiAgICBib3JkZXItY29sb3I6ICMyOGE3NDU7XHJcbiAgICBib3gtc2hhZG93OiAwIDAgMCAwLjJyZW0gcmdiYSg0MCwgMTY3LCA2OSwgMC4yNSk7XHJcbiAgfVxyXG59XHJcblxyXG4ucHJvZ3Jlc3Mge1xyXG4gIGJvcmRlci1yYWRpdXM6IDMwcHg7XHJcbn1cclxuXHJcbi5wcm9ncmVzcy1iYXIge1xyXG4gIGJvcmRlci1yYWRpdXM6IDMwcHg7XHJcbn1cclxuXHJcbi5jdXJzb3ItcG9pbnRlciB7XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG59XHJcblxyXG4vLyBDdXN0b20gc3R5bGluZyBmb3IgdGhlIGZvcm1cclxuLmZvcm0tY29udHJvbCB7XHJcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gIHBhZGRpbmc6IDAuNzVyZW0gMXJlbTtcclxufVxyXG5cclxuLmZvcm0tc2VsZWN0IHtcclxuICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgcGFkZGluZzogMC43NXJlbSAxcmVtO1xyXG59XHJcblxyXG4vLyBEcm9wZG93biBzdHlsaW5nXHJcbi5kcm9wZG93biB7XHJcbiAgLmJ0bi1vdXRsaW5lLXNlY29uZGFyeSB7XHJcbiAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjZTRlNmVmO1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjhmYTtcclxuICAgIGNvbG9yOiAjNWU2Mjc4O1xyXG4gICAgcGFkZGluZzogMC43NXJlbSAxcmVtO1xyXG5cclxuICAgICY6aG92ZXIsXHJcbiAgICAmOmZvY3VzIHtcclxuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjhmYTtcclxuICAgICAgYm9yZGVyLWNvbG9yOiAjZTRlNmVmO1xyXG4gICAgfVxyXG5cclxuICAgICY6OmFmdGVyIHtcclxuICAgICAgZGlzcGxheTogbm9uZTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5kcm9wZG93bi1tZW51IHtcclxuICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgIGJveC1zaGFkb3c6IDAgMCA1MHB4IDAgcmdiYSg4MiwgNjMsIDEwNSwgMC4xNSk7XHJcbiAgICBwYWRkaW5nOiAwLjVyZW0gMDtcclxuXHJcbiAgICAuZHJvcGRvd24taXRlbSB7XHJcbiAgICAgIHBhZGRpbmc6IDAuNzVyZW0gMS4yNXJlbTtcclxuICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG5cclxuICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjhmYTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gRXhpc3RpbmcgZmlsZXMgZGlzcGxheSBzdHlsaW5nXHJcbi5leGlzdGluZy1maWxlcy1jb250YWluZXIge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7XHJcbiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlNGU2ZWY7XHJcblxyXG4gIGg2IHtcclxuICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgY29sb3I6ICM1ZTYyNzg7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAwLjc1cmVtO1xyXG4gIH1cclxuXHJcbiAgLmZpbGUtaXRlbSB7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xyXG5cclxuICAgICY6aG92ZXIge1xyXG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XHJcblxyXG4gICAgICAuZGVsZXRlLWJ0biB7XHJcbiAgICAgICAgb3BhY2l0eTogMTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGltZyB7XHJcbiAgICAgIGJvcmRlcjogMnB4IHNvbGlkICNlNGU2ZWY7XHJcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XHJcblxyXG4gICAgICAmOmhvdmVyIHtcclxuICAgICAgICBib3JkZXItY29sb3I6ICMwZDZlZmQ7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAuZGVsZXRlLWJ0biB7XHJcbiAgICAgIG9wYWNpdHk6IDA7XHJcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XHJcbiAgICAgIHdpZHRoOiA1cHg7XHJcbiAgICAgIGhlaWdodDogNXB4O1xyXG4gICAgICBwYWRkaW5nOiAwO1xyXG5cclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcblxyXG4gICAgICAmOmhvdmVyIHtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZGMzNTQ1ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgYm9yZGVyLWNvbG9yOiAjZGMzNTQ1ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLnZpZGVvLW92ZXJsYXktc21hbGwge1xyXG4gICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgIHRvcDogNTAlO1xyXG4gICAgICBsZWZ0OiA1MCU7XHJcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlKC01MCUsIC01MCUpO1xyXG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuNyk7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICAgICAgd2lkdGg6IDMwcHg7XHJcbiAgICAgIGhlaWdodDogMzBweDtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICAgIGNvbG9yOiB3aGl0ZTtcclxuICAgICAgZm9udC1zaXplOiAxMnB4O1xyXG4gICAgICBwb2ludGVyLWV2ZW50czogbm9uZTtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIEJhZGdlIHN0eWxpbmcgZm9yIGZpbGUgY291bnRzXHJcbi5iYWRnZSB7XHJcbiAgZm9udC1zaXplOiAwLjc1cmVtO1xyXG4gIHBhZGRpbmc6IDAuMzc1cmVtIDAuNXJlbTtcclxuXHJcbiAgJi5iZy1pbmZvIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICMwZGNhZjAgIWltcG9ydGFudDtcclxuICB9XHJcblxyXG4gICYuYmctc3VjY2VzcyB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTk4NzU0ICFpbXBvcnRhbnQ7XHJcbiAgfVxyXG59XHJcbiIsIi5jYXJkIHtcbiAgbWF4LXdpZHRoOiA1NTBweDtcbiAgbWFyZ2luOiAwIGF1dG87XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgYm94LXNoYWRvdzogMCAwIDE1cHggcmdiYSgwLCAwLCAwLCAwLjA1KTtcbiAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTZlZjtcbn1cbi5jYXJkIC5jYXJkLWJvZHkge1xuICBwYWRkaW5nOiAxLjVyZW07XG59XG4uY2FyZC5jdXJzb3ItcG9pbnRlciB7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG59XG4uY2FyZC5jdXJzb3ItcG9pbnRlcjpob3ZlciB7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtM3B4KTtcbiAgYm94LXNoYWRvdzogMCA1cHggMTVweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG59XG5cbi50ZXh0LWRhcmstYmx1ZSB7XG4gIGNvbG9yOiAjMGQ2ZWZkOyAvKiBNb3JlIHZpYnJhbnQgYmx1ZSBjb2xvciAqL1xufVxuXG4udXBsb2FkLWNhcmQtY29udGFpbmVyIC5jYXJkIHtcbiAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcbiAgYm9yZGVyLXJhZGl1czogMjVweDtcbiAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTZlZjtcbn1cbi51cGxvYWQtY2FyZC1jb250YWluZXIgLmNhcmQgbGFiZWwge1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIGZvbnQtc2l6ZTogMXJlbTtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGhlaWdodDogODBweDtcbiAgbWFyZ2luLWJvdHRvbTogMDtcbn1cbi51cGxvYWQtY2FyZC1jb250YWluZXIgLmNhcmQgbGFiZWwgLnVwbG9hZC1pY29uIHtcbiAgd2lkdGg6IDMycHg7XG4gIGhlaWdodDogMzJweDtcbiAgYmFja2dyb3VuZC1jb2xvcjogIzBkNmVmZDtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBtYXJnaW4tYm90dG9tOiAxMHB4O1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbn1cbi51cGxvYWQtY2FyZC1jb250YWluZXIgLmNhcmQgbGFiZWwgLnVwbG9hZC1pY29uIGkge1xuICBjb2xvcjogd2hpdGU7XG4gIGZvbnQtc2l6ZTogMTZweDtcbn1cbi51cGxvYWQtY2FyZC1jb250YWluZXIgLmNhcmQgbGFiZWwgLnVwbG9hZC10ZXh0IHtcbiAgY29sb3I6ICMwZDZlZmQ7XG4gIGZvbnQtd2VpZ2h0OiBib2xkO1xufVxuLnVwbG9hZC1jYXJkLWNvbnRhaW5lciAuY2FyZDpob3ZlciB7XG4gIGJvcmRlci1jb2xvcjogIzBkNmVmZDtcbiAgYm94LXNoYWRvdzogMCAwIDEwcHggcmdiYSgxMywgMTEwLCAyNTMsIDAuMSk7XG59XG5cbi5idG4tZGFyay1ibHVlIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogIzFlMWUyZDtcbiAgY29sb3I6ICNmZmZmZmY7XG59XG5cbi5idG4tbmF2eSB7XG4gIGJhY2tncm91bmQtY29sb3I6ICMxZTFlN2M7XG4gIGNvbG9yOiAjZmZmZmZmO1xuICBib3JkZXI6IG5vbmU7XG59XG4uYnRuLW5hdnk6aG92ZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTYxNjVhO1xufVxuLmJ0bi1uYXZ5OmRpc2FibGVkIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogIzk5OTljOTtcbn1cblxuLmJ0bi1ibHVlLWN1c3RvbSB7XG4gIGJhY2tncm91bmQtY29sb3I6ICMwMDdiZmY7XG4gIGNvbG9yOiAjZmZmZmZmO1xuICBib3JkZXI6IG5vbmU7XG59XG4uYnRuLWJsdWUtY3VzdG9tOmhvdmVyIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogIzAwNTZiMztcbn1cbi5idG4tYmx1ZS1jdXN0b206ZGlzYWJsZWQge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjNmM3NTdkO1xuICBjb2xvcjogI2ZmZmZmZjtcbn1cblxuLmJ0bi1ncmVlbi1jdXN0b20ge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmO1xuICBjb2xvcjogIzAwMDAwMDtcbiAgYm9yZGVyOiAycHggc29saWQgIzI4YTc0NTtcbn1cbi5idG4tZ3JlZW4tY3VzdG9tOmhvdmVyIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogIzI4YTc0NTtcbiAgY29sb3I6ICNmZmZmZmY7XG4gIGJvcmRlci1jb2xvcjogIzI4YTc0NTtcbn1cbi5idG4tZ3JlZW4tY3VzdG9tOmZvY3VzIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZjtcbiAgY29sb3I6ICMwMDAwMDA7XG4gIGJvcmRlci1jb2xvcjogIzI4YTc0NTtcbiAgYm94LXNoYWRvdzogMCAwIDAgMC4ycmVtIHJnYmEoNDAsIDE2NywgNjksIDAuMjUpO1xufVxuXG4ucHJvZ3Jlc3Mge1xuICBib3JkZXItcmFkaXVzOiAzMHB4O1xufVxuXG4ucHJvZ3Jlc3MtYmFyIHtcbiAgYm9yZGVyLXJhZGl1czogMzBweDtcbn1cblxuLmN1cnNvci1wb2ludGVyIHtcbiAgY3Vyc29yOiBwb2ludGVyO1xufVxuXG4uZm9ybS1jb250cm9sIHtcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xuICBwYWRkaW5nOiAwLjc1cmVtIDFyZW07XG59XG5cbi5mb3JtLXNlbGVjdCB7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgcGFkZGluZzogMC43NXJlbSAxcmVtO1xufVxuXG4uZHJvcGRvd24gLmJ0bi1vdXRsaW5lLXNlY29uZGFyeSB7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTZlZjtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjhmYTtcbiAgY29sb3I6ICM1ZTYyNzg7XG4gIHBhZGRpbmc6IDAuNzVyZW0gMXJlbTtcbn1cbi5kcm9wZG93biAuYnRuLW91dGxpbmUtc2Vjb25kYXJ5OmhvdmVyLCAuZHJvcGRvd24gLmJ0bi1vdXRsaW5lLXNlY29uZGFyeTpmb2N1cyB7XG4gIGJhY2tncm91bmQtY29sb3I6ICNmNWY4ZmE7XG4gIGJvcmRlci1jb2xvcjogI2U0ZTZlZjtcbn1cbi5kcm9wZG93biAuYnRuLW91dGxpbmUtc2Vjb25kYXJ5OjphZnRlciB7XG4gIGRpc3BsYXk6IG5vbmU7XG59XG4uZHJvcGRvd24gLmRyb3Bkb3duLW1lbnUge1xuICBib3JkZXItcmFkaXVzOiA4cHg7XG4gIGJveC1zaGFkb3c6IDAgMCA1MHB4IDAgcmdiYSg4MiwgNjMsIDEwNSwgMC4xNSk7XG4gIHBhZGRpbmc6IDAuNXJlbSAwO1xufVxuLmRyb3Bkb3duIC5kcm9wZG93bi1tZW51IC5kcm9wZG93bi1pdGVtIHtcbiAgcGFkZGluZzogMC43NXJlbSAxLjI1cmVtO1xuICBjdXJzb3I6IHBvaW50ZXI7XG59XG4uZHJvcGRvd24gLmRyb3Bkb3duLW1lbnUgLmRyb3Bkb3duLWl0ZW06aG92ZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmOGZhO1xufVxuXG4uZXhpc3RpbmctZmlsZXMtY29udGFpbmVyIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTtcbiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlNGU2ZWY7XG59XG4uZXhpc3RpbmctZmlsZXMtY29udGFpbmVyIGg2IHtcbiAgZm9udC1zaXplOiAwLjg3NXJlbTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgY29sb3I6ICM1ZTYyNzg7XG4gIG1hcmdpbi1ib3R0b206IDAuNzVyZW07XG59XG4uZXhpc3RpbmctZmlsZXMtY29udGFpbmVyIC5maWxlLWl0ZW0ge1xuICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xufVxuLmV4aXN0aW5nLWZpbGVzLWNvbnRhaW5lciAuZmlsZS1pdGVtOmhvdmVyIHtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xufVxuLmV4aXN0aW5nLWZpbGVzLWNvbnRhaW5lciAuZmlsZS1pdGVtOmhvdmVyIC5kZWxldGUtYnRuIHtcbiAgb3BhY2l0eTogMTtcbn1cbi5leGlzdGluZy1maWxlcy1jb250YWluZXIgLmZpbGUtaXRlbSBpbWcge1xuICBib3JkZXI6IDJweCBzb2xpZCAjZTRlNmVmO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xufVxuLmV4aXN0aW5nLWZpbGVzLWNvbnRhaW5lciAuZmlsZS1pdGVtIGltZzpob3ZlciB7XG4gIGJvcmRlci1jb2xvcjogIzBkNmVmZDtcbn1cbi5leGlzdGluZy1maWxlcy1jb250YWluZXIgLmZpbGUtaXRlbSAuZGVsZXRlLWJ0biB7XG4gIG9wYWNpdHk6IDA7XG4gIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XG4gIHdpZHRoOiA1cHg7XG4gIGhlaWdodDogNXB4O1xuICBwYWRkaW5nOiAwO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbn1cbi5leGlzdGluZy1maWxlcy1jb250YWluZXIgLmZpbGUtaXRlbSAuZGVsZXRlLWJ0bjpob3ZlciB7XG4gIGJhY2tncm91bmQtY29sb3I6ICNkYzM1NDUgIWltcG9ydGFudDtcbiAgYm9yZGVyLWNvbG9yOiAjZGMzNTQ1ICFpbXBvcnRhbnQ7XG4gIHRyYW5zZm9ybTogc2NhbGUoMS4xKTtcbn1cbi5leGlzdGluZy1maWxlcy1jb250YWluZXIgLmZpbGUtaXRlbSAudmlkZW8tb3ZlcmxheS1zbWFsbCB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiA1MCU7XG4gIGxlZnQ6IDUwJTtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoLTUwJSwgLTUwJSk7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMCwgMCwgMCwgMC43KTtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICB3aWR0aDogMzBweDtcbiAgaGVpZ2h0OiAzMHB4O1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgY29sb3I6IHdoaXRlO1xuICBmb250LXNpemU6IDEycHg7XG4gIHBvaW50ZXItZXZlbnRzOiBub25lO1xufVxuXG4uYmFkZ2Uge1xuICBmb250LXNpemU6IDAuNzVyZW07XG4gIHBhZGRpbmc6IDAuMzc1cmVtIDAuNXJlbTtcbn1cbi5iYWRnZS5iZy1pbmZvIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogIzBkY2FmMCAhaW1wb3J0YW50O1xufVxuLmJhZGdlLmJnLXN1Y2Nlc3Mge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTk4NzU0ICFpbXBvcnRhbnQ7XG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "ReactiveFormsModule", "DeveloperDashboardModule", "CommonModule", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "UpdatePropertyComponent_div_22_Template_div_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "prevStep", "ɵɵadvance", "ɵɵtextInterpolate1", "getFieldErrorMessage", "ɵɵelement", "ɵɵtemplate", "UpdatePropertyComponent_div_26_div_5_Template", "UpdatePropertyComponent_div_26_div_10_Template", "UpdatePropertyComponent_div_26_div_15_Template", "UpdatePropertyComponent_div_26_div_20_Template", "ɵɵproperty", "step1Form", "ɵɵclassProp", "hasFieldError", "UpdatePropertyComponent_div_27_li_10_Template_a_click_1_listener", "city_r4", "_r3", "$implicit", "selectCity", "id", "name_en", "name", "UpdatePropertyComponent_div_27_li_20_Template_a_click_1_listener", "area_r6", "_r5", "selectArea", "UpdatePropertyComponent_div_27_li_10_Template", "UpdatePropertyComponent_div_27_li_20_Template", "UpdatePropertyComponent_div_27_div_25_Template", "UpdatePropertyComponent_div_27_div_30_Template", "step2Form", "ɵɵtextInterpolate", "selectedCityName", "cities", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "areas", "UpdatePropertyComponent_div_28_Template_a_click_11_listener", "_r7", "selectProjectType", "UpdatePropertyComponent_div_28_Template_a_click_14_listener", "UpdatePropertyComponent_div_28_Template_a_click_17_listener", "UpdatePropertyComponent_div_28_div_27_Template", "UpdatePropertyComponent_div_28_div_33_Template", "UpdatePropertyComponent_div_28_div_39_Template", "UpdatePropertyComponent_div_28_div_45_Template", "UpdatePropertyComponent_div_28_div_51_Template", "UpdatePropertyComponent_div_28_div_57_Template", "step3Form", "tmp_2_0", "get", "value", "getFileCount", "UpdatePropertyComponent_div_29_div_11_Template_button_click_5_listener", "_r9", "removeImage", "existingFiles", "logoImage", "ɵɵsanitizeUrl", "UpdatePropertyComponent_div_29_div_21_Template_button_click_5_listener", "_r10", "coverImage", "UpdatePropertyComponent_div_29_div_31_Template_button_click_5_listener", "_r11", "masterPlan", "galleryImages", "length", "UpdatePropertyComponent_div_29_div_41_div_4_Template_button_click_3_listener", "i_r13", "_r12", "index", "removeGalleryItem", "image_r14", "url", "UpdatePropertyComponent_div_29_div_41_div_4_Template", "galleryVideos", "UpdatePropertyComponent_div_29_div_51_div_4_Template_button_click_4_listener", "i_r16", "_r15", "UpdatePropertyComponent_div_29_div_51_div_4_Template", "UpdatePropertyComponent_div_29_span_8_Template", "UpdatePropertyComponent_div_29_span_9_Template", "UpdatePropertyComponent_div_29_Template_input_change_10_listener", "$event", "_r8", "onFileChange", "UpdatePropertyComponent_div_29_div_11_Template", "UpdatePropertyComponent_div_29_span_18_Template", "UpdatePropertyComponent_div_29_span_19_Template", "UpdatePropertyComponent_div_29_Template_input_change_20_listener", "UpdatePropertyComponent_div_29_div_21_Template", "UpdatePropertyComponent_div_29_span_28_Template", "UpdatePropertyComponent_div_29_span_29_Template", "UpdatePropertyComponent_div_29_Template_input_change_30_listener", "UpdatePropertyComponent_div_29_div_31_Template", "UpdatePropertyComponent_div_29_span_38_Template", "UpdatePropertyComponent_div_29_span_39_Template", "UpdatePropertyComponent_div_29_Template_input_change_40_listener", "UpdatePropertyComponent_div_29_div_41_Template", "UpdatePropertyComponent_div_29_span_48_Template", "UpdatePropertyComponent_div_29_span_49_Template", "UpdatePropertyComponent_div_29_Template_input_change_50_listener", "UpdatePropertyComponent_div_29_div_51_Template", "step4Form", "UpdatePropertyComponent_div_30_Template_button_click_1_listener", "_r17", "cancel", "UpdatePropertyComponent_div_30_Template_button_click_3_listener", "nextStep", "isCurrentFormValid", "UpdatePropertyComponent_div_31_ng_container_1_Template_button_click_1_listener", "_r18", "UpdatePropertyComponent_div_31_ng_container_1_ng_container_3_Template", "UpdatePropertyComponent_div_31_ng_container_1_ng_container_4_Template", "currentStep", "UpdatePropertyComponent_div_31_ng_container_2_Template_button_click_2_listener", "_r19", "submitForm", "UpdatePropertyComponent_div_31_ng_container_1_Template", "UpdatePropertyComponent_div_31_ng_container_2_Template", "totalSteps", "UpdatePropertyComponent", "fb", "router", "route", "projectsService", "cdr", "projectId", "developerId", "selectedCityId", "constructor", "ngOnInit", "initForms", "loadCities", "userJson", "localStorage", "getItem", "user", "JSON", "parse", "queryParams", "subscribe", "params", "loadProjectData", "navigate", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "designer", "projectExecutor", "managementTeam", "cityId", "areaId", "address", "googleMapsLink", "pattern", "googleMapUrl", "projectType", "buildingsCount", "min", "apartmentsCount", "villasCount", "duplexCount", "administrativeUnitsCount", "commercialUnitsCount", "gallery", "videos", "getCurrentForm", "valid", "fieldName", "form", "field", "invalid", "dirty", "touched", "errors", "markCurrentFormAsTouched", "Object", "keys", "controls", "for<PERSON>ach", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "areAllFormsValid", "getCities", "next", "response", "console", "log", "data", "Array", "isArray", "warn", "error", "err", "complete", "detectChanges", "loadAreas", "<PERSON><PERSON><PERSON><PERSON>", "cityName", "patchValue", "areaName", "type", "httpFormData", "FormData", "append", "fileFields", "files", "isMultiple", "includes", "file", "File", "updateProject", "event", "target", "from", "getById", "filter", "item", "setTimeout", "city", "area", "alert", "splice", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "ActivatedRoute", "i3", "ProjectsService", "ChangeDetectorRef", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "UpdatePropertyComponent_Template", "rf", "ctx", "UpdatePropertyComponent_ng_container_11_Template", "UpdatePropertyComponent_ng_container_12_Template", "UpdatePropertyComponent_ng_container_13_Template", "UpdatePropertyComponent_ng_container_14_Template", "UpdatePropertyComponent_div_22_Template", "UpdatePropertyComponent_div_26_Template", "UpdatePropertyComponent_div_27_Template", "UpdatePropertyComponent_div_28_Template", "UpdatePropertyComponent_div_29_Template", "UpdatePropertyComponent_div_30_Template", "UpdatePropertyComponent_div_31_Template", "ɵɵstyleProp", "i4", "HeaderComponent", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\developer\\update-property-dev\\update-property.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\developer\\update-property-dev\\update-property.component.html"], "sourcesContent": ["import { Component, OnInit, ChangeDetectorRef } from '@angular/core';\r\nimport {\r\n  FormBuilder,\r\n  FormGroup,\r\n  Validators,\r\n  ReactiveFormsModule,\r\n} from '@angular/forms';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { DeveloperDashboardModule } from '../developer-dashboard/developer-dashboard.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ProjectsService } from '../services/projects.service';\r\n\r\n@Component({\r\n  selector: 'app-update-property',\r\n  templateUrl: './update-property.component.html',\r\n  styleUrl: './update-property.component.scss',\r\n  standalone: true,\r\n  imports: [DeveloperDashboardModule, CommonModule, ReactiveFormsModule],\r\n})\r\nexport class UpdatePropertyComponent implements OnInit {\r\n  totalSteps = 4;\r\n  currentStep = 1;\r\n  projectId: number | null = null;\r\n  existingFiles: any = {};\r\n\r\n  step1Form: FormGroup;\r\n  step2Form: FormGroup;\r\n  step3Form: FormGroup;\r\n  step4Form: FormGroup;\r\n\r\n  cities: any[] = [];\r\n  areas: any[] = [];\r\n  developerId: number;\r\n  selectedCityId: number | null = null;\r\n  selectedCityName: string = '';\r\n  selectedAreaName: string = '';\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private projectsService: ProjectsService,\r\n    private cdr: ChangeDetectorRef\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.initForms();\r\n    this.loadCities();\r\n\r\n    const userJson = localStorage.getItem('currentUser');\r\n    let user = userJson ? JSON.parse(userJson) : null;\r\n    this.developerId = user?.developerId;\r\n\r\n    this.route.queryParams.subscribe((params) => {\r\n      if (params['id']) {\r\n        this.projectId = +params['id'];\r\n        this.loadProjectData();\r\n      } else {\r\n        this.router.navigate(['/developer/projects']);\r\n      }\r\n    });\r\n  }\r\n\r\n  initForms() {\r\n    // Step 1: Basic Property Settings\r\n    this.step1Form = this.fb.group({\r\n      name: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.minLength(3),\r\n          Validators.maxLength(100),\r\n        ],\r\n      ],\r\n      designer: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.minLength(2),\r\n          Validators.maxLength(50),\r\n        ],\r\n      ],\r\n      projectExecutor: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.minLength(2),\r\n          Validators.maxLength(50),\r\n        ],\r\n      ],\r\n      managementTeam: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.minLength(2),\r\n          Validators.maxLength(50),\r\n        ],\r\n      ],\r\n    });\r\n\r\n    // Step 2: Location Information\r\n    this.step2Form = this.fb.group({\r\n      cityId: ['', Validators.required],\r\n      areaId: ['', Validators.required],\r\n      address: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.minLength(10),\r\n          Validators.maxLength(200),\r\n        ],\r\n      ],\r\n      googleMapsLink: ['', [Validators.pattern('https?://.+')]],\r\n      googleMapUrl: ['', [Validators.pattern('https?://.+')]],\r\n    });\r\n\r\n    // Step 3: Project type\r\n    this.step3Form = this.fb.group({\r\n      projectType: ['', Validators.required],\r\n      buildingsCount: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.min(0),\r\n          Validators.pattern('^[0-9]+$'),\r\n        ],\r\n      ],\r\n      apartmentsCount: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.min(0),\r\n          Validators.pattern('^[0-9]+$'),\r\n        ],\r\n      ],\r\n      villasCount: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.min(0),\r\n          Validators.pattern('^[0-9]+$'),\r\n        ],\r\n      ],\r\n      duplexCount: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.min(0),\r\n          Validators.pattern('^[0-9]+$'),\r\n        ],\r\n      ],\r\n      administrativeUnitsCount: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.min(0),\r\n          Validators.pattern('^[0-9]+$'),\r\n        ],\r\n      ],\r\n      commercialUnitsCount: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.min(0),\r\n          Validators.pattern('^[0-9]+$'),\r\n        ],\r\n      ],\r\n    });\r\n\r\n    // Step 4: Project Documents\r\n    this.step4Form = this.fb.group({\r\n      logoImage: [[]],\r\n      coverImage: [[]],\r\n      masterPlan: [[]],\r\n      gallery: [[]],\r\n      videos: [[]],\r\n    });\r\n  }\r\n\r\n  getCurrentForm(): FormGroup {\r\n    switch (this.currentStep) {\r\n      case 1:\r\n        return this.step1Form;\r\n      case 2:\r\n        return this.step2Form;\r\n      case 3:\r\n        return this.step3Form;\r\n      case 4:\r\n        return this.step4Form;\r\n      default:\r\n        return this.step1Form;\r\n    }\r\n  }\r\n\r\n  isCurrentFormValid(): boolean {\r\n    return this.getCurrentForm().valid;\r\n  }\r\n\r\n  hasFieldError(fieldName: string): boolean {\r\n    const form = this.getCurrentForm();\r\n    const field = form.get(fieldName);\r\n    return field ? field.invalid && (field.dirty || field.touched) : false;\r\n  }\r\n\r\n  getFieldErrorMessage(fieldName: string): string {\r\n    const field = this.getCurrentForm().get(fieldName);\r\n\r\n    if (!field?.errors) return '';\r\n\r\n    if (field.errors['required']) return 'Required';\r\n    if (field.errors['minlength']) return 'Too short';\r\n    if (field.errors['maxlength']) return 'Too long';\r\n    if (field.errors['min']) return 'Invalid number';\r\n    if (field.errors['pattern']) return 'Invalid format';\r\n\r\n    return 'Invalid';\r\n  }\r\n\r\n  markCurrentFormAsTouched(): void {\r\n    const form = this.getCurrentForm();\r\n    Object.keys(form.controls).forEach((key) => {\r\n      form.get(key)?.markAsTouched();\r\n    });\r\n  }\r\n\r\n  areAllFormsValid(): boolean {\r\n    return this.step1Form.valid && this.step2Form.valid && this.step3Form.valid;\r\n  }\r\n\r\n  nextStep() {\r\n    this.markCurrentFormAsTouched();\r\n\r\n    if (!this.isCurrentFormValid()) {\r\n      return;\r\n    }\r\n\r\n    if (this.currentStep < this.totalSteps) {\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  prevStep() {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  loadCities(): void {\r\n    this.projectsService.getCities().subscribe({\r\n      next: (response: any) => {\r\n        console.log('Cities response:', response);\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.cities = response.data;\r\n        } else if (response && Array.isArray(response)) {\r\n          this.cities = response;\r\n        } else if (response && response.cities) {\r\n          this.cities = response.cities;\r\n        } else {\r\n          console.warn('No cities data in response:', response);\r\n          this.cities = [];\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        console.error('Error loading cities:', err);\r\n      },\r\n      complete: () => {\r\n        this.cdr.detectChanges();\r\n      },\r\n    });\r\n  }\r\n\r\n  loadAreas(cityId?: number): void {\r\n    this.projectsService.getAreas(cityId).subscribe({\r\n      next: (response: any) => {\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.areas = response.data;\r\n        } else if (response && Array.isArray(response)) {\r\n          this.areas = response;\r\n        } else if (response && response.areas) {\r\n          this.areas = response.areas;\r\n        } else {\r\n          this.areas = [];\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        console.error('Error loading areas:', err);\r\n        this.areas = [];\r\n      },\r\n      complete: () => {\r\n        this.cdr.detectChanges();\r\n      },\r\n    });\r\n  }\r\n\r\n  selectCity(cityId: number, cityName: string) {\r\n    this.selectedCityId = cityId;\r\n    this.selectedCityName = cityName;\r\n    this.step2Form.patchValue({\r\n      cityId: cityId,\r\n    });\r\n    this.loadAreas(cityId);\r\n  }\r\n\r\n  selectArea(areaId: number, areaName: string) {\r\n    this.selectedAreaName = areaName;\r\n    this.step2Form.patchValue({\r\n      areaId: areaId,\r\n    });\r\n  }\r\n\r\n  selectProjectType(type: string) {\r\n    this.step3Form.patchValue({\r\n      projectType: type,\r\n    });\r\n  }\r\n\r\n  submitForm() {\r\n    this.markCurrentFormAsTouched();\r\n\r\n    if (this.areAllFormsValid()) {\r\n      const httpFormData = new FormData();\r\n\r\n      Object.keys(this.step1Form.value).forEach((key) => {\r\n        httpFormData.append(key, this.step1Form.value[key]);\r\n      });\r\n\r\n      Object.keys(this.step2Form.value).forEach((key) => {\r\n        if (\r\n          this.step2Form.value[key] !== null &&\r\n          this.step2Form.value[key] !== ''\r\n        ) {\r\n          httpFormData.append(key, this.step2Form.value[key]);\r\n        }\r\n      });\r\n\r\n      Object.keys(this.step3Form.value).forEach((key) => {\r\n        httpFormData.append(key, this.step3Form.value[key]);\r\n      });\r\n\r\n      const fileFields = [\r\n        'logoImage',\r\n        'coverImage',\r\n        'masterPlan',\r\n        'gallery',\r\n        'videos',\r\n      ];\r\n      fileFields.forEach((field) => {\r\n        const files = this.step4Form.get(field)?.value;\r\n        const isMultiple = ['gallery', 'videos'].includes(field);\r\n\r\n        if (isMultiple && Array.isArray(files) && files.length > 0) {\r\n          files.forEach((file: File) => {\r\n            if (file instanceof File) {\r\n              httpFormData.append(`${field}[]`, file);\r\n            }\r\n          });\r\n        } else if (!isMultiple && files[0] instanceof File) {\r\n          httpFormData.append(field, files[0]);\r\n        }\r\n      });\r\n\r\n      httpFormData.append('developerId', this.developerId );\r\n\r\n      if (this.projectId !== null) {\r\n        this.projectsService\r\n          .updateProject(this.projectId, httpFormData)\r\n          .subscribe({\r\n            next: (response) => {\r\n              console.log('Project updated successfully:', response);\r\n              this.router.navigate(['/developer/projects']);\r\n            },\r\n            error: (error) => {\r\n              console.error('Error updating project:', error);\r\n            },\r\n          });\r\n      } else {\r\n        console.error('Project ID is null');\r\n      }\r\n    }\r\n  }\r\n\r\n  cancel() {\r\n    this.router.navigate(['/developer/projects']);\r\n  }\r\n\r\n  onFileChange(event: any, fieldName: string) {\r\n    if (event.target.files && event.target.files.length) {\r\n      const files = Array.from(event.target.files);\r\n      this.step4Form.patchValue({\r\n        [fieldName]: files,\r\n      });\r\n\r\n      console.log(`${fieldName}: ${files.length} files selected`);\r\n    }\r\n  }\r\n\r\n  getFileCount(fieldName: string): number {\r\n    const files = this.step4Form.get(fieldName)?.value;\r\n    return files && Array.isArray(files) ? files.length : 0;\r\n  }\r\n\r\n  loadProjectData() {\r\n    if (this.projectId) {\r\n      this.projectsService.getById(this.projectId).subscribe({\r\n        next: (response) => {\r\n          const data = response.data || response;\r\n          console.log('Project data:', data);\r\n\r\n          // Simple direct assignment\r\n          this.existingFiles = {\r\n            logoImage: data.logoImage || null,\r\n            coverImage: data.coverImage || null,\r\n            masterPlan: data.masterPlan || null,\r\n            galleryImages:\r\n              data.gallery?.filter((item: any) => item.type === 'image') || [],\r\n            galleryVideos:\r\n              data.gallery?.filter((item: any) => item.type === 'video') || [],\r\n          };\r\n\r\n          setTimeout(() => {\r\n            this.step1Form.patchValue({\r\n              name: data.name || '',\r\n              designer: data.designer || '',\r\n              projectExecutor: data.projectExecutor || '',\r\n              managementTeam: data.managementTeam || '',\r\n            });\r\n\r\n            // Populate Step 2 form with existing data\r\n            this.step2Form.patchValue({\r\n              cityId: data.city?.id || data.cityId || '',\r\n              areaId: data.area?.id || data.areaId || '',\r\n              address: data.address || '',\r\n              googleMapsLink: data.googleMapUrl || '',\r\n              googleMapUrl: data.googleMapUrl || '',\r\n            });\r\n\r\n            // Set selected values for display\r\n            if (data.city) {\r\n              this.selectedCityId = data.city.id || data.cityId;\r\n              this.selectedCityName = data.city.name_en || data.city.name || '';\r\n              if (this.selectedCityId) {\r\n                this.loadAreas(this.selectedCityId);\r\n              }\r\n            }\r\n            if (data.area) {\r\n              this.selectedAreaName = data.area.name_en || data.area.name || '';\r\n            }\r\n\r\n            // Populate Step 3 form with existing data\r\n            this.step3Form.patchValue({\r\n              projectType: data.projectType || '',\r\n              buildingsCount: data.buildingsCount || 0,\r\n              apartmentsCount: data.apartmentsCount || 0,\r\n              villasCount: data.villasCount || 0,\r\n              duplexCount: data.duplexCount || 0,\r\n              administrativeUnitsCount: data.administrativeUnitsCount || 0,\r\n              commercialUnitsCount: data.commercialUnitsCount || 0,\r\n            });\r\n\r\n            this.cdr.detectChanges();\r\n          }, 100);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading project data:', error);\r\n          alert('Failed to load project data. Please try again later.');\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  removeImage(fieldName: string): void {\r\n    this.existingFiles[fieldName] = null;\r\n\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  removeGalleryItem(type: string, index: number): void {\r\n    if (type === 'image') {\r\n      this.existingFiles.galleryImages.splice(index, 1);\r\n    } else if (type === 'video') {\r\n      this.existingFiles.galleryVideos.splice(index, 1);\r\n    }\r\n\r\n    this.cdr.detectChanges();\r\n  }\r\n}\r\n", "<div class=\"mb-20 mt-0\">\r\n  <app-developer-header\r\n    [title]=\"'Update Project'\"\r\n    [subtitle]=\"'Edit and update existing project information'\"\r\n  >\r\n  </app-developer-header>\r\n</div>\r\n\r\n<div class=\"card rounded-4\">\r\n  <div class=\"card-body p-10\">\r\n    <div\r\n      class=\"stepper stepper-pills d-flex flex-column\"\r\n      id=\"update_property_stepper\"\r\n    >\r\n      <!-- Update Mode Notification -->\r\n      <div class=\"alert alert-warning text-center mb-4\">\r\n        <i class=\"fas fa-edit me-2\"></i>\r\n        <strong>Edit mode :</strong>\r\n        You are in edit mode. Any changes you make will be saved to the existing\r\n        project.\r\n      </div>\r\n\r\n      <!-- Header and Progress Bar -->\r\n      <div class=\"mb-5 text-center\">\r\n        <ng-container *ngIf=\"currentStep === 1\">\r\n          <h2>\r\n            <span class=\"text-dark-blue fw-bold\">Update Project - </span>\r\n            <span class=\"text-dark-blue fw-normal\">Basic Data</span>\r\n          </h2>\r\n        </ng-container>\r\n        <ng-container *ngIf=\"currentStep === 2\">\r\n          <h2>\r\n            <span class=\"text-dark-blue fw-bold\">Update Project - </span>\r\n            <span class=\"text-dark-blue fw-normal\">Location </span>\r\n          </h2>\r\n        </ng-container>\r\n        <ng-container *ngIf=\"currentStep === 3\">\r\n          <h2>\r\n            <span class=\"text-dark-blue fw-bold\">Update Project - </span>\r\n            <span class=\"text-dark-blue fw-normal\"> Project type</span>\r\n          </h2>\r\n        </ng-container>\r\n        <ng-container *ngIf=\"currentStep === 4\">\r\n          <h2>\r\n            <span class=\"text-dark-blue fw-bold\">Update Project - </span>\r\n            <span class=\"text-dark-blue fw-normal\">Project Documents</span>\r\n          </h2>\r\n        </ng-container>\r\n\r\n        <div class=\"d-flex justify-content-center align-items-center mb-2\">\r\n          <span class=\"text-success fw-bold\">Step {{ currentStep }}</span>\r\n          <span class=\"text-muted mx-1\">of</span>\r\n          <span class=\"text-muted\">{{ totalSteps }}</span>\r\n        </div>\r\n\r\n        <div\r\n          *ngIf=\"currentStep > 1\"\r\n          class=\"text-primary cursor-pointer mb-2\"\r\n          (click)=\"prevStep()\"\r\n        >\r\n          Back to previous step\r\n        </div>\r\n\r\n        <div class=\"progress h-8px bg-light-success w-75 mx-auto\">\r\n          <div\r\n            class=\"progress-bar bg-success\"\r\n            role=\"progressbar\"\r\n            [style.width]=\"(currentStep / totalSteps) * 100 + '%'\"\r\n            aria-valuenow=\"50\"\r\n            aria-valuemin=\"0\"\r\n            aria-valuemax=\"100\"\r\n          ></div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Form Content -->\r\n      <form class=\"mx-auto w-100 pt-5 pb-10\">\r\n        <!-- Step 1: Basic Property Settings -->\r\n        <div *ngIf=\"currentStep === 1\" [formGroup]=\"step1Form\">\r\n          <div class=\"mb-10\">\r\n            <label class=\"form-label fw-bold text-start d-block\"\r\n              >Project name\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              class=\"form-control text-start\"\r\n              [class.is-invalid]=\"hasFieldError('name')\"\r\n              formControlName=\"name\"\r\n            />\r\n            <div *ngIf=\"hasFieldError('name')\" class=\"invalid-feedback d-block\">\r\n              {{ getFieldErrorMessage(\"name\") }}\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"mb-10\">\r\n            <label class=\"form-label fw-bold text-start d-block\">\r\n              Project Designer\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              class=\"form-control text-start\"\r\n              [class.is-invalid]=\"hasFieldError('designer')\"\r\n              formControlName=\"designer\"\r\n            />\r\n            <div\r\n              *ngIf=\"hasFieldError('designer')\"\r\n              class=\"invalid-feedback d-block\"\r\n            >\r\n              {{ getFieldErrorMessage(\"designer\") }}\r\n            </div>\r\n          </div>\r\n          <div class=\"mb-10\">\r\n            <label class=\"form-label fw-bold text-start d-block\">\r\n              Project implementer\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              class=\"form-control text-start\"\r\n              [class.is-invalid]=\"hasFieldError('projectExecutor')\"\r\n              formControlName=\"projectExecutor\"\r\n            />\r\n            <div\r\n              *ngIf=\"hasFieldError('projectExecutor')\"\r\n              class=\"invalid-feedback d-block\"\r\n            >\r\n              {{ getFieldErrorMessage(\"projectExecutor\") }}\r\n            </div>\r\n          </div>\r\n          <div class=\"mb-10\">\r\n            <label class=\"form-label fw-bold text-start d-block\">\r\n              Project management\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              class=\"form-control text-start\"\r\n              [class.is-invalid]=\"hasFieldError('managementTeam')\"\r\n              formControlName=\"managementTeam\"\r\n            />\r\n            <div\r\n              *ngIf=\"hasFieldError('managementTeam')\"\r\n              class=\"invalid-feedback d-block\"\r\n            >\r\n              {{ getFieldErrorMessage(\"managementTeam\") }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Step 2: Location Information -->\r\n        <div *ngIf=\"currentStep === 2\" [formGroup]=\"step2Form\">\r\n          <div class=\"mb-10\">\r\n            <label class=\"form-label fw-bold text-start d-block\"> City</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\"\r\n                id=\"cityDropdown\"\r\n                data-bs-toggle=\"dropdown\"\r\n                aria-expanded=\"false\"\r\n              >\r\n                <span>{{ selectedCityName || \"Choose the city\" }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul\r\n                class=\"dropdown-menu w-100\"\r\n                aria-labelledby=\"cityDropdown\"\r\n                style=\"max-height: 300px; overflow-y: auto\"\r\n              >\r\n                <li *ngFor=\"let city of cities\">\r\n                  <a\r\n                    class=\"dropdown-item text-start\"\r\n                    (click)=\"selectCity(city.id, city.name_en || city.name)\"\r\n                  >\r\n                    {{ city.name_en || city.name }}\r\n                  </a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"mb-10\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Area</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\"\r\n                id=\"areaDropdown\"\r\n                data-bs-toggle=\"dropdown\"\r\n                aria-expanded=\"false\"\r\n              >\r\n                <span>{{ selectedAreaName || \"Choose the Area\" }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul\r\n                class=\"dropdown-menu w-100\"\r\n                aria-labelledby=\"areaDropdown\"\r\n                style=\"max-height: 300px; overflow-y: auto\"\r\n              >\r\n                <li *ngFor=\"let area of areas\">\r\n                  <a\r\n                    class=\"dropdown-item text-start\"\r\n                    (click)=\"selectArea(area.id, area.name_en || area.name)\"\r\n                  >\r\n                    {{ area.name_en || area.name }}\r\n                  </a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"mb-10\">\r\n            <label class=\"form-label fw-bold text-start d-block\">\r\n              Details address</label\r\n            >\r\n            <input\r\n              type=\"text\"\r\n              class=\"form-control text-start\"\r\n              [class.is-invalid]=\"hasFieldError('address')\"\r\n              formControlName=\"address\"\r\n              placeholder=\" Enter the address in details\"\r\n            />\r\n            <div\r\n              *ngIf=\"hasFieldError('address')\"\r\n              class=\"invalid-feedback d-block\"\r\n            >\r\n              {{ getFieldErrorMessage(\"address\") }}\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"mb-10\">\r\n            <label class=\"form-label fw-bold text-start d-block\"\r\n              >Website link on Google Maps</label\r\n            >\r\n            <input\r\n              type=\"text\"\r\n              class=\"form-control text-start\"\r\n              [class.is-invalid]=\"hasFieldError('googleMapsLink')\"\r\n              formControlName=\"googleMapsLink\"\r\n              placeholder=\" Enter the map link\"\r\n            />\r\n            <div\r\n              *ngIf=\"hasFieldError('googleMapsLink')\"\r\n              class=\"invalid-feedback d-block\"\r\n            >\r\n              {{ getFieldErrorMessage(\"googleMapsLink\") }}\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Hidden fields -->\r\n          <input type=\"hidden\" formControlName=\"cityId\" />\r\n          <input type=\"hidden\" formControlName=\"areaId\" />\r\n          <input type=\"hidden\" formControlName=\"googleMapUrl\" />\r\n        </div>\r\n\r\n        <!-- Step 3: Project type   -->\r\n        <div *ngIf=\"currentStep === 3\" [formGroup]=\"step3Form\">\r\n          <div class=\"mb-10\">\r\n            <label class=\"form-label fw-bold text-start d-block\">\r\n              Project type\r\n            </label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\"\r\n                id=\"projectTypeDropdown\"\r\n                data-bs-toggle=\"dropdown\"\r\n                aria-expanded=\"false\"\r\n              >\r\n                <span>{{\r\n                  step3Form.get(\"projectType\")?.value || \"commercial...\"\r\n                }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul\r\n                class=\"dropdown-menu w-100\"\r\n                aria-labelledby=\"projectTypeDropdown\"\r\n                style=\"max-height: 300px; overflow-y: auto\"\r\n              >\r\n                <li>\r\n                  <a\r\n                    class=\"dropdown-item text-start\"\r\n                    (click)=\"selectProjectType(' residential')\"\r\n                  >\r\n                    residential</a\r\n                  >\r\n                </li>\r\n                <li>\r\n                  <a\r\n                    class=\"dropdown-item text-start\"\r\n                    (click)=\"selectProjectType('commercial')\"\r\n                    >commercial</a\r\n                  >\r\n                </li>\r\n                <li>\r\n                  <a\r\n                    class=\"dropdown-item text-start\"\r\n                    (click)=\"selectProjectType('mixed')\"\r\n                    >mixed</a\r\n                  >\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"mb-10\">\r\n            <label class=\"form-label fw-bold text-start d-block\">\r\n              Number of project units</label\r\n            >\r\n\r\n            <!-- Buildings Row -->\r\n            <div class=\"d-flex mb-3 align-items-center\">\r\n              <button\r\n                type=\"button\"\r\n                class=\"btn btn-primary me-3\"\r\n                style=\"width: 150px; background-color: #1e1e7c\"\r\n              >\r\n                Buildings\r\n              </button>\r\n              <div class=\"flex-grow-1\">\r\n                <input\r\n                  type=\"number\"\r\n                  class=\"form-control text-start\"\r\n                  [class.is-invalid]=\"hasFieldError('buildingsCount')\"\r\n                  formControlName=\"buildingsCount\"\r\n                  placeholder=\"00\"\r\n                />\r\n                <div\r\n                  *ngIf=\"hasFieldError('buildingsCount')\"\r\n                  class=\"invalid-feedback d-block\"\r\n                >\r\n                  {{ getFieldErrorMessage(\"buildingsCount\") }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Apartments Row -->\r\n            <div class=\"d-flex mb-3 align-items-center\">\r\n              <button\r\n                type=\"button\"\r\n                class=\"btn btn-primary me-3\"\r\n                style=\"width: 150px; background-color: #1e1e7c\"\r\n              >\r\n                Apartments\r\n              </button>\r\n              <div class=\"flex-grow-1\">\r\n                <input\r\n                  type=\"number\"\r\n                  class=\"form-control text-start\"\r\n                  [class.is-invalid]=\"hasFieldError('apartmentsCount')\"\r\n                  formControlName=\"apartmentsCount\"\r\n                  placeholder=\"00\"\r\n                />\r\n                <div\r\n                  *ngIf=\"hasFieldError('apartmentsCount')\"\r\n                  class=\"invalid-feedback d-block\"\r\n                >\r\n                  {{ getFieldErrorMessage(\"apartmentsCount\") }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Villas Row -->\r\n            <div class=\"d-flex mb-3 align-items-center\">\r\n              <button\r\n                type=\"button\"\r\n                class=\"btn btn-primary me-3\"\r\n                style=\"width: 150px; background-color: #1e1e7c\"\r\n              >\r\n                Villas\r\n              </button>\r\n              <div class=\"flex-grow-1\">\r\n                <input\r\n                  type=\"number\"\r\n                  class=\"form-control text-start\"\r\n                  [class.is-invalid]=\"hasFieldError('villasCount')\"\r\n                  formControlName=\"villasCount\"\r\n                  placeholder=\"00\"\r\n                />\r\n                <div\r\n                  *ngIf=\"hasFieldError('villasCount')\"\r\n                  class=\"invalid-feedback d-block\"\r\n                >\r\n                  {{ getFieldErrorMessage(\"villasCount\") }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Duplexes Row -->\r\n            <div class=\"d-flex mb-3 align-items-center\">\r\n              <button\r\n                type=\"button\"\r\n                class=\"btn btn-primary me-3\"\r\n                style=\"width: 150px; background-color: #1e1e7c\"\r\n              >\r\n                Duplexes\r\n              </button>\r\n              <div class=\"flex-grow-1\">\r\n                <input\r\n                  type=\"number\"\r\n                  class=\"form-control text-start\"\r\n                  [class.is-invalid]=\"hasFieldError('duplexCount')\"\r\n                  formControlName=\"duplexCount\"\r\n                  placeholder=\"00\"\r\n                />\r\n                <div\r\n                  *ngIf=\"hasFieldError('duplexCount')\"\r\n                  class=\"invalid-feedback d-block\"\r\n                >\r\n                  {{ getFieldErrorMessage(\"duplexCount\") }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- administrative units count Row -->\r\n            <div class=\"d-flex mb-3 align-items-center\">\r\n              <button\r\n                type=\"button\"\r\n                class=\"btn btn-primary me-3\"\r\n                style=\"width: 150px; background-color: #1e1e7c\"\r\n              >\r\n                Administrative Units\r\n              </button>\r\n              <div class=\"flex-grow-1\">\r\n                <input\r\n                  type=\"number\"\r\n                  class=\"form-control text-start\"\r\n                  [class.is-invalid]=\"hasFieldError('administrativeUnitsCount')\"\r\n                  formControlName=\"administrativeUnitsCount\"\r\n                  placeholder=\"00\"\r\n                />\r\n                <div\r\n                  *ngIf=\"hasFieldError('administrativeUnitsCount')\"\r\n                  class=\"invalid-feedback d-block\"\r\n                >\r\n                  {{ getFieldErrorMessage(\"administrativeUnitsCount\") }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- commercial units count Row-->\r\n            <div class=\"d-flex mb-3 align-items-center\">\r\n              <button\r\n                type=\"button\"\r\n                class=\"btn btn-primary me-3\"\r\n                style=\"width: 150px; background-color: #1e1e7c\"\r\n              >\r\n                Commercial Units\r\n              </button>\r\n              <div class=\"flex-grow-1\">\r\n                <input\r\n                  type=\"number\"\r\n                  class=\"form-control text-start\"\r\n                  [class.is-invalid]=\"hasFieldError('commercialUnitsCount')\"\r\n                  formControlName=\"commercialUnitsCount\"\r\n                  placeholder=\"00\"\r\n                />\r\n                <div\r\n                  *ngIf=\"hasFieldError('commercialUnitsCount')\"\r\n                  class=\"invalid-feedback d-block\"\r\n                >\r\n                  {{ getFieldErrorMessage(\"commercialUnitsCount\") }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Step 4: Project Documents -->\r\n        <div *ngIf=\"currentStep === 4\" [formGroup]=\"step4Form\">\r\n          <!-- Project Documents Cards -->\r\n          <div class=\"mb-10 upload-card-container\">\r\n            <!-- Project Logo -->\r\n            <div class=\"card mb-5 cursor-pointer\">\r\n              <label for=\"projectLogo\" class=\"card-body text-center py-3\">\r\n                <div class=\"upload-icon\">\r\n                  <i class=\"fas fa-arrow-up\"></i>\r\n                </div>\r\n                <span class=\"upload-text\">\r\n                  upload project logo\r\n                  <!-- Show existing files count -->\r\n                  <span\r\n                    *ngIf=\"existingFiles.logoImage\"\r\n                    class=\"badge bg-info ms-2\"\r\n                    title=\"Existing files\"\r\n                  >\r\n                    1 existing\r\n                  </span>\r\n                  <!-- Show new files count -->\r\n                  <span\r\n                    *ngIf=\"getFileCount('logoImage') > 0\"\r\n                    class=\"badge bg-success ms-2\"\r\n                    title=\"New files selected\"\r\n                  >\r\n                    {{ getFileCount(\"logoImage\") }} new\r\n                  </span>\r\n                </span>\r\n                <input\r\n                  type=\"file\"\r\n                  id=\"projectLogo\"\r\n                  class=\"d-none\"\r\n                  (change)=\"onFileChange($event, 'logoImage')\"\r\n                  accept=\"image/*\"\r\n                />\r\n              </label>\r\n\r\n              <!-- Simple existing logo display -->\r\n              <div *ngIf=\"existingFiles?.logoImage\" class=\"mt-3\">\r\n                <p class=\"text-muted mb-2\">Current Logo:</p>\r\n                <div class=\"position-relative d-inline-block\">\r\n                  <img\r\n                    [src]=\"existingFiles.logoImage\"\r\n                    class=\"img-fluid rounded\"\r\n                    style=\"height: 100px; width: auto; max-width: 200px\"\r\n                    alt=\"Current Logo\"\r\n                  />\r\n                  <button\r\n                    type=\"button\"\r\n                    class=\"btn btn-danger btn-sm position-absolute top-0 end-0 m-1\"\r\n                    (click)=\"removeImage('logoImage')\"\r\n                    title=\"Delete\"\r\n                  >\r\n                    <i class=\"fas fa-times\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Project Layout -->\r\n            <div class=\"card mb-5 cursor-pointer\">\r\n              <label for=\"projectLayout\" class=\"card-body text-center py-3\">\r\n                <div class=\"upload-icon\">\r\n                  <i class=\"fas fa-arrow-up\"></i>\r\n                </div>\r\n                <span class=\"upload-text\">\r\n                  upload project layout\r\n                  <!-- Show existing files count -->\r\n                  <span\r\n                    *ngIf=\"existingFiles.coverImage\"\r\n                    class=\"badge bg-info ms-2\"\r\n                    title=\"Existing files\"\r\n                  >\r\n                    1 existing\r\n                  </span>\r\n                  <!-- Show new files count -->\r\n                  <span\r\n                    *ngIf=\"getFileCount('coverImage') > 0\"\r\n                    class=\"badge bg-success ms-2\"\r\n                    title=\"New files selected\"\r\n                  >\r\n                    {{ getFileCount(\"coverImage\") }} new\r\n                  </span>\r\n                </span>\r\n                <input\r\n                  type=\"file\"\r\n                  id=\"projectLayout\"\r\n                  class=\"d-none\"\r\n                  (change)=\"onFileChange($event, 'coverImage')\"\r\n                  accept=\"image/*\"\r\n                />\r\n              </label>\r\n\r\n              <!-- Simple existing cover image display -->\r\n              <div *ngIf=\"existingFiles.coverImage\" class=\"mt-3\">\r\n                <p class=\"text-muted mb-2\">Current Cover Image:</p>\r\n                <div class=\"position-relative d-inline-block\">\r\n                  <img\r\n                    [src]=\"existingFiles.coverImage\"\r\n                    class=\"img-fluid rounded\"\r\n                    style=\"height: 100px; width: auto; max-width: 200px\"\r\n                    alt=\"Current Cover Image\"\r\n                  />\r\n                  <button\r\n                    type=\"button\"\r\n                    class=\"btn btn-danger btn-sm position-absolute top-0 end-0 m-1\"\r\n                    (click)=\"removeImage('coverImage')\"\r\n                    title=\"Delete\"\r\n                  >\r\n                    <i class=\"fas fa-times\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Project Licenses -->\r\n            <div class=\"card mb-5 cursor-pointer\">\r\n              <label for=\"projectLicenses\" class=\"card-body text-center py-3\">\r\n                <div class=\"upload-icon\">\r\n                  <i class=\"fas fa-arrow-up\"></i>\r\n                </div>\r\n                <span class=\"upload-text\">\r\n                  upload project licenses\r\n                  <!-- Show existing files count -->\r\n                  <span\r\n                    *ngIf=\"existingFiles.masterPlan\"\r\n                    class=\"badge bg-info ms-2\"\r\n                    title=\"Existing files\"\r\n                  >\r\n                    1 existing\r\n                  </span>\r\n                  <!-- Show new files count -->\r\n                  <span\r\n                    *ngIf=\"getFileCount('masterPlan') > 0\"\r\n                    class=\"badge bg-success ms-2\"\r\n                    title=\"New files selected\"\r\n                  >\r\n                    {{ getFileCount(\"masterPlan\") }} new\r\n                  </span>\r\n                </span>\r\n                <input\r\n                  type=\"file\"\r\n                  id=\"projectLicenses\"\r\n                  class=\"d-none\"\r\n                  (change)=\"onFileChange($event, 'masterPlan')\"\r\n                  accept=\"image/*\"\r\n                />\r\n              </label>\r\n\r\n              <!-- Simple existing master plan display -->\r\n              <div *ngIf=\"existingFiles.masterPlan\" class=\"mt-3\">\r\n                <p class=\"text-muted mb-2\">Current Master Plan:</p>\r\n                <div class=\"position-relative d-inline-block\">\r\n                  <img\r\n                    [src]=\"existingFiles.masterPlan\"\r\n                    class=\"img-fluid rounded\"\r\n                    style=\"height: 100px; width: auto; max-width: 200px\"\r\n                    alt=\"Current Master Plan\"\r\n                  />\r\n                  <button\r\n                    type=\"button\"\r\n                    class=\"btn btn-danger btn-sm position-absolute top-0 end-0 m-1\"\r\n                    (click)=\"removeImage('masterPlan')\"\r\n                    title=\"Delete\"\r\n                  >\r\n                    <i class=\"fas fa-times\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Project Images -->\r\n            <div class=\"card mb-5 cursor-pointer\">\r\n              <label for=\"projectImages\" class=\"card-body text-center py-3\">\r\n                <div class=\"upload-icon\">\r\n                  <i class=\"fas fa-arrow-up\"></i>\r\n                </div>\r\n                <span class=\"upload-text\">\r\n                  upload project images\r\n                  <!-- Show existing files count -->\r\n                  <span\r\n                    *ngIf=\"existingFiles.galleryImages?.length > 0\"\r\n                    class=\"badge bg-info ms-2\"\r\n                    title=\"Existing files\"\r\n                  >\r\n                    {{ existingFiles.galleryImages.length }} existing\r\n                  </span>\r\n                  <!-- Show new files count -->\r\n                  <span\r\n                    *ngIf=\"getFileCount('gallery') > 0\"\r\n                    class=\"badge bg-success ms-2\"\r\n                    title=\"New files selected\"\r\n                  >\r\n                    {{ getFileCount(\"gallery\") }} new\r\n                  </span>\r\n                </span>\r\n                <input\r\n                  type=\"file\"\r\n                  id=\"projectImages\"\r\n                  class=\"d-none\"\r\n                  (change)=\"onFileChange($event, 'gallery')\"\r\n                  accept=\"image/*\"\r\n                  multiple\r\n                />\r\n              </label>\r\n\r\n              <!-- Simple existing gallery images display -->\r\n              <div *ngIf=\"existingFiles.galleryImages?.length > 0\" class=\"mt-3\">\r\n                <p class=\"text-muted mb-2\">Current Gallery Images:</p>\r\n                <div class=\"row g-2\">\r\n                  <div\r\n                    *ngFor=\"\r\n                      let image of existingFiles.galleryImages;\r\n                      let i = index\r\n                    \"\r\n                    class=\"col-6 col-md-4 col-lg-3\"\r\n                  >\r\n                    <div class=\"position-relative\">\r\n                      <img\r\n                        [src]=\"image.url\"\r\n                        class=\"img-fluid rounded\"\r\n                        style=\"height: 80px; width: 100%; object-fit: cover\"\r\n                        [alt]=\"'Gallery Image ' + (i + 1)\"\r\n                      />\r\n                      <button\r\n                        type=\"button\"\r\n                        class=\"btn btn-danger btn-sm position-absolute top-0 end-0 m-1\"\r\n                        (click)=\"removeGalleryItem('image', i)\"\r\n                        title=\"Delete\"\r\n                      >\r\n                        <i class=\"fas fa-times\"></i>\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Project Videos -->\r\n            <div class=\"card mb-5 cursor-pointer\">\r\n              <label for=\"projectVideos\" class=\"card-body text-center py-3\">\r\n                <div class=\"upload-icon\">\r\n                  <i class=\"fas fa-arrow-up\"></i>\r\n                </div>\r\n                <span class=\"upload-text\">\r\n                  upload project videos\r\n                  <!-- Show existing files count -->\r\n                  <span\r\n                    *ngIf=\"existingFiles.galleryVideos?.length > 0\"\r\n                    class=\"badge bg-info ms-2\"\r\n                    title=\"Existing files\"\r\n                  >\r\n                    {{ existingFiles.galleryVideos.length }} existing\r\n                  </span>\r\n                  <!-- Show new files count -->\r\n                  <span\r\n                    *ngIf=\"getFileCount('videos') > 0\"\r\n                    class=\"badge bg-success ms-2\"\r\n                    title=\"New files selected\"\r\n                  >\r\n                    {{ getFileCount(\"videos\") }} new\r\n                  </span>\r\n                </span>\r\n                <input\r\n                  type=\"file\"\r\n                  id=\"projectVideos\"\r\n                  class=\"d-none\"\r\n                  (change)=\"onFileChange($event, 'videos')\"\r\n                  accept=\"video/*\"\r\n                  multiple\r\n                />\r\n              </label>\r\n\r\n              <!-- Simple existing gallery videos display -->\r\n              <div *ngIf=\"existingFiles.galleryVideos?.length > 0\" class=\"mt-3\">\r\n                <p class=\"text-muted mb-2\">Current Gallery Videos:</p>\r\n                <div class=\"row g-2\">\r\n                  <div\r\n                    *ngFor=\"\r\n                      let video of existingFiles.galleryVideos;\r\n                      let i = index\r\n                    \"\r\n                    class=\"col-6 col-md-4 col-lg-3\"\r\n                  >\r\n                    <div class=\"position-relative\">\r\n                      <div\r\n                        class=\"video-thumbnail rounded d-flex align-items-center justify-content-center\"\r\n                        style=\"\r\n                          height: 80px;\r\n                          width: 100%;\r\n                          background-color: #f8f9fa;\r\n                          border: 1px solid #dee2e6;\r\n                        \"\r\n                      >\r\n                        <i class=\"fas fa-play-circle fa-2x text-primary\"></i>\r\n                      </div>\r\n                      <button\r\n                        type=\"button\"\r\n                        class=\"btn btn-danger btn-sm position-absolute top-0 end-0 m-1\"\r\n                        (click)=\"removeGalleryItem('video', i)\"\r\n                        title=\"Delete\"\r\n                      >\r\n                        <i class=\"fas fa-times\"></i>\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Navigation Buttons -->\r\n        <div\r\n          *ngIf=\"currentStep === 1\"\r\n          class=\"d-flex justify-content-between pt-10\"\r\n        >\r\n          <button\r\n            type=\"button\"\r\n            class=\"btn btn-light-dark btn-lg px-6 py-3\"\r\n            (click)=\"cancel()\"\r\n          >\r\n            Cancel\r\n          </button>\r\n\r\n          <button\r\n            type=\"button\"\r\n            class=\"btn btn-lg btn-navy px-10 py-3 rounded-pill\"\r\n            [disabled]=\"!isCurrentFormValid()\"\r\n            (click)=\"nextStep()\"\r\n          >\r\n            <span class=\"indicator-label text-white\">\r\n              Next - Location Information\r\n            </span>\r\n          </button>\r\n        </div>\r\n\r\n        <!-- ******-->\r\n        <div\r\n          *ngIf=\"currentStep > 1\"\r\n          class=\"d-flex justify-content-center pt-10\"\r\n        >\r\n          <ng-container *ngIf=\"currentStep !== totalSteps\">\r\n            <button\r\n              type=\"button\"\r\n              class=\"btn btn-lg btn-navy px-10 py-3 rounded-pill\"\r\n              [disabled]=\"!isCurrentFormValid()\"\r\n              (click)=\"nextStep()\"\r\n            >\r\n              <span class=\"indicator-label text-white\">\r\n                <ng-container *ngIf=\"currentStep === 2\">\r\n                  Next - Project type\r\n                </ng-container>\r\n                <ng-container *ngIf=\"currentStep === 3\">\r\n                  Next - Project Documents\r\n                </ng-container>\r\n              </span>\r\n            </button>\r\n          </ng-container>\r\n\r\n          <ng-container *ngIf=\"currentStep === totalSteps\">\r\n            <div class=\"d-flex flex-column align-items-center\">\r\n              <button\r\n                type=\"button\"\r\n                class=\"btn btn-lg btn-blue-custom px-10 py-3 rounded-pill mb-3 w-100\"\r\n                [disabled]=\"!isCurrentFormValid()\"\r\n                (click)=\"submitForm()\"\r\n              >\r\n                <span class=\"indicator-label text-white\"> Update Project </span>\r\n              </button>\r\n            </div>\r\n          </ng-container>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAGEA,UAAU,EACVC,mBAAmB,QACd,gBAAgB;AAEvB,SAASC,wBAAwB,QAAQ,mDAAmD;AAC5F,SAASC,YAAY,QAAQ,iBAAiB;;;;;;;;;ICetCC,EAAA,CAAAC,uBAAA,GAAwC;IAEpCD,EADF,CAAAE,cAAA,SAAI,eACmC;IAAAF,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC7DJ,EAAA,CAAAE,cAAA,eAAuC;IAAAF,EAAA,CAAAG,MAAA,iBAAU;IACnDH,EADmD,CAAAI,YAAA,EAAO,EACrD;;;;;;IAEPJ,EAAA,CAAAC,uBAAA,GAAwC;IAEpCD,EADF,CAAAE,cAAA,SAAI,eACmC;IAAAF,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC7DJ,EAAA,CAAAE,cAAA,eAAuC;IAAAF,EAAA,CAAAG,MAAA,gBAAS;IAClDH,EADkD,CAAAI,YAAA,EAAO,EACpD;;;;;;IAEPJ,EAAA,CAAAC,uBAAA,GAAwC;IAEpCD,EADF,CAAAE,cAAA,SAAI,eACmC;IAAAF,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC7DJ,EAAA,CAAAE,cAAA,eAAuC;IAACF,EAAA,CAAAG,MAAA,oBAAY;IACtDH,EADsD,CAAAI,YAAA,EAAO,EACxD;;;;;;IAEPJ,EAAA,CAAAC,uBAAA,GAAwC;IAEpCD,EADF,CAAAE,cAAA,SAAI,eACmC;IAAAF,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC7DJ,EAAA,CAAAE,cAAA,eAAuC;IAAAF,EAAA,CAAAG,MAAA,wBAAiB;IAC1DH,EAD0D,CAAAI,YAAA,EAAO,EAC5D;;;;;;;IASPJ,EAAA,CAAAE,cAAA,cAIC;IADCF,EAAA,CAAAK,UAAA,mBAAAC,6DAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAEpBZ,EAAA,CAAAG,MAAA,8BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IA4BFJ,EAAA,CAAAE,cAAA,cAAoE;IAClEF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAM,oBAAA,cACF;;;;;IAaAf,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAM,oBAAA,kBACF;;;;;IAYAf,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAM,oBAAA,yBACF;;;;;IAYAf,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAM,oBAAA,wBACF;;;;;IA/DAf,EAFJ,CAAAE,cAAA,cAAuD,cAClC,gBAEd;IAAAF,EAAA,CAAAG,MAAA,oBACH;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAAgB,SAAA,gBAKE;IACFhB,EAAA,CAAAiB,UAAA,IAAAC,6CAAA,kBAAoE;IAGtElB,EAAA,CAAAI,YAAA,EAAM;IAGJJ,EADF,CAAAE,cAAA,cAAmB,gBACoC;IACnDF,EAAA,CAAAG,MAAA,yBACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAAgB,SAAA,gBAKE;IACFhB,EAAA,CAAAiB,UAAA,KAAAE,8CAAA,kBAGC;IAGHnB,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAE,cAAA,eAAmB,iBACoC;IACnDF,EAAA,CAAAG,MAAA,6BACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAAgB,SAAA,iBAKE;IACFhB,EAAA,CAAAiB,UAAA,KAAAG,8CAAA,kBAGC;IAGHpB,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAE,cAAA,eAAmB,iBACoC;IACnDF,EAAA,CAAAG,MAAA,4BACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAAgB,SAAA,iBAKE;IACFhB,EAAA,CAAAiB,UAAA,KAAAI,8CAAA,kBAGC;IAILrB,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAnEyBJ,EAAA,CAAAsB,UAAA,cAAAb,MAAA,CAAAc,SAAA,CAAuB;IAQhDvB,EAAA,CAAAa,SAAA,GAA0C;IAA1Cb,EAAA,CAAAwB,WAAA,eAAAf,MAAA,CAAAgB,aAAA,SAA0C;IAGtCzB,EAAA,CAAAa,SAAA,EAA2B;IAA3Bb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAAgB,aAAA,SAA2B;IAY/BzB,EAAA,CAAAa,SAAA,GAA8C;IAA9Cb,EAAA,CAAAwB,WAAA,eAAAf,MAAA,CAAAgB,aAAA,aAA8C;IAI7CzB,EAAA,CAAAa,SAAA,EAA+B;IAA/Bb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAAgB,aAAA,aAA+B;IAahCzB,EAAA,CAAAa,SAAA,GAAqD;IAArDb,EAAA,CAAAwB,WAAA,eAAAf,MAAA,CAAAgB,aAAA,oBAAqD;IAIpDzB,EAAA,CAAAa,SAAA,EAAsC;IAAtCb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAAgB,aAAA,oBAAsC;IAavCzB,EAAA,CAAAa,SAAA,GAAoD;IAApDb,EAAA,CAAAwB,WAAA,eAAAf,MAAA,CAAAgB,aAAA,mBAAoD;IAInDzB,EAAA,CAAAa,SAAA,EAAqC;IAArCb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAAgB,aAAA,mBAAqC;;;;;;IA6BlCzB,EADF,CAAAE,cAAA,SAAgC,YAI7B;IADCF,EAAA,CAAAK,UAAA,mBAAAqB,iEAAA;MAAA,MAAAC,OAAA,GAAA3B,EAAA,CAAAO,aAAA,CAAAqB,GAAA,EAAAC,SAAA;MAAA,MAAApB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAqB,UAAA,CAAAH,OAAA,CAAAI,EAAA,EAAAJ,OAAA,CAAAK,OAAA,IAAAL,OAAA,CAAAM,IAAA,CAA8C;IAAA,EAAC;IAExDjC,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAI,EACD;;;;IAFDJ,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAa,OAAA,CAAAK,OAAA,IAAAL,OAAA,CAAAM,IAAA,MACF;;;;;;IAyBAjC,EADF,CAAAE,cAAA,SAA+B,YAI5B;IADCF,EAAA,CAAAK,UAAA,mBAAA6B,iEAAA;MAAA,MAAAC,OAAA,GAAAnC,EAAA,CAAAO,aAAA,CAAA6B,GAAA,EAAAP,SAAA;MAAA,MAAApB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA4B,UAAA,CAAAF,OAAA,CAAAJ,EAAA,EAAAI,OAAA,CAAAH,OAAA,IAAAG,OAAA,CAAAF,IAAA,CAA8C;IAAA,EAAC;IAExDjC,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAI,EACD;;;;IAFDJ,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAqB,OAAA,CAAAH,OAAA,IAAAG,OAAA,CAAAF,IAAA,MACF;;;;;IAiBNjC,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAM,oBAAA,iBACF;;;;;IAcAf,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAM,oBAAA,wBACF;;;;;IA9FAf,EAFJ,CAAAE,cAAA,cAAuD,cAClC,gBACoC;IAACF,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAS9DJ,EARJ,CAAAE,cAAA,cAAsB,iBAOnB,WACO;IAAAF,EAAA,CAAAG,MAAA,GAA2C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACxDJ,EAAA,CAAAgB,SAAA,YAAmC;IACrChB,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,aAIC;IACCF,EAAA,CAAAiB,UAAA,KAAAqB,6CAAA,iBAAgC;IAUtCtC,EAFI,CAAAI,YAAA,EAAK,EACD,EACF;IAGJJ,EADF,CAAAE,cAAA,eAAmB,iBACoC;IAAAF,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAS7DJ,EARJ,CAAAE,cAAA,eAAsB,kBAOnB,YACO;IAAAF,EAAA,CAAAG,MAAA,IAA2C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACxDJ,EAAA,CAAAgB,SAAA,aAAmC;IACrChB,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,cAIC;IACCF,EAAA,CAAAiB,UAAA,KAAAsB,6CAAA,iBAA+B;IAUrCvC,EAFI,CAAAI,YAAA,EAAK,EACD,EACF;IAGJJ,EADF,CAAAE,cAAA,eAAmB,iBACoC;IACnDF,EAAA,CAAAG,MAAA,wBAAe;IAAAH,EAAA,CAAAI,YAAA,EAChB;IACDJ,EAAA,CAAAgB,SAAA,iBAME;IACFhB,EAAA,CAAAiB,UAAA,KAAAuB,8CAAA,kBAGC;IAGHxC,EAAA,CAAAI,YAAA,EAAM;IAGJJ,EADF,CAAAE,cAAA,eAAmB,iBAEd;IAAAF,EAAA,CAAAG,MAAA,mCAA2B;IAAAH,EAAA,CAAAI,YAAA,EAC7B;IACDJ,EAAA,CAAAgB,SAAA,iBAME;IACFhB,EAAA,CAAAiB,UAAA,KAAAwB,8CAAA,kBAGC;IAGHzC,EAAA,CAAAI,YAAA,EAAM;IAKNJ,EAFA,CAAAgB,SAAA,iBAAgD,iBACA,iBACM;IACxDhB,EAAA,CAAAI,YAAA,EAAM;;;;IAvGyBJ,EAAA,CAAAsB,UAAA,cAAAb,MAAA,CAAAiC,SAAA,CAAuB;IAWxC1C,EAAA,CAAAa,SAAA,GAA2C;IAA3Cb,EAAA,CAAA2C,iBAAA,CAAAlC,MAAA,CAAAmC,gBAAA,sBAA2C;IAQ5B5C,EAAA,CAAAa,SAAA,GAAS;IAATb,EAAA,CAAAsB,UAAA,YAAAb,MAAA,CAAAoC,MAAA,CAAS;IAsBxB7C,EAAA,CAAAa,SAAA,GAA2C;IAA3Cb,EAAA,CAAA2C,iBAAA,CAAAlC,MAAA,CAAAqC,gBAAA,sBAA2C;IAQ5B9C,EAAA,CAAAa,SAAA,GAAQ;IAARb,EAAA,CAAAsB,UAAA,YAAAb,MAAA,CAAAsC,KAAA,CAAQ;IAmB/B/C,EAAA,CAAAa,SAAA,GAA6C;IAA7Cb,EAAA,CAAAwB,WAAA,eAAAf,MAAA,CAAAgB,aAAA,YAA6C;IAK5CzB,EAAA,CAAAa,SAAA,EAA8B;IAA9Bb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAAgB,aAAA,YAA8B;IAc/BzB,EAAA,CAAAa,SAAA,GAAoD;IAApDb,EAAA,CAAAwB,WAAA,eAAAf,MAAA,CAAAgB,aAAA,mBAAoD;IAKnDzB,EAAA,CAAAa,SAAA,EAAqC;IAArCb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAAgB,aAAA,mBAAqC;;;;;IAqFpCzB,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAM,oBAAA,wBACF;;;;;IAqBAf,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAM,oBAAA,yBACF;;;;;IAqBAf,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAM,oBAAA,qBACF;;;;;IAqBAf,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAM,oBAAA,qBACF;;;;;IAqBAf,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAM,oBAAA,kCACF;;;;;IAqBAf,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAM,oBAAA,8BACF;;;;;;IA5MJf,EAFJ,CAAAE,cAAA,cAAuD,cAClC,gBACoC;IACnDF,EAAA,CAAAG,MAAA,qBACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IASJJ,EARJ,CAAAE,cAAA,cAAsB,iBAOnB,WACO;IAAAF,EAAA,CAAAG,MAAA,GAEJ;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACTJ,EAAA,CAAAgB,SAAA,YAAmC;IACrChB,EAAA,CAAAI,YAAA,EAAS;IAOLJ,EANJ,CAAAE,cAAA,aAIC,UACK,aAID;IADCF,EAAA,CAAAK,UAAA,mBAAA2C,4DAAA;MAAAhD,EAAA,CAAAO,aAAA,CAAA0C,GAAA;MAAA,MAAAxC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyC,iBAAA,CAAkB,cAAc,CAAC;IAAA,EAAC;IAE3ClD,EAAA,CAAAG,MAAA,oBAAW;IAEfH,EAFe,CAAAI,YAAA,EACZ,EACE;IAEHJ,EADF,CAAAE,cAAA,UAAI,aAIC;IADDF,EAAA,CAAAK,UAAA,mBAAA8C,4DAAA;MAAAnD,EAAA,CAAAO,aAAA,CAAA0C,GAAA;MAAA,MAAAxC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyC,iBAAA,CAAkB,YAAY,CAAC;IAAA,EAAC;IACxClD,EAAA,CAAAG,MAAA,kBAAU;IAEfH,EAFe,CAAAI,YAAA,EACZ,EACE;IAEHJ,EADF,CAAAE,cAAA,UAAI,aAIC;IADDF,EAAA,CAAAK,UAAA,mBAAA+C,4DAAA;MAAApD,EAAA,CAAAO,aAAA,CAAA0C,GAAA;MAAA,MAAAxC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyC,iBAAA,CAAkB,OAAO,CAAC;IAAA,EAAC;IACnClD,EAAA,CAAAG,MAAA,aAAK;IAKhBH,EALgB,CAAAI,YAAA,EACP,EACE,EACF,EACD,EACF;IAGJJ,EADF,CAAAE,cAAA,eAAmB,iBACoC;IACnDF,EAAA,CAAAG,MAAA,gCAAuB;IAAAH,EAAA,CAAAI,YAAA,EACxB;IAICJ,EADF,CAAAE,cAAA,eAA4C,kBAKzC;IACCF,EAAA,CAAAG,MAAA,mBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,eAAyB;IACvBF,EAAA,CAAAgB,SAAA,iBAME;IACFhB,EAAA,CAAAiB,UAAA,KAAAoC,8CAAA,kBAGC;IAILrD,EADE,CAAAI,YAAA,EAAM,EACF;IAIJJ,EADF,CAAAE,cAAA,eAA4C,kBAKzC;IACCF,EAAA,CAAAG,MAAA,oBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,eAAyB;IACvBF,EAAA,CAAAgB,SAAA,iBAME;IACFhB,EAAA,CAAAiB,UAAA,KAAAqC,8CAAA,kBAGC;IAILtD,EADE,CAAAI,YAAA,EAAM,EACF;IAIJJ,EADF,CAAAE,cAAA,eAA4C,kBAKzC;IACCF,EAAA,CAAAG,MAAA,gBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,eAAyB;IACvBF,EAAA,CAAAgB,SAAA,iBAME;IACFhB,EAAA,CAAAiB,UAAA,KAAAsC,8CAAA,kBAGC;IAILvD,EADE,CAAAI,YAAA,EAAM,EACF;IAIJJ,EADF,CAAAE,cAAA,eAA4C,kBAKzC;IACCF,EAAA,CAAAG,MAAA,kBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,eAAyB;IACvBF,EAAA,CAAAgB,SAAA,iBAME;IACFhB,EAAA,CAAAiB,UAAA,KAAAuC,8CAAA,kBAGC;IAILxD,EADE,CAAAI,YAAA,EAAM,EACF;IAIJJ,EADF,CAAAE,cAAA,eAA4C,kBAKzC;IACCF,EAAA,CAAAG,MAAA,8BACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,eAAyB;IACvBF,EAAA,CAAAgB,SAAA,iBAME;IACFhB,EAAA,CAAAiB,UAAA,KAAAwC,8CAAA,kBAGC;IAILzD,EADE,CAAAI,YAAA,EAAM,EACF;IAIJJ,EADF,CAAAE,cAAA,eAA4C,kBAKzC;IACCF,EAAA,CAAAG,MAAA,0BACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,eAAyB;IACvBF,EAAA,CAAAgB,SAAA,iBAME;IACFhB,EAAA,CAAAiB,UAAA,KAAAyC,8CAAA,kBAGC;IAMT1D,EAHM,CAAAI,YAAA,EAAM,EACF,EACF,EACF;;;;;IAlNyBJ,EAAA,CAAAsB,UAAA,cAAAb,MAAA,CAAAkD,SAAA,CAAuB;IAaxC3D,EAAA,CAAAa,SAAA,GAEJ;IAFIb,EAAA,CAAA2C,iBAAA,GAAAiB,OAAA,GAAAnD,MAAA,CAAAkD,SAAA,CAAAE,GAAA,kCAAAD,OAAA,CAAAE,KAAA,qBAEJ;IAoDA9D,EAAA,CAAAa,SAAA,IAAoD;IAApDb,EAAA,CAAAwB,WAAA,eAAAf,MAAA,CAAAgB,aAAA,mBAAoD;IAKnDzB,EAAA,CAAAa,SAAA,EAAqC;IAArCb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAAgB,aAAA,mBAAqC;IAqBtCzB,EAAA,CAAAa,SAAA,GAAqD;IAArDb,EAAA,CAAAwB,WAAA,eAAAf,MAAA,CAAAgB,aAAA,oBAAqD;IAKpDzB,EAAA,CAAAa,SAAA,EAAsC;IAAtCb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAAgB,aAAA,oBAAsC;IAqBvCzB,EAAA,CAAAa,SAAA,GAAiD;IAAjDb,EAAA,CAAAwB,WAAA,eAAAf,MAAA,CAAAgB,aAAA,gBAAiD;IAKhDzB,EAAA,CAAAa,SAAA,EAAkC;IAAlCb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAAgB,aAAA,gBAAkC;IAqBnCzB,EAAA,CAAAa,SAAA,GAAiD;IAAjDb,EAAA,CAAAwB,WAAA,eAAAf,MAAA,CAAAgB,aAAA,gBAAiD;IAKhDzB,EAAA,CAAAa,SAAA,EAAkC;IAAlCb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAAgB,aAAA,gBAAkC;IAqBnCzB,EAAA,CAAAa,SAAA,GAA8D;IAA9Db,EAAA,CAAAwB,WAAA,eAAAf,MAAA,CAAAgB,aAAA,6BAA8D;IAK7DzB,EAAA,CAAAa,SAAA,EAA+C;IAA/Cb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAAgB,aAAA,6BAA+C;IAqBhDzB,EAAA,CAAAa,SAAA,GAA0D;IAA1Db,EAAA,CAAAwB,WAAA,eAAAf,MAAA,CAAAgB,aAAA,yBAA0D;IAKzDzB,EAAA,CAAAa,SAAA,EAA2C;IAA3Cb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAAgB,aAAA,yBAA2C;;;;;IAuB5CzB,EAAA,CAAAE,cAAA,eAIC;IACCF,EAAA,CAAAG,MAAA,mBACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAEPJ,EAAA,CAAAE,cAAA,eAIC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IADLJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAsD,YAAA,uBACF;;;;;;IAaF/D,EADF,CAAAE,cAAA,cAAmD,YACtB;IAAAF,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC5CJ,EAAA,CAAAE,cAAA,cAA8C;IAC5CF,EAAA,CAAAgB,SAAA,cAKE;IACFhB,EAAA,CAAAE,cAAA,iBAKC;IAFCF,EAAA,CAAAK,UAAA,mBAAA2D,uEAAA;MAAAhE,EAAA,CAAAO,aAAA,CAAA0D,GAAA;MAAA,MAAAxD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyD,WAAA,CAAY,WAAW,CAAC;IAAA,EAAC;IAGlClE,EAAA,CAAAgB,SAAA,YAA4B;IAGlChB,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;;;;IAdAJ,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAsB,UAAA,QAAAb,MAAA,CAAA0D,aAAA,CAAAC,SAAA,EAAApE,EAAA,CAAAqE,aAAA,CAA+B;;;;;IA0BjCrE,EAAA,CAAAE,cAAA,eAIC;IACCF,EAAA,CAAAG,MAAA,mBACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAEPJ,EAAA,CAAAE,cAAA,eAIC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IADLJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAsD,YAAA,wBACF;;;;;;IAaF/D,EADF,CAAAE,cAAA,cAAmD,YACtB;IAAAF,EAAA,CAAAG,MAAA,2BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACnDJ,EAAA,CAAAE,cAAA,cAA8C;IAC5CF,EAAA,CAAAgB,SAAA,cAKE;IACFhB,EAAA,CAAAE,cAAA,iBAKC;IAFCF,EAAA,CAAAK,UAAA,mBAAAiE,uEAAA;MAAAtE,EAAA,CAAAO,aAAA,CAAAgE,IAAA;MAAA,MAAA9D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyD,WAAA,CAAY,YAAY,CAAC;IAAA,EAAC;IAGnClE,EAAA,CAAAgB,SAAA,YAA4B;IAGlChB,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;;;;IAdAJ,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAsB,UAAA,QAAAb,MAAA,CAAA0D,aAAA,CAAAK,UAAA,EAAAxE,EAAA,CAAAqE,aAAA,CAAgC;;;;;IA0BlCrE,EAAA,CAAAE,cAAA,eAIC;IACCF,EAAA,CAAAG,MAAA,mBACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAEPJ,EAAA,CAAAE,cAAA,eAIC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IADLJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAsD,YAAA,wBACF;;;;;;IAaF/D,EADF,CAAAE,cAAA,cAAmD,YACtB;IAAAF,EAAA,CAAAG,MAAA,2BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACnDJ,EAAA,CAAAE,cAAA,cAA8C;IAC5CF,EAAA,CAAAgB,SAAA,cAKE;IACFhB,EAAA,CAAAE,cAAA,iBAKC;IAFCF,EAAA,CAAAK,UAAA,mBAAAoE,uEAAA;MAAAzE,EAAA,CAAAO,aAAA,CAAAmE,IAAA;MAAA,MAAAjE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyD,WAAA,CAAY,YAAY,CAAC;IAAA,EAAC;IAGnClE,EAAA,CAAAgB,SAAA,YAA4B;IAGlChB,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;;;;IAdAJ,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAsB,UAAA,QAAAb,MAAA,CAAA0D,aAAA,CAAAQ,UAAA,EAAA3E,EAAA,CAAAqE,aAAA,CAAgC;;;;;IA0BlCrE,EAAA,CAAAE,cAAA,eAIC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IADLJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAA0D,aAAA,CAAAS,aAAA,CAAAC,MAAA,eACF;;;;;IAEA7E,EAAA,CAAAE,cAAA,eAIC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IADLJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAsD,YAAA,qBACF;;;;;;IAuBE/D,EAPF,CAAAE,cAAA,cAMC,cACgC;IAC7BF,EAAA,CAAAgB,SAAA,cAKE;IACFhB,EAAA,CAAAE,cAAA,iBAKC;IAFCF,EAAA,CAAAK,UAAA,mBAAAyE,6EAAA;MAAA,MAAAC,KAAA,GAAA/E,EAAA,CAAAO,aAAA,CAAAyE,IAAA,EAAAC,KAAA;MAAA,MAAAxE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyE,iBAAA,CAAkB,OAAO,EAAAH,KAAA,CAAI;IAAA,EAAC;IAGvC/E,EAAA,CAAAgB,SAAA,YAA4B;IAGlChB,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;;;;;IAdAJ,EAAA,CAAAa,SAAA,GAAiB;IAGjBb,EAHA,CAAAsB,UAAA,QAAA6D,SAAA,CAAAC,GAAA,EAAApF,EAAA,CAAAqE,aAAA,CAAiB,4BAAAU,KAAA,MAGiB;;;;;IAd1C/E,EADF,CAAAE,cAAA,cAAkE,YACrC;IAAAF,EAAA,CAAAG,MAAA,8BAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACtDJ,EAAA,CAAAE,cAAA,cAAqB;IACnBF,EAAA,CAAAiB,UAAA,IAAAoE,oDAAA,kBAMC;IAmBLrF,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAvBmCJ,EAAA,CAAAa,SAAA,GAEhD;IAFgDb,EAAA,CAAAsB,UAAA,YAAAb,MAAA,CAAA0D,aAAA,CAAAS,aAAA,CAEhD;;;;;IAiCW5E,EAAA,CAAAE,cAAA,eAIC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IADLJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAA0D,aAAA,CAAAmB,aAAA,CAAAT,MAAA,eACF;;;;;IAEA7E,EAAA,CAAAE,cAAA,eAIC;IACCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IADLJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAsD,YAAA,oBACF;;;;;;IAwBI/D,EARJ,CAAAE,cAAA,cAMC,cACgC,cAS5B;IACCF,EAAA,CAAAgB,SAAA,YAAqD;IACvDhB,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBAKC;IAFCF,EAAA,CAAAK,UAAA,mBAAAkF,6EAAA;MAAA,MAAAC,KAAA,GAAAxF,EAAA,CAAAO,aAAA,CAAAkF,IAAA,EAAAR,KAAA;MAAA,MAAAxE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyE,iBAAA,CAAkB,OAAO,EAAAM,KAAA,CAAI;IAAA,EAAC;IAGvCxF,EAAA,CAAAgB,SAAA,YAA4B;IAGlChB,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;;;;;IA9BRJ,EADF,CAAAE,cAAA,cAAkE,YACrC;IAAAF,EAAA,CAAAG,MAAA,8BAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACtDJ,EAAA,CAAAE,cAAA,cAAqB;IACnBF,EAAA,CAAAiB,UAAA,IAAAyE,oDAAA,kBAMC;IAwBL1F,EADE,CAAAI,YAAA,EAAM,EACF;;;;IA5BmCJ,EAAA,CAAAa,SAAA,GAEhD;IAFgDb,EAAA,CAAAsB,UAAA,YAAAb,MAAA,CAAA0D,aAAA,CAAAmB,aAAA,CAEhD;;;;;;IAnRStF,EANR,CAAAE,cAAA,cAAuD,cAEZ,cAED,gBACwB,cACjC;IACvBF,EAAA,CAAAgB,SAAA,YAA+B;IACjChB,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAA0B;IACxBF,EAAA,CAAAG,MAAA,4BACA;IASAH,EARA,CAAAiB,UAAA,IAAA0E,8CAAA,mBAIC,IAAAC,8CAAA,mBAQA;IAGH5F,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAE,cAAA,iBAME;IAFAF,EAAA,CAAAK,UAAA,oBAAAwF,iEAAAC,MAAA;MAAA9F,EAAA,CAAAO,aAAA,CAAAwF,GAAA;MAAA,MAAAtF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAUF,MAAA,CAAAuF,YAAA,CAAAF,MAAA,EAAqB,WAAW,CAAC;IAAA,EAAC;IAGhD9F,EAPE,CAAAI,YAAA,EAME,EACI;IAGRJ,EAAA,CAAAiB,UAAA,KAAAgF,8CAAA,kBAAmD;IAmBrDjG,EAAA,CAAAI,YAAA,EAAM;IAKFJ,EAFJ,CAAAE,cAAA,eAAsC,iBAC0B,eACnC;IACvBF,EAAA,CAAAgB,SAAA,aAA+B;IACjChB,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,gBAA0B;IACxBF,EAAA,CAAAG,MAAA,+BACA;IASAH,EARA,CAAAiB,UAAA,KAAAiF,+CAAA,mBAIC,KAAAC,+CAAA,mBAQA;IAGHnG,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAE,cAAA,iBAME;IAFAF,EAAA,CAAAK,UAAA,oBAAA+F,iEAAAN,MAAA;MAAA9F,EAAA,CAAAO,aAAA,CAAAwF,GAAA;MAAA,MAAAtF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAUF,MAAA,CAAAuF,YAAA,CAAAF,MAAA,EAAqB,YAAY,CAAC;IAAA,EAAC;IAGjD9F,EAPE,CAAAI,YAAA,EAME,EACI;IAGRJ,EAAA,CAAAiB,UAAA,KAAAoF,8CAAA,kBAAmD;IAmBrDrG,EAAA,CAAAI,YAAA,EAAM;IAKFJ,EAFJ,CAAAE,cAAA,eAAsC,iBAC4B,eACrC;IACvBF,EAAA,CAAAgB,SAAA,aAA+B;IACjChB,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,gBAA0B;IACxBF,EAAA,CAAAG,MAAA,iCACA;IASAH,EARA,CAAAiB,UAAA,KAAAqF,+CAAA,mBAIC,KAAAC,+CAAA,mBAQA;IAGHvG,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAE,cAAA,iBAME;IAFAF,EAAA,CAAAK,UAAA,oBAAAmG,iEAAAV,MAAA;MAAA9F,EAAA,CAAAO,aAAA,CAAAwF,GAAA;MAAA,MAAAtF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAUF,MAAA,CAAAuF,YAAA,CAAAF,MAAA,EAAqB,YAAY,CAAC;IAAA,EAAC;IAGjD9F,EAPE,CAAAI,YAAA,EAME,EACI;IAGRJ,EAAA,CAAAiB,UAAA,KAAAwF,8CAAA,kBAAmD;IAmBrDzG,EAAA,CAAAI,YAAA,EAAM;IAKFJ,EAFJ,CAAAE,cAAA,eAAsC,iBAC0B,eACnC;IACvBF,EAAA,CAAAgB,SAAA,aAA+B;IACjChB,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,gBAA0B;IACxBF,EAAA,CAAAG,MAAA,+BACA;IASAH,EARA,CAAAiB,UAAA,KAAAyF,+CAAA,mBAIC,KAAAC,+CAAA,mBAQA;IAGH3G,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAE,cAAA,iBAOE;IAHAF,EAAA,CAAAK,UAAA,oBAAAuG,iEAAAd,MAAA;MAAA9F,EAAA,CAAAO,aAAA,CAAAwF,GAAA;MAAA,MAAAtF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAUF,MAAA,CAAAuF,YAAA,CAAAF,MAAA,EAAqB,SAAS,CAAC;IAAA,EAAC;IAI9C9F,EARE,CAAAI,YAAA,EAOE,EACI;IAGRJ,EAAA,CAAAiB,UAAA,KAAA4F,8CAAA,kBAAkE;IA6BpE7G,EAAA,CAAAI,YAAA,EAAM;IAKFJ,EAFJ,CAAAE,cAAA,eAAsC,iBAC0B,eACnC;IACvBF,EAAA,CAAAgB,SAAA,aAA+B;IACjChB,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,gBAA0B;IACxBF,EAAA,CAAAG,MAAA,+BACA;IASAH,EARA,CAAAiB,UAAA,KAAA6F,+CAAA,mBAIC,KAAAC,+CAAA,mBAQA;IAGH/G,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAE,cAAA,iBAOE;IAHAF,EAAA,CAAAK,UAAA,oBAAA2G,iEAAAlB,MAAA;MAAA9F,EAAA,CAAAO,aAAA,CAAAwF,GAAA;MAAA,MAAAtF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAUF,MAAA,CAAAuF,YAAA,CAAAF,MAAA,EAAqB,QAAQ,CAAC;IAAA,EAAC;IAI7C9F,EARE,CAAAI,YAAA,EAOE,EACI;IAGRJ,EAAA,CAAAiB,UAAA,KAAAgG,8CAAA,kBAAkE;IAoCxEjH,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;IAtTyBJ,EAAA,CAAAsB,UAAA,cAAAb,MAAA,CAAAyG,SAAA,CAAuB;IAazClH,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAA0D,aAAA,CAAAC,SAAA,CAA6B;IAQ7BpE,EAAA,CAAAa,SAAA,EAAmC;IAAnCb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAAsD,YAAA,kBAAmC;IAiBpC/D,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAA0D,aAAA,kBAAA1D,MAAA,CAAA0D,aAAA,CAAAC,SAAA,CAA8B;IA+B7BpE,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAA0D,aAAA,CAAAK,UAAA,CAA8B;IAQ9BxE,EAAA,CAAAa,SAAA,EAAoC;IAApCb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAAsD,YAAA,mBAAoC;IAiBrC/D,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAA0D,aAAA,CAAAK,UAAA,CAA8B;IA+B7BxE,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAA0D,aAAA,CAAAQ,UAAA,CAA8B;IAQ9B3E,EAAA,CAAAa,SAAA,EAAoC;IAApCb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAAsD,YAAA,mBAAoC;IAiBrC/D,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAA0D,aAAA,CAAAQ,UAAA,CAA8B;IA+B7B3E,EAAA,CAAAa,SAAA,GAA6C;IAA7Cb,EAAA,CAAAsB,UAAA,UAAAb,MAAA,CAAA0D,aAAA,CAAAS,aAAA,kBAAAnE,MAAA,CAAA0D,aAAA,CAAAS,aAAA,CAAAC,MAAA,MAA6C;IAQ7C7E,EAAA,CAAAa,SAAA,EAAiC;IAAjCb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAAsD,YAAA,gBAAiC;IAkBlC/D,EAAA,CAAAa,SAAA,GAA6C;IAA7Cb,EAAA,CAAAsB,UAAA,UAAAb,MAAA,CAAA0D,aAAA,CAAAS,aAAA,kBAAAnE,MAAA,CAAA0D,aAAA,CAAAS,aAAA,CAAAC,MAAA,MAA6C;IAyC5C7E,EAAA,CAAAa,SAAA,GAA6C;IAA7Cb,EAAA,CAAAsB,UAAA,UAAAb,MAAA,CAAA0D,aAAA,CAAAmB,aAAA,kBAAA7E,MAAA,CAAA0D,aAAA,CAAAmB,aAAA,CAAAT,MAAA,MAA6C;IAQ7C7E,EAAA,CAAAa,SAAA,EAAgC;IAAhCb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAAsD,YAAA,eAAgC;IAkBjC/D,EAAA,CAAAa,SAAA,GAA6C;IAA7Cb,EAAA,CAAAsB,UAAA,UAAAb,MAAA,CAAA0D,aAAA,CAAAmB,aAAA,kBAAA7E,MAAA,CAAA0D,aAAA,CAAAmB,aAAA,CAAAT,MAAA,MAA6C;;;;;;IA2CvD7E,EAJF,CAAAE,cAAA,cAGC,iBAKE;IADCF,EAAA,CAAAK,UAAA,mBAAA8G,gEAAA;MAAAnH,EAAA,CAAAO,aAAA,CAAA6G,IAAA;MAAA,MAAA3G,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA4G,MAAA,EAAQ;IAAA,EAAC;IAElBrH,EAAA,CAAAG,MAAA,eACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAETJ,EAAA,CAAAE,cAAA,iBAKC;IADCF,EAAA,CAAAK,UAAA,mBAAAiH,gEAAA;MAAAtH,EAAA,CAAAO,aAAA,CAAA6G,IAAA;MAAA,MAAA3G,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA8G,QAAA,EAAU;IAAA,EAAC;IAEpBvH,EAAA,CAAAE,cAAA,eAAyC;IACvCF,EAAA,CAAAG,MAAA,oCACF;IAEJH,EAFI,CAAAI,YAAA,EAAO,EACA,EACL;;;;IAPFJ,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAAsB,UAAA,cAAAb,MAAA,CAAA+G,kBAAA,GAAkC;;;;;IAsB9BxH,EAAA,CAAAC,uBAAA,GAAwC;IACtCD,EAAA,CAAAG,MAAA,4BACF;;;;;;IACAH,EAAA,CAAAC,uBAAA,GAAwC;IACtCD,EAAA,CAAAG,MAAA,iCACF;;;;;;;IAbNH,EAAA,CAAAC,uBAAA,GAAiD;IAC/CD,EAAA,CAAAE,cAAA,iBAKC;IADCF,EAAA,CAAAK,UAAA,mBAAAoH,+EAAA;MAAAzH,EAAA,CAAAO,aAAA,CAAAmH,IAAA;MAAA,MAAAjH,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA8G,QAAA,EAAU;IAAA,EAAC;IAEpBvH,EAAA,CAAAE,cAAA,eAAyC;IAIvCF,EAHA,CAAAiB,UAAA,IAAA0G,qEAAA,0BAAwC,IAAAC,qEAAA,0BAGA;IAI5C5H,EADE,CAAAI,YAAA,EAAO,EACA;;;;;IAXPJ,EAAA,CAAAa,SAAA,EAAkC;IAAlCb,EAAA,CAAAsB,UAAA,cAAAb,MAAA,CAAA+G,kBAAA,GAAkC;IAIjBxH,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAAoH,WAAA,OAAuB;IAGvB7H,EAAA,CAAAa,SAAA,EAAuB;IAAvBb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAAoH,WAAA,OAAuB;;;;;;IAO5C7H,EAAA,CAAAC,uBAAA,GAAiD;IAE7CD,EADF,CAAAE,cAAA,cAAmD,iBAMhD;IADCF,EAAA,CAAAK,UAAA,mBAAAyH,+EAAA;MAAA9H,EAAA,CAAAO,aAAA,CAAAwH,IAAA;MAAA,MAAAtH,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuH,UAAA,EAAY;IAAA,EAAC;IAEtBhI,EAAA,CAAAE,cAAA,eAAyC;IAACF,EAAA,CAAAG,MAAA,uBAAe;IAE7DH,EAF6D,CAAAI,YAAA,EAAO,EACzD,EACL;;;;;IALFJ,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAAsB,UAAA,cAAAb,MAAA,CAAA+G,kBAAA,GAAkC;;;;;IA3B1CxH,EAAA,CAAAE,cAAA,cAGC;IAmBCF,EAlBA,CAAAiB,UAAA,IAAAgH,sDAAA,0BAAiD,IAAAC,sDAAA,0BAkBA;IAYnDlI,EAAA,CAAAI,YAAA,EAAM;;;;IA9BWJ,EAAA,CAAAa,SAAA,EAAgC;IAAhCb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAAoH,WAAA,KAAApH,MAAA,CAAA0H,UAAA,CAAgC;IAkBhCnI,EAAA,CAAAa,SAAA,EAAgC;IAAhCb,EAAA,CAAAsB,UAAA,SAAAb,MAAA,CAAAoH,WAAA,KAAApH,MAAA,CAAA0H,UAAA,CAAgC;;;ADxyBzD,OAAM,MAAOC,uBAAuB;EAmBxBC,EAAA;EACAC,MAAA;EACAC,KAAA;EACAC,eAAA;EACAC,GAAA;EAtBVN,UAAU,GAAG,CAAC;EACdN,WAAW,GAAG,CAAC;EACfa,SAAS,GAAkB,IAAI;EAC/BvE,aAAa,GAAQ,EAAE;EAEvB5C,SAAS;EACTmB,SAAS;EACTiB,SAAS;EACTuD,SAAS;EAETrE,MAAM,GAAU,EAAE;EAClBE,KAAK,GAAU,EAAE;EACjB4F,WAAW;EACXC,cAAc,GAAkB,IAAI;EACpChG,gBAAgB,GAAW,EAAE;EAC7BE,gBAAgB,GAAW,EAAE;EAE7B+F,YACUR,EAAe,EACfC,MAAc,EACdC,KAAqB,EACrBC,eAAgC,EAChCC,GAAsB;IAJtB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,GAAG,GAAHA,GAAG;EACV;EAEHK,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACC,UAAU,EAAE;IAEjB,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACpD,IAAIC,IAAI,GAAGH,QAAQ,GAAGI,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC,GAAG,IAAI;IACjD,IAAI,CAACN,WAAW,GAAGS,IAAI,EAAET,WAAW;IAEpC,IAAI,CAACJ,KAAK,CAACgB,WAAW,CAACC,SAAS,CAAEC,MAAM,IAAI;MAC1C,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;QAChB,IAAI,CAACf,SAAS,GAAG,CAACe,MAAM,CAAC,IAAI,CAAC;QAC9B,IAAI,CAACC,eAAe,EAAE;MACxB,CAAC,MAAM;QACL,IAAI,CAACpB,MAAM,CAACqB,QAAQ,CAAC,CAAC,qBAAqB,CAAC,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ;EAEAZ,SAASA,CAAA;IACP;IACA,IAAI,CAACxH,SAAS,GAAG,IAAI,CAAC8G,EAAE,CAACuB,KAAK,CAAC;MAC7B3H,IAAI,EAAE,CACJ,EAAE,EACF,CACErC,UAAU,CAACiK,QAAQ,EACnBjK,UAAU,CAACkK,SAAS,CAAC,CAAC,CAAC,EACvBlK,UAAU,CAACmK,SAAS,CAAC,GAAG,CAAC,CAC1B,CACF;MACDC,QAAQ,EAAE,CACR,EAAE,EACF,CACEpK,UAAU,CAACiK,QAAQ,EACnBjK,UAAU,CAACkK,SAAS,CAAC,CAAC,CAAC,EACvBlK,UAAU,CAACmK,SAAS,CAAC,EAAE,CAAC,CACzB,CACF;MACDE,eAAe,EAAE,CACf,EAAE,EACF,CACErK,UAAU,CAACiK,QAAQ,EACnBjK,UAAU,CAACkK,SAAS,CAAC,CAAC,CAAC,EACvBlK,UAAU,CAACmK,SAAS,CAAC,EAAE,CAAC,CACzB,CACF;MACDG,cAAc,EAAE,CACd,EAAE,EACF,CACEtK,UAAU,CAACiK,QAAQ,EACnBjK,UAAU,CAACkK,SAAS,CAAC,CAAC,CAAC,EACvBlK,UAAU,CAACmK,SAAS,CAAC,EAAE,CAAC,CACzB;KAEJ,CAAC;IAEF;IACA,IAAI,CAACrH,SAAS,GAAG,IAAI,CAAC2F,EAAE,CAACuB,KAAK,CAAC;MAC7BO,MAAM,EAAE,CAAC,EAAE,EAAEvK,UAAU,CAACiK,QAAQ,CAAC;MACjCO,MAAM,EAAE,CAAC,EAAE,EAAExK,UAAU,CAACiK,QAAQ,CAAC;MACjCQ,OAAO,EAAE,CACP,EAAE,EACF,CACEzK,UAAU,CAACiK,QAAQ,EACnBjK,UAAU,CAACkK,SAAS,CAAC,EAAE,CAAC,EACxBlK,UAAU,CAACmK,SAAS,CAAC,GAAG,CAAC,CAC1B,CACF;MACDO,cAAc,EAAE,CAAC,EAAE,EAAE,CAAC1K,UAAU,CAAC2K,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;MACzDC,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC5K,UAAU,CAAC2K,OAAO,CAAC,aAAa,CAAC,CAAC;KACvD,CAAC;IAEF;IACA,IAAI,CAAC5G,SAAS,GAAG,IAAI,CAAC0E,EAAE,CAACuB,KAAK,CAAC;MAC7Ba,WAAW,EAAE,CAAC,EAAE,EAAE7K,UAAU,CAACiK,QAAQ,CAAC;MACtCa,cAAc,EAAE,CACd,EAAE,EACF,CACE9K,UAAU,CAACiK,QAAQ,EACnBjK,UAAU,CAAC+K,GAAG,CAAC,CAAC,CAAC,EACjB/K,UAAU,CAAC2K,OAAO,CAAC,UAAU,CAAC,CAC/B,CACF;MACDK,eAAe,EAAE,CACf,EAAE,EACF,CACEhL,UAAU,CAACiK,QAAQ,EACnBjK,UAAU,CAAC+K,GAAG,CAAC,CAAC,CAAC,EACjB/K,UAAU,CAAC2K,OAAO,CAAC,UAAU,CAAC,CAC/B,CACF;MACDM,WAAW,EAAE,CACX,EAAE,EACF,CACEjL,UAAU,CAACiK,QAAQ,EACnBjK,UAAU,CAAC+K,GAAG,CAAC,CAAC,CAAC,EACjB/K,UAAU,CAAC2K,OAAO,CAAC,UAAU,CAAC,CAC/B,CACF;MACDO,WAAW,EAAE,CACX,EAAE,EACF,CACElL,UAAU,CAACiK,QAAQ,EACnBjK,UAAU,CAAC+K,GAAG,CAAC,CAAC,CAAC,EACjB/K,UAAU,CAAC2K,OAAO,CAAC,UAAU,CAAC,CAC/B,CACF;MACDQ,wBAAwB,EAAE,CACxB,EAAE,EACF,CACEnL,UAAU,CAACiK,QAAQ,EACnBjK,UAAU,CAAC+K,GAAG,CAAC,CAAC,CAAC,EACjB/K,UAAU,CAAC2K,OAAO,CAAC,UAAU,CAAC,CAC/B,CACF;MACDS,oBAAoB,EAAE,CACpB,EAAE,EACF,CACEpL,UAAU,CAACiK,QAAQ,EACnBjK,UAAU,CAAC+K,GAAG,CAAC,CAAC,CAAC,EACjB/K,UAAU,CAAC2K,OAAO,CAAC,UAAU,CAAC,CAC/B;KAEJ,CAAC;IAEF;IACA,IAAI,CAACrD,SAAS,GAAG,IAAI,CAACmB,EAAE,CAACuB,KAAK,CAAC;MAC7BxF,SAAS,EAAE,CAAC,EAAE,CAAC;MACfI,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBG,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBsG,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,MAAM,EAAE,CAAC,EAAE;KACZ,CAAC;EACJ;EAEAC,cAAcA,CAAA;IACZ,QAAQ,IAAI,CAACtD,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACtG,SAAS;MACvB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACmB,SAAS;MACvB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACiB,SAAS;MACvB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACuD,SAAS;MACvB;QACE,OAAO,IAAI,CAAC3F,SAAS;IACzB;EACF;EAEAiG,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAAC2D,cAAc,EAAE,CAACC,KAAK;EACpC;EAEA3J,aAAaA,CAAC4J,SAAiB;IAC7B,MAAMC,IAAI,GAAG,IAAI,CAACH,cAAc,EAAE;IAClC,MAAMI,KAAK,GAAGD,IAAI,CAACzH,GAAG,CAACwH,SAAS,CAAC;IACjC,OAAOE,KAAK,GAAGA,KAAK,CAACC,OAAO,KAAKD,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACG,OAAO,CAAC,GAAG,KAAK;EACxE;EAEA3K,oBAAoBA,CAACsK,SAAiB;IACpC,MAAME,KAAK,GAAG,IAAI,CAACJ,cAAc,EAAE,CAACtH,GAAG,CAACwH,SAAS,CAAC;IAElD,IAAI,CAACE,KAAK,EAAEI,MAAM,EAAE,OAAO,EAAE;IAE7B,IAAIJ,KAAK,CAACI,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,UAAU;IAC/C,IAAIJ,KAAK,CAACI,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,WAAW;IACjD,IAAIJ,KAAK,CAACI,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,UAAU;IAChD,IAAIJ,KAAK,CAACI,MAAM,CAAC,KAAK,CAAC,EAAE,OAAO,gBAAgB;IAChD,IAAIJ,KAAK,CAACI,MAAM,CAAC,SAAS,CAAC,EAAE,OAAO,gBAAgB;IAEpD,OAAO,SAAS;EAClB;EAEAC,wBAAwBA,CAAA;IACtB,MAAMN,IAAI,GAAG,IAAI,CAACH,cAAc,EAAE;IAClCU,MAAM,CAACC,IAAI,CAACR,IAAI,CAACS,QAAQ,CAAC,CAACC,OAAO,CAAEC,GAAG,IAAI;MACzCX,IAAI,CAACzH,GAAG,CAACoI,GAAG,CAAC,EAAEC,aAAa,EAAE;IAChC,CAAC,CAAC;EACJ;EAEAC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC5K,SAAS,CAAC6J,KAAK,IAAI,IAAI,CAAC1I,SAAS,CAAC0I,KAAK,IAAI,IAAI,CAACzH,SAAS,CAACyH,KAAK;EAC7E;EAEA7D,QAAQA,CAAA;IACN,IAAI,CAACqE,wBAAwB,EAAE;IAE/B,IAAI,CAAC,IAAI,CAACpE,kBAAkB,EAAE,EAAE;MAC9B;IACF;IAEA,IAAI,IAAI,CAACK,WAAW,GAAG,IAAI,CAACM,UAAU,EAAE;MACtC,IAAI,CAACN,WAAW,EAAE;IACpB;EACF;EAEAjH,QAAQA,CAAA;IACN,IAAI,IAAI,CAACiH,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAmB,UAAUA,CAAA;IACR,IAAI,CAACR,eAAe,CAAC4D,SAAS,EAAE,CAAC5C,SAAS,CAAC;MACzC6C,IAAI,EAAGC,QAAa,IAAI;QACtBC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,QAAQ,CAAC;QAEzC,IAAIA,QAAQ,IAAIA,QAAQ,CAACG,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACL,QAAQ,CAACG,IAAI,CAAC,EAAE;UAC7D,IAAI,CAAC5J,MAAM,GAAGyJ,QAAQ,CAACG,IAAI;QAC7B,CAAC,MAAM,IAAIH,QAAQ,IAAII,KAAK,CAACC,OAAO,CAACL,QAAQ,CAAC,EAAE;UAC9C,IAAI,CAACzJ,MAAM,GAAGyJ,QAAQ;QACxB,CAAC,MAAM,IAAIA,QAAQ,IAAIA,QAAQ,CAACzJ,MAAM,EAAE;UACtC,IAAI,CAACA,MAAM,GAAGyJ,QAAQ,CAACzJ,MAAM;QAC/B,CAAC,MAAM;UACL0J,OAAO,CAACK,IAAI,CAAC,6BAA6B,EAAEN,QAAQ,CAAC;UACrD,IAAI,CAACzJ,MAAM,GAAG,EAAE;QAClB;MACF,CAAC;MACDgK,KAAK,EAAGC,GAAQ,IAAI;QAClBP,OAAO,CAACM,KAAK,CAAC,uBAAuB,EAAEC,GAAG,CAAC;MAC7C,CAAC;MACDC,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACtE,GAAG,CAACuE,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EAEAC,SAASA,CAAC9C,MAAe;IACvB,IAAI,CAAC3B,eAAe,CAAC0E,QAAQ,CAAC/C,MAAM,CAAC,CAACX,SAAS,CAAC;MAC9C6C,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACG,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACL,QAAQ,CAACG,IAAI,CAAC,EAAE;UAC7D,IAAI,CAAC1J,KAAK,GAAGuJ,QAAQ,CAACG,IAAI;QAC5B,CAAC,MAAM,IAAIH,QAAQ,IAAII,KAAK,CAACC,OAAO,CAACL,QAAQ,CAAC,EAAE;UAC9C,IAAI,CAACvJ,KAAK,GAAGuJ,QAAQ;QACvB,CAAC,MAAM,IAAIA,QAAQ,IAAIA,QAAQ,CAACvJ,KAAK,EAAE;UACrC,IAAI,CAACA,KAAK,GAAGuJ,QAAQ,CAACvJ,KAAK;QAC7B,CAAC,MAAM;UACL,IAAI,CAACA,KAAK,GAAG,EAAE;QACjB;MACF,CAAC;MACD8J,KAAK,EAAGC,GAAQ,IAAI;QAClBP,OAAO,CAACM,KAAK,CAAC,sBAAsB,EAAEC,GAAG,CAAC;QAC1C,IAAI,CAAC/J,KAAK,GAAG,EAAE;MACjB,CAAC;MACDgK,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACtE,GAAG,CAACuE,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EAEAlL,UAAUA,CAACqI,MAAc,EAAEgD,QAAgB;IACzC,IAAI,CAACvE,cAAc,GAAGuB,MAAM;IAC5B,IAAI,CAACvH,gBAAgB,GAAGuK,QAAQ;IAChC,IAAI,CAACzK,SAAS,CAAC0K,UAAU,CAAC;MACxBjD,MAAM,EAAEA;KACT,CAAC;IACF,IAAI,CAAC8C,SAAS,CAAC9C,MAAM,CAAC;EACxB;EAEA9H,UAAUA,CAAC+H,MAAc,EAAEiD,QAAgB;IACzC,IAAI,CAACvK,gBAAgB,GAAGuK,QAAQ;IAChC,IAAI,CAAC3K,SAAS,CAAC0K,UAAU,CAAC;MACxBhD,MAAM,EAAEA;KACT,CAAC;EACJ;EAEAlH,iBAAiBA,CAACoK,IAAY;IAC5B,IAAI,CAAC3J,SAAS,CAACyJ,UAAU,CAAC;MACxB3C,WAAW,EAAE6C;KACd,CAAC;EACJ;EAEAtF,UAAUA,CAAA;IACR,IAAI,CAAC4D,wBAAwB,EAAE;IAE/B,IAAI,IAAI,CAACO,gBAAgB,EAAE,EAAE;MAC3B,MAAMoB,YAAY,GAAG,IAAIC,QAAQ,EAAE;MAEnC3B,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvK,SAAS,CAACuC,KAAK,CAAC,CAACkI,OAAO,CAAEC,GAAG,IAAI;QAChDsB,YAAY,CAACE,MAAM,CAACxB,GAAG,EAAE,IAAI,CAAC1K,SAAS,CAACuC,KAAK,CAACmI,GAAG,CAAC,CAAC;MACrD,CAAC,CAAC;MAEFJ,MAAM,CAACC,IAAI,CAAC,IAAI,CAACpJ,SAAS,CAACoB,KAAK,CAAC,CAACkI,OAAO,CAAEC,GAAG,IAAI;QAChD,IACE,IAAI,CAACvJ,SAAS,CAACoB,KAAK,CAACmI,GAAG,CAAC,KAAK,IAAI,IAClC,IAAI,CAACvJ,SAAS,CAACoB,KAAK,CAACmI,GAAG,CAAC,KAAK,EAAE,EAChC;UACAsB,YAAY,CAACE,MAAM,CAACxB,GAAG,EAAE,IAAI,CAACvJ,SAAS,CAACoB,KAAK,CAACmI,GAAG,CAAC,CAAC;QACrD;MACF,CAAC,CAAC;MAEFJ,MAAM,CAACC,IAAI,CAAC,IAAI,CAACnI,SAAS,CAACG,KAAK,CAAC,CAACkI,OAAO,CAAEC,GAAG,IAAI;QAChDsB,YAAY,CAACE,MAAM,CAACxB,GAAG,EAAE,IAAI,CAACtI,SAAS,CAACG,KAAK,CAACmI,GAAG,CAAC,CAAC;MACrD,CAAC,CAAC;MAEF,MAAMyB,UAAU,GAAG,CACjB,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,SAAS,EACT,QAAQ,CACT;MACDA,UAAU,CAAC1B,OAAO,CAAET,KAAK,IAAI;QAC3B,MAAMoC,KAAK,GAAG,IAAI,CAACzG,SAAS,CAACrD,GAAG,CAAC0H,KAAK,CAAC,EAAEzH,KAAK;QAC9C,MAAM8J,UAAU,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAACtC,KAAK,CAAC;QAExD,IAAIqC,UAAU,IAAIlB,KAAK,CAACC,OAAO,CAACgB,KAAK,CAAC,IAAIA,KAAK,CAAC9I,MAAM,GAAG,CAAC,EAAE;UAC1D8I,KAAK,CAAC3B,OAAO,CAAE8B,IAAU,IAAI;YAC3B,IAAIA,IAAI,YAAYC,IAAI,EAAE;cACxBR,YAAY,CAACE,MAAM,CAAC,GAAGlC,KAAK,IAAI,EAAEuC,IAAI,CAAC;YACzC;UACF,CAAC,CAAC;QACJ,CAAC,MAAM,IAAI,CAACF,UAAU,IAAID,KAAK,CAAC,CAAC,CAAC,YAAYI,IAAI,EAAE;UAClDR,YAAY,CAACE,MAAM,CAAClC,KAAK,EAAEoC,KAAK,CAAC,CAAC,CAAC,CAAC;QACtC;MACF,CAAC,CAAC;MAEFJ,YAAY,CAACE,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC9E,WAAW,CAAE;MAErD,IAAI,IAAI,CAACD,SAAS,KAAK,IAAI,EAAE;QAC3B,IAAI,CAACF,eAAe,CACjBwF,aAAa,CAAC,IAAI,CAACtF,SAAS,EAAE6E,YAAY,CAAC,CAC3C/D,SAAS,CAAC;UACT6C,IAAI,EAAGC,QAAQ,IAAI;YACjBC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEF,QAAQ,CAAC;YACtD,IAAI,CAAChE,MAAM,CAACqB,QAAQ,CAAC,CAAC,qBAAqB,CAAC,CAAC;UAC/C,CAAC;UACDkD,KAAK,EAAGA,KAAK,IAAI;YACfN,OAAO,CAACM,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UACjD;SACD,CAAC;MACN,CAAC,MAAM;QACLN,OAAO,CAACM,KAAK,CAAC,oBAAoB,CAAC;MACrC;IACF;EACF;EAEAxF,MAAMA,CAAA;IACJ,IAAI,CAACiB,MAAM,CAACqB,QAAQ,CAAC,CAAC,qBAAqB,CAAC,CAAC;EAC/C;EAEA3D,YAAYA,CAACiI,KAAU,EAAE5C,SAAiB;IACxC,IAAI4C,KAAK,CAACC,MAAM,CAACP,KAAK,IAAIM,KAAK,CAACC,MAAM,CAACP,KAAK,CAAC9I,MAAM,EAAE;MACnD,MAAM8I,KAAK,GAAGjB,KAAK,CAACyB,IAAI,CAACF,KAAK,CAACC,MAAM,CAACP,KAAK,CAAC;MAC5C,IAAI,CAACzG,SAAS,CAACkG,UAAU,CAAC;QACxB,CAAC/B,SAAS,GAAGsC;OACd,CAAC;MAEFpB,OAAO,CAACC,GAAG,CAAC,GAAGnB,SAAS,KAAKsC,KAAK,CAAC9I,MAAM,iBAAiB,CAAC;IAC7D;EACF;EAEAd,YAAYA,CAACsH,SAAiB;IAC5B,MAAMsC,KAAK,GAAG,IAAI,CAACzG,SAAS,CAACrD,GAAG,CAACwH,SAAS,CAAC,EAAEvH,KAAK;IAClD,OAAO6J,KAAK,IAAIjB,KAAK,CAACC,OAAO,CAACgB,KAAK,CAAC,GAAGA,KAAK,CAAC9I,MAAM,GAAG,CAAC;EACzD;EAEA6E,eAAeA,CAAA;IACb,IAAI,IAAI,CAAChB,SAAS,EAAE;MAClB,IAAI,CAACF,eAAe,CAAC4F,OAAO,CAAC,IAAI,CAAC1F,SAAS,CAAC,CAACc,SAAS,CAAC;QACrD6C,IAAI,EAAGC,QAAQ,IAAI;UACjB,MAAMG,IAAI,GAAGH,QAAQ,CAACG,IAAI,IAAIH,QAAQ;UACtCC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEC,IAAI,CAAC;UAElC;UACA,IAAI,CAACtI,aAAa,GAAG;YACnBC,SAAS,EAAEqI,IAAI,CAACrI,SAAS,IAAI,IAAI;YACjCI,UAAU,EAAEiI,IAAI,CAACjI,UAAU,IAAI,IAAI;YACnCG,UAAU,EAAE8H,IAAI,CAAC9H,UAAU,IAAI,IAAI;YACnCC,aAAa,EACX6H,IAAI,CAACxB,OAAO,EAAEoD,MAAM,CAAEC,IAAS,IAAKA,IAAI,CAAChB,IAAI,KAAK,OAAO,CAAC,IAAI,EAAE;YAClEhI,aAAa,EACXmH,IAAI,CAACxB,OAAO,EAAEoD,MAAM,CAAEC,IAAS,IAAKA,IAAI,CAAChB,IAAI,KAAK,OAAO,CAAC,IAAI;WACjE;UAEDiB,UAAU,CAAC,MAAK;YACd,IAAI,CAAChN,SAAS,CAAC6L,UAAU,CAAC;cACxBnL,IAAI,EAAEwK,IAAI,CAACxK,IAAI,IAAI,EAAE;cACrB+H,QAAQ,EAAEyC,IAAI,CAACzC,QAAQ,IAAI,EAAE;cAC7BC,eAAe,EAAEwC,IAAI,CAACxC,eAAe,IAAI,EAAE;cAC3CC,cAAc,EAAEuC,IAAI,CAACvC,cAAc,IAAI;aACxC,CAAC;YAEF;YACA,IAAI,CAACxH,SAAS,CAAC0K,UAAU,CAAC;cACxBjD,MAAM,EAAEsC,IAAI,CAAC+B,IAAI,EAAEzM,EAAE,IAAI0K,IAAI,CAACtC,MAAM,IAAI,EAAE;cAC1CC,MAAM,EAAEqC,IAAI,CAACgC,IAAI,EAAE1M,EAAE,IAAI0K,IAAI,CAACrC,MAAM,IAAI,EAAE;cAC1CC,OAAO,EAAEoC,IAAI,CAACpC,OAAO,IAAI,EAAE;cAC3BC,cAAc,EAAEmC,IAAI,CAACjC,YAAY,IAAI,EAAE;cACvCA,YAAY,EAAEiC,IAAI,CAACjC,YAAY,IAAI;aACpC,CAAC;YAEF;YACA,IAAIiC,IAAI,CAAC+B,IAAI,EAAE;cACb,IAAI,CAAC5F,cAAc,GAAG6D,IAAI,CAAC+B,IAAI,CAACzM,EAAE,IAAI0K,IAAI,CAACtC,MAAM;cACjD,IAAI,CAACvH,gBAAgB,GAAG6J,IAAI,CAAC+B,IAAI,CAACxM,OAAO,IAAIyK,IAAI,CAAC+B,IAAI,CAACvM,IAAI,IAAI,EAAE;cACjE,IAAI,IAAI,CAAC2G,cAAc,EAAE;gBACvB,IAAI,CAACqE,SAAS,CAAC,IAAI,CAACrE,cAAc,CAAC;cACrC;YACF;YACA,IAAI6D,IAAI,CAACgC,IAAI,EAAE;cACb,IAAI,CAAC3L,gBAAgB,GAAG2J,IAAI,CAACgC,IAAI,CAACzM,OAAO,IAAIyK,IAAI,CAACgC,IAAI,CAACxM,IAAI,IAAI,EAAE;YACnE;YAEA;YACA,IAAI,CAAC0B,SAAS,CAACyJ,UAAU,CAAC;cACxB3C,WAAW,EAAEgC,IAAI,CAAChC,WAAW,IAAI,EAAE;cACnCC,cAAc,EAAE+B,IAAI,CAAC/B,cAAc,IAAI,CAAC;cACxCE,eAAe,EAAE6B,IAAI,CAAC7B,eAAe,IAAI,CAAC;cAC1CC,WAAW,EAAE4B,IAAI,CAAC5B,WAAW,IAAI,CAAC;cAClCC,WAAW,EAAE2B,IAAI,CAAC3B,WAAW,IAAI,CAAC;cAClCC,wBAAwB,EAAE0B,IAAI,CAAC1B,wBAAwB,IAAI,CAAC;cAC5DC,oBAAoB,EAAEyB,IAAI,CAACzB,oBAAoB,IAAI;aACpD,CAAC;YAEF,IAAI,CAACvC,GAAG,CAACuE,aAAa,EAAE;UAC1B,CAAC,EAAE,GAAG,CAAC;QACT,CAAC;QACDH,KAAK,EAAGA,KAAK,IAAI;UACfN,OAAO,CAACM,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;UACnD6B,KAAK,CAAC,sDAAsD,CAAC;QAC/D;OACD,CAAC;IACJ;EACF;EAEAxK,WAAWA,CAACmH,SAAiB;IAC3B,IAAI,CAAClH,aAAa,CAACkH,SAAS,CAAC,GAAG,IAAI;IAEpC,IAAI,CAAC5C,GAAG,CAACuE,aAAa,EAAE;EAC1B;EAEA9H,iBAAiBA,CAACoI,IAAY,EAAErI,KAAa;IAC3C,IAAIqI,IAAI,KAAK,OAAO,EAAE;MACpB,IAAI,CAACnJ,aAAa,CAACS,aAAa,CAAC+J,MAAM,CAAC1J,KAAK,EAAE,CAAC,CAAC;IACnD,CAAC,MAAM,IAAIqI,IAAI,KAAK,OAAO,EAAE;MAC3B,IAAI,CAACnJ,aAAa,CAACmB,aAAa,CAACqJ,MAAM,CAAC1J,KAAK,EAAE,CAAC,CAAC;IACnD;IAEA,IAAI,CAACwD,GAAG,CAACuE,aAAa,EAAE;EAC1B;;qCAldW5E,uBAAuB,EAAApI,EAAA,CAAA4O,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9O,EAAA,CAAA4O,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAhP,EAAA,CAAA4O,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAAjP,EAAA,CAAA4O,iBAAA,CAAAM,EAAA,CAAAC,eAAA,GAAAnP,EAAA,CAAA4O,iBAAA,CAAA5O,EAAA,CAAAoP,iBAAA;EAAA;;UAAvBhH,uBAAuB;IAAAiH,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAvP,EAAA,CAAAwP,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCnBpC9P,EAAA,CAAAE,cAAA,aAAwB;QACtBF,EAAA,CAAAgB,SAAA,8BAIuB;QACzBhB,EAAA,CAAAI,YAAA,EAAM;QASAJ,EAPN,CAAAE,cAAA,aAA4B,aACE,aAIzB,aAEmD;QAChDF,EAAA,CAAAgB,SAAA,WAAgC;QAChChB,EAAA,CAAAE,cAAA,aAAQ;QAAAF,EAAA,CAAAG,MAAA,kBAAW;QAAAH,EAAA,CAAAI,YAAA,EAAS;QAC5BJ,EAAA,CAAAG,MAAA,0FAEF;QAAAH,EAAA,CAAAI,YAAA,EAAM;QAGNJ,EAAA,CAAAE,cAAA,cAA8B;QAmB5BF,EAlBA,CAAAiB,UAAA,KAAA+O,gDAAA,0BAAwC,KAAAC,gDAAA,0BAMA,KAAAC,gDAAA,0BAMA,KAAAC,gDAAA,0BAMA;QAQtCnQ,EADF,CAAAE,cAAA,cAAmE,gBAC9B;QAAAF,EAAA,CAAAG,MAAA,IAAsB;QAAAH,EAAA,CAAAI,YAAA,EAAO;QAChEJ,EAAA,CAAAE,cAAA,gBAA8B;QAAAF,EAAA,CAAAG,MAAA,UAAE;QAAAH,EAAA,CAAAI,YAAA,EAAO;QACvCJ,EAAA,CAAAE,cAAA,gBAAyB;QAAAF,EAAA,CAAAG,MAAA,IAAgB;QAC3CH,EAD2C,CAAAI,YAAA,EAAO,EAC5C;QAENJ,EAAA,CAAAiB,UAAA,KAAAmP,uCAAA,kBAIC;QAIDpQ,EAAA,CAAAE,cAAA,eAA0D;QACxDF,EAAA,CAAAgB,SAAA,eAOO;QAEXhB,EADE,CAAAI,YAAA,EAAM,EACF;QAGNJ,EAAA,CAAAE,cAAA,gBAAuC;QAytBrCF,EAvtBA,CAAAiB,UAAA,KAAAoP,uCAAA,oBAAuD,KAAAC,uCAAA,oBAsEA,KAAAC,uCAAA,oBA0GA,KAAAC,uCAAA,oBAqNA,KAAAC,uCAAA,kBA4TtD,KAAAC,uCAAA,kBAyBA;QAmCT1Q,EAHM,CAAAI,YAAA,EAAO,EACH,EACF,EACF;;;QAz0BFJ,EAAA,CAAAa,SAAA,EAA0B;QAC1Bb,EADA,CAAAsB,UAAA,2BAA0B,4DACiC;QAqBxCtB,EAAA,CAAAa,SAAA,IAAuB;QAAvBb,EAAA,CAAAsB,UAAA,SAAAyO,GAAA,CAAAlI,WAAA,OAAuB;QAMvB7H,EAAA,CAAAa,SAAA,EAAuB;QAAvBb,EAAA,CAAAsB,UAAA,SAAAyO,GAAA,CAAAlI,WAAA,OAAuB;QAMvB7H,EAAA,CAAAa,SAAA,EAAuB;QAAvBb,EAAA,CAAAsB,UAAA,SAAAyO,GAAA,CAAAlI,WAAA,OAAuB;QAMvB7H,EAAA,CAAAa,SAAA,EAAuB;QAAvBb,EAAA,CAAAsB,UAAA,SAAAyO,GAAA,CAAAlI,WAAA,OAAuB;QAQD7H,EAAA,CAAAa,SAAA,GAAsB;QAAtBb,EAAA,CAAAc,kBAAA,UAAAiP,GAAA,CAAAlI,WAAA,KAAsB;QAEhC7H,EAAA,CAAAa,SAAA,GAAgB;QAAhBb,EAAA,CAAA2C,iBAAA,CAAAoN,GAAA,CAAA5H,UAAA,CAAgB;QAIxCnI,EAAA,CAAAa,SAAA,EAAqB;QAArBb,EAAA,CAAAsB,UAAA,SAAAyO,GAAA,CAAAlI,WAAA,KAAqB;QAWpB7H,EAAA,CAAAa,SAAA,GAAsD;QAAtDb,EAAA,CAAA2Q,WAAA,UAAAZ,GAAA,CAAAlI,WAAA,GAAAkI,GAAA,CAAA5H,UAAA,aAAsD;QAWpDnI,EAAA,CAAAa,SAAA,GAAuB;QAAvBb,EAAA,CAAAsB,UAAA,SAAAyO,GAAA,CAAAlI,WAAA,OAAuB;QAsEvB7H,EAAA,CAAAa,SAAA,EAAuB;QAAvBb,EAAA,CAAAsB,UAAA,SAAAyO,GAAA,CAAAlI,WAAA,OAAuB;QA0GvB7H,EAAA,CAAAa,SAAA,EAAuB;QAAvBb,EAAA,CAAAsB,UAAA,SAAAyO,GAAA,CAAAlI,WAAA,OAAuB;QAqNvB7H,EAAA,CAAAa,SAAA,EAAuB;QAAvBb,EAAA,CAAAsB,UAAA,SAAAyO,GAAA,CAAAlI,WAAA,OAAuB;QA0T1B7H,EAAA,CAAAa,SAAA,EAAuB;QAAvBb,EAAA,CAAAsB,UAAA,SAAAyO,GAAA,CAAAlI,WAAA,OAAuB;QAyBvB7H,EAAA,CAAAa,SAAA,EAAqB;QAArBb,EAAA,CAAAsB,UAAA,SAAAyO,GAAA,CAAAlI,WAAA,KAAqB;;;mBDrxBpB/H,wBAAwB,EAAA8Q,EAAA,CAAAC,eAAA,EAAE9Q,YAAY,EAAA+Q,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEnR,mBAAmB,EAAAgP,EAAA,CAAAoC,aAAA,EAAApC,EAAA,CAAAqC,oBAAA,EAAArC,EAAA,CAAAsC,mBAAA,EAAAtC,EAAA,CAAAuC,eAAA,EAAAvC,EAAA,CAAAwC,oBAAA,EAAAxC,EAAA,CAAAyC,kBAAA,EAAAzC,EAAA,CAAA0C,eAAA;IAAAC,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}