{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { Modal } from 'bootstrap';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/profile.service\";\nimport * as i2 from \"src/app/pages/broker/services/property.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/common\";\nfunction AdvertisementsDetailsComponent_span_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 72);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function AdvertisementsDetailsComponent_span_31_Template_button_click_2_listener($event) {\n      const ctx_r2 = i0.ɵɵrestoreView(_r2);\n      const loc_r4 = ctx_r2.$implicit;\n      const i_r5 = ctx_r2.index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      ctx_r5.removeLocation(i_r5, loc_r4.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(3, \"i\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const loc_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", loc_r4.name_en, \" \");\n  }\n}\nfunction AdvertisementsDetailsComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵelement(1, \"i\", 76);\n    i0.ɵɵtext(2, \" Loading cities... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdvertisementsDetailsComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵelement(1, \"i\", 78);\n    i0.ɵɵtext(2, \" No cities available \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdvertisementsDetailsComponent_ng_container_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"i\", 76);\n    i0.ɵɵtext(2, \" Loading... \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AdvertisementsDetailsComponent_ng_container_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.selectedCityName || \"Select City\", \" \");\n  }\n}\nfunction AdvertisementsDetailsComponent_li_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 79);\n    i0.ɵɵelement(1, \"i\", 76);\n    i0.ɵɵtext(2, \" Loading cities... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdvertisementsDetailsComponent_li_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" Available Cities: \", ctx_r5.cities.length, \" \");\n  }\n}\nfunction AdvertisementsDetailsComponent_ng_container_61_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 82)(1, \"a\", 83);\n    i0.ɵɵlistener(\"click\", function AdvertisementsDetailsComponent_ng_container_61_li_1_Template_a_click_1_listener() {\n      const city_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.selectCity(city_r8.id, city_r8.name_en));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const city_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", city_r8.name_en, \" \");\n  }\n}\nfunction AdvertisementsDetailsComponent_ng_container_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AdvertisementsDetailsComponent_ng_container_61_li_1_Template, 3, 1, \"li\", 81);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.cities);\n  }\n}\nfunction AdvertisementsDetailsComponent_ng_template_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 84);\n    i0.ɵɵtext(2, \" No cities available \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AdvertisementsDetailsComponent_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵelement(1, \"i\", 78);\n    i0.ɵɵtext(2, \" Please select a city first \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdvertisementsDetailsComponent_li_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 85);\n    i0.ɵɵelement(1, \"i\", 86);\n    i0.ɵɵtext(2, \" No areas available. Please select a city first. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdvertisementsDetailsComponent_ng_container_75_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 83);\n    i0.ɵɵlistener(\"click\", function AdvertisementsDetailsComponent_ng_container_75_li_1_Template_a_click_1_listener() {\n      const area_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.selectArea(area_r10.id, area_r10.name_en));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const area_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", area_r10.name_en, \" \");\n  }\n}\nfunction AdvertisementsDetailsComponent_ng_container_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AdvertisementsDetailsComponent_ng_container_75_li_1_Template, 3, 1, \"li\", 87);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.areas);\n  }\n}\nfunction AdvertisementsDetailsComponent_li_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 88);\n    i0.ɵɵelement(1, \"i\", 89);\n    i0.ɵɵtext(2, \" No areas available for this city \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdvertisementsDetailsComponent_button_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 90);\n    i0.ɵɵelement(1, \"i\", 74);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdvertisementsDetailsComponent_div_102_div_8_div_1_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 105);\n    i0.ɵɵtext(2, \"Industrial\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AdvertisementsDetailsComponent_div_102_div_8_div_1_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 105);\n    i0.ɵɵtext(2, \"Commercial\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AdvertisementsDetailsComponent_div_102_div_8_div_1_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 105);\n    i0.ɵɵtext(2, \"Residential\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AdvertisementsDetailsComponent_div_102_div_8_div_1_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 105);\n    i0.ɵɵtext(2, \"National Project\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AdvertisementsDetailsComponent_div_102_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 100)(1, \"div\", 101)(2, \"input\", 102);\n    i0.ɵɵlistener(\"change\", function AdvertisementsDetailsComponent_div_102_div_8_div_1_Template_input_change_2_listener($event) {\n      const type_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const scope_r12 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onScopeChange(scope_r12.specialization_scope, type_r14, $event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(3, \"div\", 103);\n    i0.ɵɵelementStart(4, \"div\", 104);\n    i0.ɵɵtemplate(5, AdvertisementsDetailsComponent_div_102_div_8_div_1_ng_container_5_Template, 3, 0, \"ng-container\", 40)(6, AdvertisementsDetailsComponent_div_102_div_8_div_1_ng_container_6_Template, 3, 0, \"ng-container\", 40)(7, AdvertisementsDetailsComponent_div_102_div_8_div_1_ng_container_7_Template, 3, 0, \"ng-container\", 40)(8, AdvertisementsDetailsComponent_div_102_div_8_div_1_ng_container_8_Template, 3, 0, \"ng-container\", 40);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const type_r14 = ctx.$implicit;\n    const scope_r12 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r5.hasSpecialization(scope_r12.specialization_scope, type_r14));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", type_r14.includes(\"Factories\") || type_r14.includes(\"Warehouses\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", type_r14.includes(\"Administrative\") || type_r14.includes(\"Commercial\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", type_r14.includes(\"Apartment\") || type_r14.includes(\"Duplex\") || type_r14.includes(\"Penthouse\") || type_r14.includes(\"Townhouse\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", type_r14.includes(\"Sakan Misr\") || type_r14.includes(\"Dar Misr\") || type_r14.includes(\"Ganat Misr\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.specializationDisplayMap[type_r14] || type_r14, \" \");\n  }\n}\nfunction AdvertisementsDetailsComponent_div_102_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtemplate(1, AdvertisementsDetailsComponent_div_102_div_8_div_1_Template, 10, 6, \"div\", 99);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scope_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", scope_r12.specializations);\n  }\n}\nfunction AdvertisementsDetailsComponent_div_102_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 91)(1, \"div\", 92);\n    i0.ɵɵlistener(\"click\", function AdvertisementsDetailsComponent_div_102_Template_div_click_1_listener() {\n      const scope_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      return i0.ɵɵresetView(scope_r12.expanded = !scope_r12.expanded);\n    });\n    i0.ɵɵelementStart(2, \"span\", 93);\n    i0.ɵɵlistener(\"click\", function AdvertisementsDetailsComponent_div_102_Template_span_click_2_listener($event) {\n      const scope_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      $event.stopPropagation();\n      return i0.ɵɵresetView(scope_r12.expanded = !scope_r12.expanded);\n    });\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 94);\n    i0.ɵɵlistener(\"click\", function AdvertisementsDetailsComponent_div_102_Template_div_click_4_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(5, \"input\", 95);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 96);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, AdvertisementsDetailsComponent_div_102_div_8_Template, 2, 1, \"div\", 97);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scope_r12 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(scope_r12.expanded ? \"fas fa-chevron-down\" : \"fas fa-chevron-right\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r5.hasScope(scope_r12.specialization_scope));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", scope_r12.specialization_scope, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", scope_r12.expanded);\n  }\n}\n// Define interfaces for the tree structure\nexport class AdvertisementsDetailsComponent {\n  profileService;\n  propertyService;\n  cd;\n  fb;\n  user = {};\n  cities = [];\n  areas = [];\n  selectedCityId;\n  selectedCityName;\n  selectedAreaName;\n  isLoadingCities = false;\n  Form;\n  // New items for the modal forms\n  newLocation = '';\n  newSpecialization = '';\n  // References to the modals\n  locationModal = null;\n  specializationModal = null;\n  constructor(profileService, propertyService, cd, fb) {\n    this.profileService = profileService;\n    this.propertyService = propertyService;\n    this.cd = cd;\n    this.fb = fb;\n    // Initialize the form\n    this.Form = this.fb.group({\n      cityId: [''],\n      areaId: ['']\n    });\n  }\n  ngOnInit() {\n    this.updatedScopes = [...(this.user.specializationScopes || [])];\n    if (this.user && this.user.locations) {\n      this.locations = this.user.locations;\n    }\n    // Load cities when component initializes\n    this.loadCities();\n  }\n  //***************************************************** */\n  //***************************************************** */\n  // ******************location modal***********************\n  // Current selected locations\n  locations = [];\n  openLocationModal() {\n    this.newLocation = '';\n    const modalElement = document.getElementById('addLocationModal');\n    if (modalElement) {\n      this.locationModal = new Modal(modalElement);\n      this.locationModal.show();\n    }\n  }\n  saveLocation() {\n    const selectedAreaId = this.Form.value.areaId;\n    const selectedArea = this.areas.find(area => area.id === selectedAreaId);\n    const existingAreaIds = this.user.areas.map(area => area.id);\n    if (!existingAreaIds.includes(selectedAreaId)) {\n      existingAreaIds.push(selectedAreaId);\n    }\n    this.profileService.updateAdvertisements(this.user.id, {\n      areaIds: existingAreaIds\n    }).subscribe({\n      next: response => {\n        // console.log(locationData);\n        console.log(response);\n        this.showSuccessMessage('Location saved successfully');\n        if (!this.user.areas.some(area => area.id === selectedAreaId)) {\n          this.user.areas.push({\n            id: selectedAreaId,\n            name_en: selectedArea ? selectedArea.name_en : this.selectedAreaName\n          });\n        }\n        if (this.locationModal) {\n          this.locationModal.hide();\n        }\n        this.cd.detectChanges();\n      },\n      error: err => {\n        this.showErrorMessage(err);\n      }\n    });\n  }\n  loadCities() {\n    this.isLoadingCities = true;\n    this.propertyService.getCities().subscribe({\n      next: response => {\n        if (response && response.data) {\n          this.cities = response.data;\n        } else {\n          console.warn('No cities data in response');\n          this.cities = [];\n        }\n      },\n      error: err => {\n        console.error('Error loading cities:', err);\n      },\n      complete: () => {\n        this.isLoadingCities = false;\n        this.cd.detectChanges();\n      }\n    });\n  }\n  loadAreas(cityId) {\n    this.propertyService.getAreas(cityId).subscribe({\n      next: response => {\n        if (response && response.data) {\n          this.areas = response.data;\n        } else {\n          console.warn('No areas data in response');\n          this.areas = [];\n        }\n      },\n      error: err => {\n        console.error('Error loading areas:', err);\n        this.areas = [];\n      },\n      complete: () => {\n        this.cd.detectChanges();\n      }\n    });\n  }\n  selectCity(cityId, cityName) {\n    this.selectedCityId = cityId;\n    this.selectedCityName = cityName;\n    // Check if Form is initialized before patching\n    if (this.Form) {\n      this.Form.patchValue({\n        cityId: cityId\n      });\n    }\n    // Load areas for the selected city\n    this.loadAreas(cityId);\n  }\n  selectArea(areaId, areaName) {\n    this.selectedAreaName = areaName;\n    // Check if Form is initialized before patching\n    if (this.Form) {\n      this.Form.patchValue({\n        areaId: areaId\n      });\n    }\n  }\n  removeLocation(index, areaId) {\n    if (index >= 0 && index < this.user.areas.length) {\n      let currentAreaIds = Array.isArray(this.Form.value.areaId) ? this.Form.value.areaId : [this.Form.value.areaId];\n      let updatedAreaIds = currentAreaIds.filter(id => id !== areaId);\n      this.Form.patchValue({\n        areaId: updatedAreaIds\n      });\n      this.user.areas.splice(index, 1);\n    }\n  }\n  //***************************************************** */\n  //***************************************************** */\n  // ****************specialization modal***********************\n  searchTerm = '';\n  formChanged = false;\n  originalSpecializations = [];\n  updatedScopes = [];\n  staticScopes = [{\n    specialization_scope: 'purchase_sell_outside_compound',\n    specializations: ['purchasing_sell_residential_outside_compound', 'purchasing_sell_national_housing_projects_outside_compound', 'purchasing_sell_administrative_commercial_units_outside_compound', 'purchasing_sell_industrial_and_warehousing_outside_compound', 'purchasing_sell_lands_and_ready_projects_outside_compound', 'purchasing_sell_villas_and_buildings_outside_compound'],\n    expanded: false,\n    selected: false\n  },\n  // {\n  //   specialization_scope: 'purchase_sell_inside_compound',\n  //   specializations: [\n  //     'purchasing_sell_residential_inside_compound',\n  //     'purchasing_sell_villas_inside_compound',\n  //     'purchasing_sell_administrative_commercial_units_inside_compound'\n  //   ],\n  //   expanded: false,\n  //   selected: false,\n  // },\n  {\n    specialization_scope: 'primary_inside_compound',\n    specializations: ['purchasing_sell_residential_inside_compound', 'purchasing_sell_villas_inside_compound', 'purchasing_sell_administrative_commercial_units_inside_compound'],\n    expanded: false,\n    selected: false\n  }, {\n    specialization_scope: 'resale_inside_compound',\n    specializations: ['purchasing_sell_residential_inside_compound', 'purchasing_sell_villas_inside_compound', 'purchasing_sell_administrative_commercial_units_inside_compound'],\n    expanded: false,\n    selected: false\n  }, {\n    specialization_scope: 'rentals_outside_compound',\n    specializations: ['rent_residential_inside_compound', 'rent_hotel_Units_inside_compound', 'rent_administrative_commercial_units_inside_compound'],\n    expanded: false,\n    selected: false\n  }, {\n    specialization_scope: 'rentals_inside_compound',\n    specializations: ['rent_residential_outside_compound', 'rent_national_housing_projects_compound', 'rent_administrative_commercial_units_outside_compound', 'rent_industrial_and_warehousing_outside_compound', 'rent_hotel_units_outside_compound'],\n    expanded: false,\n    selected: false\n  }];\n  // Open specialization modal\n  openSpecializationModal() {\n    this.searchTerm = '';\n    const modalElement = document.getElementById('addSpecializationModal');\n    if (modalElement) {\n      this.specializationModal = new Modal(modalElement);\n      this.specializationModal.show();\n    }\n  }\n  hasScope(scopeName) {\n    return this.updatedScopes.some(item => item.specialization_scope === scopeName);\n  }\n  hasSpecialization(scopeName, specialization) {\n    if (!this.user || !this.user.specializationScopes) return false;\n    return this.user.specializationScopes.some(item => item.specialization_scope === scopeName && item.specialization === specialization);\n  }\n  onScopeChange(scopeName, specialization, event) {\n    console.log(specialization);\n    const checked = event.target.checked;\n    if (checked) {\n      const exists = this.updatedScopes.some(item => item.specialization_scope === scopeName && item.specialization === specialization);\n      if (!exists) {\n        this.updatedScopes.push({\n          specialization_scope: scopeName,\n          specialization: specialization\n        });\n      }\n    } else {\n      this.updatedScopes = this.updatedScopes.filter(item => !(item.specialization_scope === scopeName && item.specialization === specialization));\n    }\n    this.formChanged = true;\n  }\n  saveChanges() {\n    let payload = {\n      specializationScopes: {}\n    };\n    console.log('Updated scopes:', this.updatedScopes);\n    this.updatedScopes.forEach(scope => {\n      const key = scope.specialization_scope;\n      if (!key) return;\n      if (!payload.specializationScopes[key]) {\n        payload.specializationScopes[key] = [];\n      }\n      payload.specializationScopes[key].push(scope.specialization);\n    });\n    console.log('Payload:', payload);\n    this.profileService.updateAdvertisements(this.user.id, payload).subscribe({\n      next: res => {\n        this.formChanged = false;\n        console.log('Payload:', payload);\n        console.log('Response:', res);\n        this.showSuccessMessage('Saved successfully');\n        this.specializationModal?.hide();\n      },\n      error: err => {\n        this.showErrorMessage(err);\n      }\n    });\n  }\n  showSuccessMessage(message) {\n    Swal.fire({\n      icon: 'success',\n      title: 'Success',\n      text: message\n    });\n  }\n  showErrorMessage(error) {\n    Swal.fire({\n      icon: 'error',\n      title: 'Error',\n      text: error.error.message\n    });\n  }\n  specializationDisplayMap = {\n    'purchasing_sell_residential_outside_compound': 'Residential - Outside Compound',\n    'purchasing_sell_national_housing_projects_outside_compound': 'National Housing - Outside Compound',\n    'purchasing_sell_administrative_commercial_units_outside_compound': 'Commercial Units - Outside Compound',\n    'purchasing_sell_industrial_and_warehousing_outside_compound': 'Industrial/Warehousing - Outside Compound',\n    'purchasing_sell_lands_and_ready_projects_outside_compound': 'Lands & Ready Projects',\n    'purchasing_sell_villas_and_buildings_outside_compound': 'Villas/Buildings - Outside Compound',\n    'purchasing_sell_residential_inside_compound': 'Residential - Inside Compound',\n    'purchasing_sell_villas_inside_compound': 'Villas - Inside Compound',\n    'purchasing_sell_administrative_commercial_units_inside_compound': 'Commercial Units - Inside Compound',\n    'rent_residential_inside_compound': 'Rent Residential - Inside Compound',\n    'rent_hotel_Units_inside_compound': 'Rent Hotel Units - Inside Compound',\n    'rent_administrative_commercial_units_inside_compound': 'Rent Commercial Units - Inside Compound',\n    'rent_residential_outside_compound': 'Rent Residential - Outside Compound',\n    'rent_national_housing_projects_compound': 'Rent National Housing Projects',\n    'rent_administrative_commercial_units_outside_compound': 'Rent Commercial Units - Outside Compound',\n    'rent_industrial_and_warehousing_outside_compound': 'Rent Industrial/Warehousing - Outside Compound',\n    'rent_hotel_units_outside_compound': 'Rent Hotel Units - Outside Compound'\n  };\n  static ɵfac = function AdvertisementsDetailsComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AdvertisementsDetailsComponent)(i0.ɵɵdirectiveInject(i1.ProfileService), i0.ɵɵdirectiveInject(i2.PropertyService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.FormBuilder));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AdvertisementsDetailsComponent,\n    selectors: [[\"app-advertisements-details\"]],\n    inputs: {\n      user: \"user\"\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 111,\n    vars: 21,\n    consts: [[\"noCities\", \"\"], [1, \"card\", \"mb-5\", \"mb-xl-10\"], [\"role\", \"button\", \"data-bs-toggle\", \"collapse\", \"data-bs-target\", \"#kt_account_ads_details\", \"aria-expanded\", \"true\", \"aria-controls\", \"kt_account_ads_details\", 1, \"card-header\", \"border-0\", \"bg-light-dark-blue\"], [1, \"card-title\", \"m-0\"], [1, \"fw-bolder\", \"m-0\"], [\"id\", \"kt_account_ads_details\", 1, \"collapse\", \"show\"], [\"novalidate\", \"\", 1, \"form\"], [1, \"card-body\", \"border-top\", \"p-9\"], [1, \"row\", \"mb-6\"], [1, \"col-lg-4\", \"col-form-label\", \"fw-bold\", \"fs-6\"], [1, \"col-lg-8\", \"fv-row\"], [1, \"row\"], [1, \"col-md-9\"], [1, \"col-md-3\"], [1, \"align-items-end\", \"text-end\"], [1, \"btn\", \"btn-light-dark-blue\", \"btn-sm\", 3, \"click\"], [1, \"fa-solid\", \"fa-arrow-right\"], [\"data-bs-toggle\", \"tooltip\", \"data-bs-placement\", \"top\", \"data-bs-custom-class\", \"red-tooltip\", \"title\", \"\\u064A\\u0645\\u0643\\u0646 \\u062A\\u063A\\u064A\\u064A\\u0631 \\u0627\\u0644\\u0645\\u0646\\u0627\\u0637\\u0642 \\u0628\\u0639\\u062F \\u0661\\u0667 \\u064A\\u0648\\u0645\\u0627\\u064B\", 1, \"ki-duotone\", \"ki-information-5\", \"text-danger\", \"fs-6\", \"ms-1\"], [1, \"path1\"], [1, \"path2\"], [1, \"path3\"], [1, \"d-flex\", \"flex-wrap\", \"fw-bold\", \"fs-6\", \"mb-4\", \"pe-2\"], [\"class\", \"d-flex align-items-center me-5 mb-2 fw-bolder py-4 px-5 fs-6 badge badge-light-dark-blue text-dark-blue\", 4, \"ngFor\", \"ngForOf\"], [1, \"fa-solid\", \"fa-plus\"], [\"id\", \"addLocationModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"addLocationModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-dialog-centered\"], [1, \"modal-content\"], [1, \"modal-header\", \"bg-light-primary\"], [\"id\", \"addLocationModalLabel\", 1, \"modal-title\", \"fw-bold\", \"text-dark-blue\"], [1, \"fas\", \"fa-map-marker-alt\", \"me-2\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"btn-close\"], [1, \"modal-body\", 3, \"formGroup\"], [1, \"card\", \"mb-4\"], [1, \"card-body\", \"p-4\"], [1, \"mb-4\"], [1, \"form-label\", \"fw-bold\", \"text-start\", \"d-block\", \"fs-6\"], [\"class\", \"d-flex align-items-center text-primary mb-2\", 4, \"ngIf\"], [\"class\", \"alert alert-warning py-2 mb-2\", 4, \"ngIf\"], [1, \"dropdown\", \"bg-secondary\"], [\"type\", \"button\", \"id\", \"cityDropdownStep1\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-primary\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"py-2\", 3, \"disabled\"], [4, \"ngIf\"], [1, \"fas\", \"fa-chevron-down\"], [\"aria-labelledby\", \"cityDropdownStep1\", 1, \"dropdown-menu\", \"w-100\", \"shadow-sm\", 2, \"max-height\", \"250px\", \"overflow-y\", \"auto\", \"position\", \"absolute\", \"z-index\", \"1000\"], [\"class\", \"dropdown-item disabled text-primary\", 4, \"ngIf\"], [\"class\", \"dropdown-item disabled small text-muted\", 4, \"ngIf\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"mb-2\"], [\"type\", \"button\", \"id\", \"areaDropdownStep1\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-primary\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"py-2\", 3, \"disabled\"], [\"aria-labelledby\", \"areaDropdownStep1\", 1, \"dropdown-menu\", \"w-100\", \"shadow-sm\", 2, \"max-height\", \"250px\", \"overflow-y\", \"auto\", \"position\", \"absolute\", \"z-index\", \"1000\"], [\"class\", \"dropdown-item disabled text-danger\", 4, \"ngIf\"], [\"class\", \"dropdown-item disabled\", 4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-light-dark\", \"px-4\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"px-4\", 3, \"click\"], [1, \"fa-solid\", \"fa-save\", \"me-2\"], [\"id\", \"addSpecializationModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"addSpecializationModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-dialog-centered\", \"modal-lg\"], [\"id\", \"addSpecializationModalLabel\", 1, \"modal-title\", \"fw-bold\"], [1, \"modal-body\"], [1, \"form-label\", \"fw-bold\", \"mb-3\"], [1, \"specialization-tree-container\"], [1, \"input-group\"], [1, \"input-group-text\", \"bg-light\"], [1, \"fa-solid\", \"fa-search\"], [\"type\", \"text\", \"placeholder\", \"Search specializations...\", \"name\", \"searchTerm\", 1, \"form-control\", \"form-control-solid\", 3, \"ngModelChange\", \"ngModel\"], [\"class\", \"btn btn-light-primary\", \"type\", \"button\", 4, \"ngIf\"], [1, \"specialization-tree\"], [\"class\", \"tree-node\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-text\", \"text-muted\", \"mt-2\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-light\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"fa-solid\", \"fa-save\", \"me-1\"], [1, \"d-flex\", \"align-items-center\", \"me-5\", \"mb-2\", \"fw-bolder\", \"py-4\", \"px-5\", \"fs-6\", \"badge\", \"badge-light-dark-blue\", \"text-dark-blue\"], [\"type\", \"button\", \"title\", \"Remove\", 1, \"btn\", \"btn-icon\", \"btn-light\", \"btn-active-light-danger\", \"rounded-circle\", \"ms-2\", 3, \"click\"], [1, \"fa-solid\", \"fa-times\"], [1, \"d-flex\", \"align-items-center\", \"text-primary\", \"mb-2\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"me-2\"], [1, \"alert\", \"alert-warning\", \"py-2\", \"mb-2\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-2\"], [1, \"dropdown-item\", \"disabled\", \"text-primary\"], [1, \"dropdown-item\", \"disabled\", \"small\", \"text-muted\"], [\"style\", \"cursor: pointer\", 4, \"ngFor\", \"ngForOf\"], [2, \"cursor\", \"pointer\"], [1, \"dropdown-item\", \"text-start\", \"py-2\", 3, \"click\"], [1, \"dropdown-item\", \"text-start\", \"disabled\", \"py-2\"], [1, \"dropdown-item\", \"disabled\", \"text-danger\"], [1, \"fas\", \"fa-exclamation-circle\", \"me-2\"], [4, \"ngFor\", \"ngForOf\"], [1, \"dropdown-item\", \"disabled\"], [1, \"fas\", \"fa-info-circle\", \"me-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-light-primary\"], [1, \"tree-node\"], [1, \"d-flex\", \"align-items-center\", \"py-2\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"me-2\", 3, \"click\"], [1, \"form-check\", \"me-2\", 3, \"click\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"checked\"], [1, \"fw-bold\", \"fs-5\"], [\"class\", \"ms-5 ps-3 py-1\", 4, \"ngIf\"], [1, \"ms-5\", \"ps-3\", \"py-1\"], [\"class\", \"d-flex align-items-center mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"align-items-center\", \"mb-1\"], [1, \"form-check\", \"me-2\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"change\", \"checked\"], [1, \"fw-semibold\", \"text-primary\"], [1, \"ms-3\", \"text-muted\"], [1, \"badge\", \"badge-light-info\", \"me-2\"]],\n    template: function AdvertisementsDetailsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"h3\", 4);\n        i0.ɵɵtext(4, \"Advertisements and Properties Details\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(5, \"div\", 5)(6, \"form\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"label\", 9)(10, \"span\");\n        i0.ɵɵtext(11, \"Specializations \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(12, \"div\", 10)(13, \"div\", 11);\n        i0.ɵɵelement(14, \"div\", 12);\n        i0.ɵɵelementStart(15, \"div\", 13)(16, \"div\", 14)(17, \"button\", 15);\n        i0.ɵɵlistener(\"click\", function AdvertisementsDetailsComponent_Template_button_click_17_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.openSpecializationModal());\n        });\n        i0.ɵɵelement(18, \"i\", 16);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(19, \"div\", 8)(20, \"label\", 9)(21, \"span\");\n        i0.ɵɵtext(22, \"Locations\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"i\", 17);\n        i0.ɵɵelement(24, \"span\", 18)(25, \"span\", 19)(26, \"span\", 20);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(27, \"div\", 10)(28, \"div\", 11)(29, \"div\", 12)(30, \"div\", 21);\n        i0.ɵɵtemplate(31, AdvertisementsDetailsComponent_span_31_Template, 4, 1, \"span\", 22);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(32, \"div\", 13)(33, \"div\", 14)(34, \"button\", 15);\n        i0.ɵɵlistener(\"click\", function AdvertisementsDetailsComponent_Template_button_click_34_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.openLocationModal());\n        });\n        i0.ɵɵelement(35, \"i\", 23);\n        i0.ɵɵelementEnd()()()()()()()()()();\n        i0.ɵɵelementStart(36, \"div\", 24)(37, \"div\", 25)(38, \"div\", 26)(39, \"div\", 27)(40, \"h5\", 28);\n        i0.ɵɵelement(41, \"i\", 29);\n        i0.ɵɵtext(42, \" Add New Location \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(43, \"button\", 30);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(44, \"div\", 31)(45, \"div\", 32)(46, \"div\", 33)(47, \"div\", 34)(48, \"label\", 35);\n        i0.ɵɵtext(49, \"City\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(50, AdvertisementsDetailsComponent_div_50_Template, 3, 0, \"div\", 36)(51, AdvertisementsDetailsComponent_div_51_Template, 3, 0, \"div\", 37);\n        i0.ɵɵelementStart(52, \"div\", 38)(53, \"button\", 39)(54, \"span\");\n        i0.ɵɵtemplate(55, AdvertisementsDetailsComponent_ng_container_55_Template, 3, 0, \"ng-container\", 40)(56, AdvertisementsDetailsComponent_ng_container_56_Template, 2, 1, \"ng-container\", 40);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(57, \"i\", 41);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(58, \"ul\", 42);\n        i0.ɵɵtemplate(59, AdvertisementsDetailsComponent_li_59_Template, 3, 0, \"li\", 43)(60, AdvertisementsDetailsComponent_li_60_Template, 2, 1, \"li\", 44)(61, AdvertisementsDetailsComponent_ng_container_61_Template, 2, 1, \"ng-container\", 45)(62, AdvertisementsDetailsComponent_ng_template_62_Template, 3, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(64, \"div\", 46)(65, \"label\", 35);\n        i0.ɵɵtext(66, \"Area\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(67, AdvertisementsDetailsComponent_div_67_Template, 3, 0, \"div\", 37);\n        i0.ɵɵelementStart(68, \"div\", 38)(69, \"button\", 47)(70, \"span\");\n        i0.ɵɵtext(71);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(72, \"i\", 41);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(73, \"ul\", 48);\n        i0.ɵɵtemplate(74, AdvertisementsDetailsComponent_li_74_Template, 3, 0, \"li\", 49)(75, AdvertisementsDetailsComponent_ng_container_75_Template, 2, 1, \"ng-container\", 40)(76, AdvertisementsDetailsComponent_li_76_Template, 3, 0, \"li\", 50);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(77, \"div\", 51)(78, \"button\", 52);\n        i0.ɵɵtext(79, \" Cancel \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(80, \"button\", 53);\n        i0.ɵɵlistener(\"click\", function AdvertisementsDetailsComponent_Template_button_click_80_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.saveLocation());\n        });\n        i0.ɵɵelement(81, \"i\", 54);\n        i0.ɵɵtext(82, \" Save Location \");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(83, \"div\", 55)(84, \"div\", 56)(85, \"div\", 26)(86, \"div\", 27)(87, \"h5\", 57);\n        i0.ɵɵtext(88, \" Select Specializations \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(89, \"button\", 30);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(90, \"div\", 58)(91, \"div\", 34)(92, \"h6\", 59);\n        i0.ɵɵtext(93, \" Choose from the specialization tree: \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(94, \"div\", 60)(95, \"div\", 34)(96, \"div\", 61)(97, \"span\", 62);\n        i0.ɵɵelement(98, \"i\", 63);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(99, \"input\", 64);\n        i0.ɵɵtwoWayListener(\"ngModelChange\", function AdvertisementsDetailsComponent_Template_input_ngModelChange_99_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          i0.ɵɵtwoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);\n          return i0.ɵɵresetView($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(100, AdvertisementsDetailsComponent_button_100_Template, 2, 0, \"button\", 65);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(101, \"div\", 66);\n        i0.ɵɵtemplate(102, AdvertisementsDetailsComponent_div_102_Template, 9, 5, \"div\", 67);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(103, \"div\", 68);\n        i0.ɵɵtext(104, \" Select scopes and types. Selecting a scope will select all types under it. \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(105, \"div\", 51)(106, \"button\", 69);\n        i0.ɵɵtext(107, \" Cancel \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(108, \"button\", 70);\n        i0.ɵɵlistener(\"click\", function AdvertisementsDetailsComponent_Template_button_click_108_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.saveChanges());\n        });\n        i0.ɵɵelement(109, \"i\", 71);\n        i0.ɵɵtext(110, \" Save \");\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        const noCities_r15 = i0.ɵɵreference(63);\n        i0.ɵɵadvance(31);\n        i0.ɵɵproperty(\"ngForOf\", ctx.user.areas);\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"formGroup\", ctx.Form);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoadingCities);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingCities && ctx.cities.length === 0);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.isLoadingCities);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoadingCities);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingCities);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoadingCities);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingCities);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.cities && ctx.cities.length > 0)(\"ngIfElse\", noCities_r15);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", !ctx.selectedCityId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", !ctx.selectedCityId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.selectedAreaName || \"Select Area\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", !ctx.selectedCityId);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedCityId && ctx.areas.length > 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedCityId && ctx.areas.length === 0);\n        i0.ɵɵadvance(23);\n        i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTerm);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.searchTerm);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.staticScopes);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"disabled\", !ctx.formChanged);\n      }\n    },\n    dependencies: [CommonModule, i4.NgForOf, i4.NgIf, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.NgModel, i3.NgForm, ReactiveFormsModule, i3.FormGroupDirective],\n    styles: [\".badge[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem !important;\\n  position: relative;\\n  transition: all 0.2s ease;\\n  display: inline-flex;\\n  align-items: center;\\n}\\n.badge[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  width: 15px;\\n  height: 15px;\\n  padding: 0;\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-left: 5px;\\n  opacity: 0;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 0.1rem 0.3rem rgba(0, 0, 0, 0.1);\\n}\\n.badge[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 8px;\\n}\\n.badge[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  background-color: #e6dadb !important;\\n  color: #ffffff !important;\\n}\\n.badge[_ngcontent-%COMP%]:hover   .btn[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  border: none;\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);\\n  border-radius: 0.475rem;\\n}\\n.modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%] {\\n  background-color: #f1f8ff;\\n  border-bottom: 1px solid #eff2f5;\\n}\\n.modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%] {\\n  color: #181c32;\\n}\\n.modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n}\\n.modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-control-solid[_ngcontent-%COMP%], \\n.modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-select-solid[_ngcontent-%COMP%] {\\n  background-color: #f5f8fa;\\n  border-color: #f5f8fa;\\n  color: #5e6278;\\n  transition: color 0.2s ease, background-color 0.2s ease;\\n}\\n.modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-control-solid[_ngcontent-%COMP%]:focus, \\n.modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-select-solid[_ngcontent-%COMP%]:focus {\\n  background-color: #eef3f7;\\n  border-color: #eef3f7;\\n}\\n.modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-text[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n}\\n.modal-content[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%] {\\n  border-top: 1px solid #eff2f5;\\n  padding: 1.5rem;\\n}\\n\\n.specialization-tree-container[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n  overflow-y: auto;\\n  border: 1px solid #eff2f5;\\n  border-radius: 0.475rem;\\n  padding: 1rem;\\n  background-color: #ffffff;\\n  box-shadow: 0 0.1rem 0.5rem rgba(0, 0, 0, 0.05);\\n}\\n\\n.specialization-tree[_ngcontent-%COMP%]   .tree-node[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n  margin-bottom: 0.5rem;\\n  border-radius: 0.475rem;\\n}\\n.specialization-tree[_ngcontent-%COMP%]   .tree-node[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.specialization-tree[_ngcontent-%COMP%]   .tree-node[_ngcontent-%COMP%]   .node-name[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.specialization-tree[_ngcontent-%COMP%]   .tree-node[_ngcontent-%COMP%]   .node-name[_ngcontent-%COMP%]:hover {\\n  color: #009ef7;\\n}\\n.specialization-tree[_ngcontent-%COMP%]   .tree-node-type[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n  margin-bottom: 0.25rem;\\n  border-left: 2px solid #eff2f5;\\n}\\n.specialization-tree[_ngcontent-%COMP%]   .tree-node-type[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.specialization-tree[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n.specialization-tree[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%]:checked {\\n  background-color: #009ef7;\\n  border-color: #1ac228;\\n}\\n.specialization-tree[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n}\\n.specialization-tree[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]:hover {\\n  background-color: #f1f8ff;\\n  color: #009ef7;\\n}\\n.specialization-tree[_ngcontent-%COMP%]   .btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "Modal", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵlistener", "AdvertisementsDetailsComponent_span_31_Template_button_click_2_listener", "$event", "ctx_r2", "ɵɵrestoreView", "_r2", "loc_r4", "$implicit", "i_r5", "index", "ctx_r5", "ɵɵnextContext", "removeLocation", "id", "ɵɵresetView", "stopPropagation", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "name_en", "ɵɵelementContainerStart", "selectedCityName", "cities", "length", "AdvertisementsDetailsComponent_ng_container_61_li_1_Template_a_click_1_listener", "city_r8", "_r7", "selectCity", "ɵɵtemplate", "AdvertisementsDetailsComponent_ng_container_61_li_1_Template", "ɵɵproperty", "AdvertisementsDetailsComponent_ng_container_75_li_1_Template_a_click_1_listener", "area_r10", "_r9", "selectArea", "AdvertisementsDetailsComponent_ng_container_75_li_1_Template", "areas", "AdvertisementsDetailsComponent_div_102_div_8_div_1_Template_input_change_2_listener", "type_r14", "_r13", "scope_r12", "onScopeChange", "specialization_scope", "AdvertisementsDetailsComponent_div_102_div_8_div_1_ng_container_5_Template", "AdvertisementsDetailsComponent_div_102_div_8_div_1_ng_container_6_Template", "AdvertisementsDetailsComponent_div_102_div_8_div_1_ng_container_7_Template", "AdvertisementsDetailsComponent_div_102_div_8_div_1_ng_container_8_Template", "hasSpecialization", "includes", "specializationDisplayMap", "AdvertisementsDetailsComponent_div_102_div_8_div_1_Template", "specializations", "AdvertisementsDetailsComponent_div_102_Template_div_click_1_listener", "_r11", "expanded", "AdvertisementsDetailsComponent_div_102_Template_span_click_2_listener", "AdvertisementsDetailsComponent_div_102_Template_div_click_4_listener", "AdvertisementsDetailsComponent_div_102_div_8_Template", "ɵɵclassMap", "hasScope", "AdvertisementsDetailsComponent", "profileService", "propertyService", "cd", "fb", "user", "selectedCityId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLoadingCities", "Form", "newLocation", "newSpecialization", "locationModal", "specializationModal", "constructor", "group", "cityId", "areaId", "ngOnInit", "updatedScopes", "specializationScopes", "locations", "loadCities", "openLocationModal", "modalElement", "document", "getElementById", "show", "saveLocation", "selectedAreaId", "value", "<PERSON><PERSON><PERSON>", "find", "area", "existingAreaIds", "map", "push", "updateAdvertisements", "areaIds", "subscribe", "next", "response", "console", "log", "showSuccessMessage", "some", "hide", "detectChanges", "error", "err", "showErrorMessage", "getCities", "data", "warn", "complete", "loadAreas", "<PERSON><PERSON><PERSON><PERSON>", "cityName", "patchValue", "areaName", "currentAreaIds", "Array", "isArray", "updatedAreaIds", "filter", "splice", "searchTerm", "formChanged", "originalSpecializations", "staticScopes", "selected", "openSpecializationModal", "scopeName", "item", "specialization", "event", "checked", "target", "exists", "saveChanges", "payload", "for<PERSON>ach", "scope", "key", "res", "message", "fire", "icon", "title", "text", "ɵɵdirectiveInject", "i1", "ProfileService", "i2", "PropertyService", "ChangeDetectorRef", "i3", "FormBuilder", "selectors", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AdvertisementsDetailsComponent_Template", "rf", "ctx", "AdvertisementsDetailsComponent_Template_button_click_17_listener", "_r1", "AdvertisementsDetailsComponent_span_31_Template", "AdvertisementsDetailsComponent_Template_button_click_34_listener", "AdvertisementsDetailsComponent_div_50_Template", "AdvertisementsDetailsComponent_div_51_Template", "AdvertisementsDetailsComponent_ng_container_55_Template", "AdvertisementsDetailsComponent_ng_container_56_Template", "AdvertisementsDetailsComponent_li_59_Template", "AdvertisementsDetailsComponent_li_60_Template", "AdvertisementsDetailsComponent_ng_container_61_Template", "AdvertisementsDetailsComponent_ng_template_62_Template", "ɵɵtemplateRefExtractor", "AdvertisementsDetailsComponent_div_67_Template", "AdvertisementsDetailsComponent_li_74_Template", "AdvertisementsDetailsComponent_ng_container_75_Template", "AdvertisementsDetailsComponent_li_76_Template", "AdvertisementsDetailsComponent_Template_button_click_80_listener", "ɵɵtwoWayListener", "AdvertisementsDetailsComponent_Template_input_ngModelChange_99_listener", "ɵɵtwoWayBindingSet", "AdvertisementsDetailsComponent_button_100_Template", "AdvertisementsDetailsComponent_div_102_Template", "AdvertisementsDetailsComponent_Template_button_click_108_listener", "noCities_r15", "ɵɵtextInterpolate", "ɵɵtwoWayProperty", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "NgModel", "NgForm", "FormGroupDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\shared\\profile\\components\\advertisements-details\\advertisements-details.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\shared\\profile\\components\\advertisements-details\\advertisements-details.component.html"], "sourcesContent": ["import { Component, Input, ChangeDetectorRef, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport {\r\n  FormGroup,\r\n  FormsModule,\r\n  FormBuilder,\r\n  ReactiveFormsModule,\r\n} from '@angular/forms';\r\nimport { Modal } from 'bootstrap';\r\nimport { ProfileService } from '../../services/profile.service';\r\nimport Swal from 'sweetalert2';\r\nimport { PropertyService } from 'src/app/pages/broker/services/property.service';\r\n\r\n// Define interfaces for the tree structure\r\n\r\n@Component({\r\n  selector: 'app-advertisements-details',\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, ReactiveFormsModule],\r\n  templateUrl: './advertisements-details.component.html',\r\n  styleUrl: './advertisements-details.component.scss',\r\n})\r\nexport class AdvertisementsDetailsComponent implements OnInit {\r\n  @Input() user: any = {};\r\n  cities: any[] = [];\r\n  areas: any[] = [];\r\n  selectedCityId: any;\r\n  selectedCityName: string;\r\n  selectedAreaName: string;\r\n  isLoadingCities = false;\r\n\r\n  Form: FormGroup;\r\n\r\n  // New items for the modal forms\r\n  newLocation: string = '';\r\n  newSpecialization: string = '';\r\n\r\n  // References to the modals\r\n  private locationModal: Modal | null = null;\r\n  private specializationModal: Modal | null = null;\r\n\r\n  constructor(\r\n    private profileService: ProfileService,\r\n    private propertyService: PropertyService,\r\n    private cd: ChangeDetectorRef,\r\n    private fb: FormBuilder\r\n  ) {\r\n    // Initialize the form\r\n    this.Form = this.fb.group({\r\n      cityId: [''],\r\n      areaId: [''],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.updatedScopes = [...(this.user.specializationScopes || [])];\r\n    if (this.user && this.user.locations) {\r\n      this.locations = this.user.locations;\r\n    }\r\n    // Load cities when component initializes\r\n    this.loadCities();\r\n  }\r\n\r\n  //***************************************************** */\r\n  //***************************************************** */\r\n  // ******************location modal***********************\r\n\r\n  // Current selected locations\r\n  locations: any[] = [];\r\n\r\n  openLocationModal() {\r\n    this.newLocation = '';\r\n    const modalElement = document.getElementById('addLocationModal');\r\n    if (modalElement) {\r\n      this.locationModal = new Modal(modalElement);\r\n      this.locationModal.show();\r\n    }\r\n  }\r\n\r\n  saveLocation() {\r\n    const selectedAreaId = this.Form.value.areaId;\r\n    const selectedArea = this.areas.find((area) => area.id === selectedAreaId);\r\n\r\n    const existingAreaIds = this.user.areas.map((area: any) => area.id);\r\n\r\n    if (!existingAreaIds.includes(selectedAreaId)) {\r\n      existingAreaIds.push(selectedAreaId);\r\n    }\r\n\r\n    this.profileService\r\n      .updateAdvertisements(this.user.id, { areaIds: existingAreaIds })\r\n      .subscribe({\r\n        next: (response) => {\r\n          // console.log(locationData);\r\n          console.log(response);\r\n          this.showSuccessMessage('Location saved successfully');\r\n\r\n          if (\r\n            !this.user.areas.some((area: any) => area.id === selectedAreaId)\r\n          ) {\r\n            this.user.areas.push({\r\n              id: selectedAreaId,\r\n              name_en: selectedArea\r\n                ? selectedArea.name_en\r\n                : this.selectedAreaName,\r\n            });\r\n          }\r\n\r\n          if (this.locationModal) {\r\n            this.locationModal.hide();\r\n          }\r\n          this.cd.detectChanges();\r\n        },\r\n        error: (err) => {\r\n          this.showErrorMessage(err);\r\n        },\r\n      });\r\n  }\r\n\r\n  loadCities(): void {\r\n    this.isLoadingCities = true;\r\n    this.propertyService.getCities().subscribe({\r\n      next: (response) => {\r\n        if (response && response.data) {\r\n          this.cities = response.data;\r\n        } else {\r\n          console.warn('No cities data in response');\r\n          this.cities = [];\r\n        }\r\n      },\r\n      error: (err) => {\r\n        console.error('Error loading cities:', err);\r\n      },\r\n      complete: () => {\r\n        this.isLoadingCities = false;\r\n        this.cd.detectChanges();\r\n      },\r\n    });\r\n  }\r\n\r\n  loadAreas(cityId?: number): void {\r\n    this.propertyService.getAreas(cityId).subscribe({\r\n      next: (response) => {\r\n        if (response && response.data) {\r\n          this.areas = response.data;\r\n        } else {\r\n          console.warn('No areas data in response');\r\n          this.areas = [];\r\n        }\r\n      },\r\n      error: (err) => {\r\n        console.error('Error loading areas:', err);\r\n        this.areas = [];\r\n      },\r\n      complete: () => {\r\n        this.cd.detectChanges();\r\n      },\r\n    });\r\n  }\r\n  selectCity(cityId: number, cityName: string) {\r\n    this.selectedCityId = cityId;\r\n    this.selectedCityName = cityName;\r\n\r\n    // Check if Form is initialized before patching\r\n    if (this.Form) {\r\n      this.Form.patchValue({\r\n        cityId: cityId,\r\n      });\r\n    }\r\n\r\n    // Load areas for the selected city\r\n    this.loadAreas(cityId);\r\n  }\r\n\r\n  selectArea(areaId: number, areaName: string) {\r\n    this.selectedAreaName = areaName;\r\n\r\n    // Check if Form is initialized before patching\r\n    if (this.Form) {\r\n      this.Form.patchValue({\r\n        areaId: areaId,\r\n      });\r\n    }\r\n  }\r\n  removeLocation(index: number, areaId: number) {\r\n    if (index >= 0 && index < this.user.areas.length) {\r\n      let currentAreaIds = Array.isArray(this.Form.value.areaId)\r\n        ? this.Form.value.areaId\r\n        : [this.Form.value.areaId];\r\n\r\n      let updatedAreaIds = currentAreaIds.filter((id: number) => id !== areaId);\r\n      this.Form.patchValue({ areaId: updatedAreaIds });\r\n      this.user.areas.splice(index, 1);\r\n    }\r\n  }\r\n  //***************************************************** */\r\n  //***************************************************** */\r\n  // ****************specialization modal***********************\r\n\r\n  searchTerm: string = '';\r\n  formChanged: boolean = false;\r\n  originalSpecializations: any[] = [];\r\n  updatedScopes: any[] = [];\r\n\r\n  staticScopes = [\r\n    {\r\n      specialization_scope: 'purchase_sell_outside_compound',\r\n      specializations: [\r\n        'purchasing_sell_residential_outside_compound',\r\n        'purchasing_sell_national_housing_projects_outside_compound',\r\n        'purchasing_sell_administrative_commercial_units_outside_compound',\r\n        'purchasing_sell_industrial_and_warehousing_outside_compound',\r\n        'purchasing_sell_lands_and_ready_projects_outside_compound',\r\n        'purchasing_sell_villas_and_buildings_outside_compound'\r\n      ],\r\n      expanded: false,\r\n      selected: false,\r\n    },\r\n    // {\r\n    //   specialization_scope: 'purchase_sell_inside_compound',\r\n    //   specializations: [\r\n    //     'purchasing_sell_residential_inside_compound',\r\n    //     'purchasing_sell_villas_inside_compound',\r\n    //     'purchasing_sell_administrative_commercial_units_inside_compound'\r\n    //   ],\r\n    //   expanded: false,\r\n    //   selected: false,\r\n    // },\r\n    {\r\n      specialization_scope: 'primary_inside_compound',\r\n      specializations: [\r\n        'purchasing_sell_residential_inside_compound',\r\n        'purchasing_sell_villas_inside_compound',\r\n        'purchasing_sell_administrative_commercial_units_inside_compound'\r\n      ],\r\n      expanded: false,\r\n      selected: false,\r\n    },\r\n    {\r\n      specialization_scope: 'resale_inside_compound',\r\n      specializations: [\r\n        'purchasing_sell_residential_inside_compound',\r\n        'purchasing_sell_villas_inside_compound',\r\n        'purchasing_sell_administrative_commercial_units_inside_compound'\r\n      ],\r\n      expanded: false,\r\n      selected: false,\r\n    },\r\n    {\r\n      specialization_scope: 'rentals_outside_compound',\r\n      specializations: [\r\n        'rent_residential_inside_compound',\r\n        'rent_hotel_Units_inside_compound',\r\n        'rent_administrative_commercial_units_inside_compound'\r\n      ],\r\n      expanded: false,\r\n      selected: false,\r\n    },\r\n    {\r\n      specialization_scope: 'rentals_inside_compound',\r\n      specializations: [\r\n        'rent_residential_outside_compound',\r\n        'rent_national_housing_projects_compound',\r\n        'rent_administrative_commercial_units_outside_compound',\r\n        'rent_industrial_and_warehousing_outside_compound',\r\n        'rent_hotel_units_outside_compound'\r\n      ],\r\n      expanded: false,\r\n      selected: false,\r\n    },\r\n  ];\r\n\r\n  // Open specialization modal\r\n  openSpecializationModal() {\r\n     this.searchTerm = '';\r\n     const modalElement = document.getElementById('addSpecializationModal');\r\n    if (modalElement) {\r\n      this.specializationModal = new Modal(modalElement);\r\n      this.specializationModal.show();\r\n    }\r\n  }\r\n\r\n  hasScope(scopeName: string): boolean {\r\n    return this.updatedScopes.some(\r\n      (item) => item.specialization_scope === scopeName\r\n    );\r\n  }\r\n\r\n  hasSpecialization(scopeName: string, specialization: string): boolean {\r\n    if (!this.user || !this.user.specializationScopes) return false;\r\n\r\n    return this.user.specializationScopes.some(\r\n      (item: any) =>\r\n        item.specialization_scope === scopeName &&\r\n        item.specialization === specialization\r\n    );\r\n  }\r\n\r\n  onScopeChange(scopeName: string, specialization: string, event: Event): void {\r\n    console.log(specialization);\r\n    const checked = (event.target as HTMLInputElement).checked;\r\n    if (checked) {\r\n      const exists = this.updatedScopes.some(\r\n        (item) =>\r\n          item.specialization_scope === scopeName &&\r\n          item.specialization === specialization\r\n      );\r\n\r\n      if (!exists) {\r\n        this.updatedScopes.push({\r\n          specialization_scope: scopeName,\r\n          specialization: specialization,\r\n        });\r\n      }\r\n    } else {\r\n      this.updatedScopes = this.updatedScopes.filter(\r\n        (item) =>\r\n          !(\r\n            item.specialization_scope === scopeName &&\r\n            item.specialization === specialization\r\n          )\r\n      );\r\n    }\r\n\r\n    this.formChanged = true;\r\n  }\r\n\r\n  saveChanges() {\r\n    let payload: any = { specializationScopes: {} };\r\n\r\n    console.log('Updated scopes:', this.updatedScopes);\r\n\r\n    this.updatedScopes.forEach((scope) => {\r\n      const key = scope.specialization_scope;\r\n      if (!key) return;\r\n      if (!payload.specializationScopes[key]) {\r\n        payload.specializationScopes[key] = [];\r\n      }\r\n\r\n      payload.specializationScopes[key].push(scope.specialization);\r\n    });\r\n\r\n    console.log('Payload:', payload);\r\n\r\n    this.profileService.updateAdvertisements(this.user.id, payload).subscribe({\r\n      next: (res) => {\r\n        this.formChanged = false;\r\n        console.log('Payload:', payload);\r\n        console.log('Response:', res);\r\n\r\n        this.showSuccessMessage('Saved successfully');\r\n        this.specializationModal?.hide();\r\n      },\r\n      error: (err) => {\r\n        this.showErrorMessage(err);\r\n      },\r\n    });\r\n  }\r\n\r\n  private showSuccessMessage(message: string): void {\r\n    Swal.fire({\r\n      icon: 'success',\r\n      title: 'Success',\r\n      text: message,\r\n    });\r\n  }\r\n\r\n  private showErrorMessage(error: any): void {\r\n    Swal.fire({\r\n      icon: 'error',\r\n      title: 'Error',\r\n      text: error.error.message,\r\n    });\r\n  }\r\n\r\n  specializationDisplayMap: { [key: string]: string } = {\r\n  'purchasing_sell_residential_outside_compound': 'Residential - Outside Compound',\r\n  'purchasing_sell_national_housing_projects_outside_compound': 'National Housing - Outside Compound',\r\n  'purchasing_sell_administrative_commercial_units_outside_compound': 'Commercial Units - Outside Compound',\r\n  'purchasing_sell_industrial_and_warehousing_outside_compound': 'Industrial/Warehousing - Outside Compound',\r\n  'purchasing_sell_lands_and_ready_projects_outside_compound': 'Lands & Ready Projects',\r\n  'purchasing_sell_villas_and_buildings_outside_compound': 'Villas/Buildings - Outside Compound',\r\n  'purchasing_sell_residential_inside_compound': 'Residential - Inside Compound',\r\n  'purchasing_sell_villas_inside_compound': 'Villas - Inside Compound',\r\n  'purchasing_sell_administrative_commercial_units_inside_compound': 'Commercial Units - Inside Compound',\r\n  'rent_residential_inside_compound': 'Rent Residential - Inside Compound',\r\n  'rent_hotel_Units_inside_compound': 'Rent Hotel Units - Inside Compound',\r\n  'rent_administrative_commercial_units_inside_compound': 'Rent Commercial Units - Inside Compound',\r\n  'rent_residential_outside_compound': 'Rent Residential - Outside Compound',\r\n  'rent_national_housing_projects_compound': 'Rent National Housing Projects',\r\n  'rent_administrative_commercial_units_outside_compound': 'Rent Commercial Units - Outside Compound',\r\n  'rent_industrial_and_warehousing_outside_compound': 'Rent Industrial/Warehousing - Outside Compound',\r\n  'rent_hotel_units_outside_compound': 'Rent Hotel Units - Outside Compound'\r\n};\r\n\r\n}\r\n", "<!-- profile details -->\r\n\r\n<div class=\"card mb-5 mb-xl-10\">\r\n  <div class=\"card-header border-0 bg-light-dark-blue\" role=\"button\" data-bs-toggle=\"collapse\"\r\n    data-bs-target=\"#kt_account_ads_details\" aria-expanded=\"true\" aria-controls=\"kt_account_ads_details\">\r\n    <div class=\"card-title m-0\">\r\n      <h3 class=\"fw-bolder m-0\">Advertisements and Properties Details</h3>\r\n    </div>\r\n  </div>\r\n  <div id=\"kt_account_ads_details\" class=\"collapse show\">\r\n    <form novalidate=\"\" class=\"form\">\r\n      <div class=\"card-body border-top p-9\">\r\n        <div class=\"row mb-6\">\r\n          <label class=\"col-lg-4 col-form-label fw-bold fs-6\">\r\n            <span>Specializations </span>\r\n          </label>\r\n          <div class=\"col-lg-8 fv-row\">\r\n            <div class=\"row\">\r\n              <div class=\"col-md-9\"></div>\r\n              <div class=\"col-md-3\">\r\n                <div class=\"align-items-end text-end\">\r\n                  <button class=\"btn btn-light-dark-blue btn-sm\" (click)=\"openSpecializationModal()\">\r\n                    <i class=\"fa-solid fa-arrow-right\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- <div class=\"row mb-6\">\r\n  <label class=\"col-lg-4 col-form-label fw-bold fs-6\">\r\n    <span>Specializations</span>\r\n  </label>\r\n  <div class=\"col-lg-8 fv-row\">\r\n    <div class=\"row\">\r\n      <div class=\"col-md-9\">\r\n        <ng-container *ngIf=\"user?.specializationScopes?.length; else noSpecializations\">\r\n          <div class=\"badge bg-secondary text-white me-2 mb-1\" *ngFor=\"let s of user.specializationScopes\">\r\n            {{ specializationDisplayMap[s.specialization] }}\r\n          </div>\r\n        </ng-container>\r\n        <ng-template #noSpecializations>\r\n          <span class=\"text-muted\">No specializations selected</span>\r\n        </ng-template>\r\n      </div>\r\n\r\n      <div class=\"col-md-3 text-end\">\r\n        <button class=\"btn btn-light-dark-blue btn-sm\" (click)=\"openSpecializationModal()\">\r\n          <i class=\"fa-solid fa-arrow-right\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div> -->\r\n        <div class=\"row mb-6\">\r\n          <label class=\"col-lg-4 col-form-label fw-bold fs-6\">\r\n            <span>Locations</span>\r\n            <i class=\"ki-duotone ki-information-5 text-danger fs-6 ms-1\" data-bs-toggle=\"tooltip\"\r\n              data-bs-placement=\"top\" data-bs-custom-class=\"red-tooltip\" title=\"يمكن تغيير المناطق بعد ١٧ يوماً\">\r\n              <span class=\"path1\"></span>\r\n              <span class=\"path2\"></span>\r\n              <span class=\"path3\"></span>\r\n            </i>\r\n          </label>\r\n          <div class=\"col-lg-8 fv-row\">\r\n            <div class=\"row\">\r\n              <div class=\"col-md-9\">\r\n                <div class=\"d-flex flex-wrap fw-bold fs-6 mb-4 pe-2\">\r\n                  <span *ngFor=\"let loc of user.areas; let i = index\"\r\n                    class=\"d-flex align-items-center me-5 mb-2 fw-bolder py-4 px-5 fs-6 badge badge-light-dark-blue text-dark-blue\">\r\n                    {{ loc.name_en }}\r\n                    <button class=\"btn btn-icon btn-light btn-active-light-danger rounded-circle ms-2\" type=\"button\"\r\n                      (click)=\"\r\n                        removeLocation(i, loc.id); $event.stopPropagation()\r\n                      \" title=\"Remove\">\r\n                      <i class=\"fa-solid fa-times\"></i>\r\n                    </button>\r\n                  </span>\r\n                </div>\r\n              </div>\r\n              <div class=\"col-md-3\">\r\n                <div class=\"align-items-end text-end\">\r\n                  <button class=\"btn btn-light-dark-blue btn-sm\" (click)=\"openLocationModal()\">\r\n                    <i class=\"fa-solid fa-plus\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </form>\r\n  </div>\r\n</div>\r\n\r\n<!-- ****************************************************************** -->\r\n\r\n<!-- Modal for adding locations -->\r\n<div class=\"modal fade\" id=\"addLocationModal\" tabindex=\"-1\" aria-labelledby=\"addLocationModalLabel\" aria-hidden=\"true\">\r\n  <div class=\"modal-dialog modal-dialog-centered\">\r\n    <div class=\"modal-content\">\r\n      <div class=\"modal-header bg-light-primary\">\r\n        <h5 class=\"modal-title fw-bold text-dark-blue\" id=\"addLocationModalLabel\">\r\n          <i class=\"fas fa-map-marker-alt me-2\"></i> Add New Location\r\n        </h5>\r\n        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\r\n      </div>\r\n      <div class=\"modal-body\" [formGroup]=\"Form\">\r\n        <div class=\"card mb-4\">\r\n          <div class=\"card-body p-4\">\r\n            <!-- City Selection -->\r\n            <div class=\"mb-4\">\r\n              <label class=\"form-label fw-bold text-start d-block fs-6\">City</label>\r\n\r\n              <!-- Loading indicator -->\r\n              <div *ngIf=\"isLoadingCities\" class=\"d-flex align-items-center text-primary mb-2\">\r\n                <i class=\"fas fa-spinner fa-spin me-2\"></i> Loading cities...\r\n              </div>\r\n\r\n              <!-- Debug info -->\r\n              <div *ngIf=\"!isLoadingCities && cities.length === 0\" class=\"alert alert-warning py-2 mb-2\">\r\n                <i class=\"fas fa-exclamation-triangle me-2\"></i> No cities\r\n                available\r\n              </div>\r\n\r\n              <div class=\"dropdown bg-secondary\">\r\n                <button\r\n                  class=\"btn btn-outline-primary w-100 text-start d-flex justify-content-between align-items-center py-2\"\r\n                  type=\"button\" id=\"cityDropdownStep1\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\"\r\n                  [disabled]=\"isLoadingCities\">\r\n                  <span>\r\n                    <ng-container *ngIf=\"isLoadingCities\">\r\n                      <i class=\"fas fa-spinner fa-spin me-2\"></i> Loading...\r\n                    </ng-container>\r\n                    <ng-container *ngIf=\"!isLoadingCities\">\r\n                      {{ selectedCityName || \"Select City\" }}\r\n                    </ng-container>\r\n                  </span>\r\n                  <i class=\"fas fa-chevron-down\"></i>\r\n                </button>\r\n\r\n                <ul class=\"dropdown-menu w-100 shadow-sm\" aria-labelledby=\"cityDropdownStep1\" style=\"\r\n                    max-height: 250px;\r\n                    overflow-y: auto;\r\n                    position: absolute;\r\n                    z-index: 1000;\r\n                  \">\r\n                  <!-- Loading indicator -->\r\n                  <li *ngIf=\"isLoadingCities\" class=\"dropdown-item disabled text-primary\">\r\n                    <i class=\"fas fa-spinner fa-spin me-2\"></i> Loading\r\n                    cities...\r\n                  </li>\r\n\r\n                  <!-- Debug info -->\r\n                  <li *ngIf=\"!isLoadingCities\" class=\"dropdown-item disabled small text-muted\">\r\n                    Available Cities: {{ cities.length }}\r\n                  </li>\r\n\r\n                  <ng-container *ngIf=\"cities && cities.length > 0; else noCities\">\r\n                    <li *ngFor=\"let city of cities\" style=\"cursor: pointer\">\r\n                      <a class=\"dropdown-item text-start py-2\" (click)=\"selectCity(city.id, city.name_en)\">\r\n                        {{ city.name_en }}\r\n                      </a>\r\n                    </li>\r\n                  </ng-container>\r\n\r\n                  <ng-template #noCities>\r\n                    <li>\r\n                      <a class=\"dropdown-item text-start disabled py-2\">\r\n                        No cities available\r\n                      </a>\r\n                    </li>\r\n                  </ng-template>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Area Selection -->\r\n            <div class=\"mb-2\">\r\n              <label class=\"form-label fw-bold text-start d-block fs-6\">Area</label>\r\n\r\n              <!-- Show message when no city is selected -->\r\n              <div *ngIf=\"!selectedCityId\" class=\"alert alert-warning py-2 mb-2\">\r\n                <i class=\"fas fa-exclamation-triangle me-2\"></i> Please select a\r\n                city first\r\n              </div>\r\n\r\n              <div class=\"dropdown bg-secondary\">\r\n                <button\r\n                  class=\"btn btn-outline-primary w-100 text-start d-flex justify-content-between align-items-center py-2\"\r\n                  type=\"button\" id=\"areaDropdownStep1\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\"\r\n                  [disabled]=\"!selectedCityId\">\r\n                  <span>{{ selectedAreaName || \"Select Area\" }}</span>\r\n                  <i class=\"fas fa-chevron-down\"></i>\r\n                </button>\r\n\r\n                <ul class=\"dropdown-menu w-100 shadow-sm\" aria-labelledby=\"areaDropdownStep1\" style=\"\r\n                    max-height: 250px;\r\n                    overflow-y: auto;\r\n                    position: absolute;\r\n                    z-index: 1000;\r\n                  \">\r\n                  <!-- Show message when no city is selected -->\r\n                  <li *ngIf=\"!selectedCityId\" class=\"dropdown-item disabled text-danger\">\r\n                    <i class=\"fas fa-exclamation-circle me-2\"></i> No areas\r\n                    available. Please select a city first.\r\n                  </li>\r\n\r\n                  <!-- Show areas when available -->\r\n                  <ng-container *ngIf=\"selectedCityId && areas.length > 0\">\r\n                    <li *ngFor=\"let area of areas\">\r\n                      <a class=\"dropdown-item text-start py-2\" (click)=\"selectArea(area.id, area.name_en)\">\r\n                        {{ area.name_en }}\r\n                      </a>\r\n                    </li>\r\n                  </ng-container>\r\n\r\n                  <!-- Show message when city is selected but no areas are available -->\r\n                  <li *ngIf=\"selectedCityId && areas.length === 0\" class=\"dropdown-item disabled\">\r\n                    <i class=\"fas fa-info-circle me-2\"></i> No areas available\r\n                    for this city\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"modal-footer\">\r\n        <button type=\"button\" class=\"btn btn-light-dark px-4\" data-bs-dismiss=\"modal\">\r\n          Cancel\r\n        </button>\r\n        <button type=\"button\" class=\"btn btn-primary px-4\" (click)=\"saveLocation()\">\r\n          <i class=\"fa-solid fa-save me-2\"></i> Save Location\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<!-- ****************************************************************** -->\r\n\r\n<!-- Modal for adding specializations -->\r\n<div class=\"modal fade\" id=\"addSpecializationModal\" tabindex=\"-1\" aria-labelledby=\"addSpecializationModalLabel\"\r\n  aria-hidden=\"true\">\r\n  <div class=\"modal-dialog modal-dialog-centered modal-lg\">\r\n    <div class=\"modal-content\">\r\n      <div class=\"modal-header bg-light-primary\">\r\n        <h5 class=\"modal-title fw-bold\" id=\"addSpecializationModalLabel\">\r\n          Select Specializations\r\n        </h5>\r\n        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\r\n      </div>\r\n      <div class=\"modal-body\">\r\n        <div class=\"mb-4\">\r\n          <h6 class=\"form-label fw-bold mb-3\">\r\n            Choose from the specialization tree:\r\n          </h6>\r\n\r\n          <div class=\"specialization-tree-container\">\r\n            <!-- Search box -->\r\n            <div class=\"mb-4\">\r\n              <div class=\"input-group\">\r\n                <span class=\"input-group-text bg-light\">\r\n                  <i class=\"fa-solid fa-search\"></i>\r\n                </span>\r\n                <input type=\"text\" class=\"form-control form-control-solid\" placeholder=\"Search specializations...\"\r\n                  [(ngModel)]=\"searchTerm\" name=\"searchTerm\" />\r\n                <button *ngIf=\"searchTerm\" class=\"btn btn-light-primary\" type=\"button\">\r\n                  <i class=\"fa-solid fa-times\"></i>\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Simplified 2-level Tree view -->\r\n            <div class=\"specialization-tree\">\r\n              <div *ngFor=\"let scope of staticScopes\" class=\"tree-node\">\r\n                <!-- Scope -->\r\n                <div class=\"d-flex align-items-center py-2\" style=\"cursor: pointer\"\r\n                  (click)=\"scope.expanded = !scope.expanded\">\r\n                  <span class=\"me-2\" (click)=\"\r\n                      $event.stopPropagation(); scope.expanded = !scope.expanded\r\n                    \">\r\n                    <i [class]=\"\r\n                        scope.expanded\r\n                          ? 'fas fa-chevron-down'\r\n                          : 'fas fa-chevron-right'\r\n                      \"></i>\r\n                  </span>\r\n\r\n                  <div class=\"form-check me-2\" (click)=\"$event.stopPropagation()\">\r\n                    <input class=\"form-check-input\" type=\"checkbox\" [checked]=\"hasScope(scope.specialization_scope)\" />\r\n                  </div>\r\n\r\n                  <div class=\"fw-bold fs-5\">\r\n                    {{ scope.specialization_scope }}\r\n                  </div>\r\n                </div>\r\n\r\n                <div *ngIf=\"scope.expanded\" class=\"ms-5 ps-3 py-1\">\r\n                  <div *ngFor=\"let type of scope.specializations\" class=\"d-flex align-items-center mb-1\">\r\n                    <div class=\"form-check me-2\">\r\n                      <input class=\"form-check-input\" type=\"checkbox\" [checked]=\"\r\n                          hasSpecialization(scope.specialization_scope, type)\r\n                        \" (change)=\"\r\n                          onScopeChange(\r\n                            scope.specialization_scope,\r\n                            type,\r\n                            $event\r\n                          )\r\n                        \" />\r\n                    </div>\r\n                    <div class=\"fw-semibold text-primary\"></div>\r\n                    <div class=\"ms-3 text-muted\">\r\n                      <ng-container *ngIf=\"\r\n                          type.includes('Factories') ||\r\n                          type.includes('Warehouses')\r\n                        \">\r\n                        <span class=\"badge badge-light-info me-2\">Industrial</span>\r\n                      </ng-container>\r\n                      <ng-container *ngIf=\"\r\n                          type.includes('Administrative') ||\r\n                          type.includes('Commercial')\r\n                        \">\r\n                        <span class=\"badge badge-light-info me-2\">Commercial</span>\r\n                      </ng-container>\r\n                      <ng-container *ngIf=\"\r\n                          type.includes('Apartment') ||\r\n                          type.includes('Duplex') ||\r\n                          type.includes('Penthouse') ||\r\n                          type.includes('Townhouse')\r\n                        \">\r\n                        <span class=\"badge badge-light-info me-2\">Residential</span>\r\n                      </ng-container>\r\n                      <ng-container *ngIf=\"\r\n                          type.includes('Sakan Misr') ||\r\n                          type.includes('Dar Misr') ||\r\n                          type.includes('Ganat Misr')\r\n                        \">\r\n                        <span class=\"badge badge-light-info me-2\">National Project</span>\r\n                      </ng-container>\r\n                      <!-- {{ type }} -->\r\n                      {{ specializationDisplayMap[type] || type }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"form-text text-muted mt-2\">\r\n            Select scopes and types. Selecting a scope will select all types\r\n            under it.\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"modal-footer\">\r\n        <button type=\"button\" class=\"btn btn-light\" data-bs-dismiss=\"modal\">\r\n          Cancel\r\n        </button>\r\n        <button type=\"button\" class=\"btn btn-primary\" [disabled]=\"!formChanged\" (click)=\"saveChanges()\">\r\n          <i class=\"fa-solid fa-save me-1\"></i> Save\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAEEC,WAAW,EAEXC,mBAAmB,QACd,gBAAgB;AACvB,SAASC,KAAK,QAAQ,WAAW;AAEjC,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;IC0DZC,EAAA,CAAAC,cAAA,eACkH;IAChHD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,iBAGmB;IAFjBD,EAAA,CAAAG,UAAA,mBAAAC,wEAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAH,MAAA,CAAAI,SAAA;MAAA,MAAAC,IAAA,GAAAL,MAAA,CAAAM,KAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAC2BD,MAAA,CAAAE,cAAA,CAAAJ,IAAA,EAAAF,MAAA,CAAAO,EAAA,CAAyB;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAEZ,MAAA,CAAAa,eAAA,EACrD;IAAA;IACDlB,EAAA,CAAAmB,SAAA,YAAiC;IAErCnB,EADE,CAAAoB,YAAA,EAAS,EACJ;;;;IAPLpB,EAAA,CAAAqB,SAAA,EACA;IADArB,EAAA,CAAAsB,kBAAA,MAAAb,MAAA,CAAAc,OAAA,MACA;;;;;IA4CNvB,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAmB,SAAA,YAA2C;IAACnB,EAAA,CAAAE,MAAA,0BAC9C;IAAAF,EAAA,CAAAoB,YAAA,EAAM;;;;;IAGNpB,EAAA,CAAAC,cAAA,cAA2F;IACzFD,EAAA,CAAAmB,SAAA,YAAgD;IAACnB,EAAA,CAAAE,MAAA,4BAEnD;IAAAF,EAAA,CAAAoB,YAAA,EAAM;;;;;IAQApB,EAAA,CAAAwB,uBAAA,GAAsC;IACpCxB,EAAA,CAAAmB,SAAA,YAA2C;IAACnB,EAAA,CAAAE,MAAA,mBAC9C;;;;;;IACAF,EAAA,CAAAwB,uBAAA,GAAuC;IACrCxB,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAAsB,kBAAA,MAAAT,MAAA,CAAAY,gBAAA,uBACF;;;;;IAYFzB,EAAA,CAAAC,cAAA,aAAwE;IACtED,EAAA,CAAAmB,SAAA,YAA2C;IAACnB,EAAA,CAAAE,MAAA,0BAE9C;IAAAF,EAAA,CAAAoB,YAAA,EAAK;;;;;IAGLpB,EAAA,CAAAC,cAAA,aAA6E;IAC3ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAoB,YAAA,EAAK;;;;IADHpB,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAAsB,kBAAA,wBAAAT,MAAA,CAAAa,MAAA,CAAAC,MAAA,MACF;;;;;;IAII3B,EADF,CAAAC,cAAA,aAAwD,YAC+B;IAA5CD,EAAA,CAAAG,UAAA,mBAAAyB,gFAAA;MAAA,MAAAC,OAAA,GAAA7B,EAAA,CAAAO,aAAA,CAAAuB,GAAA,EAAApB,SAAA;MAAA,MAAAG,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAkB,UAAA,CAAAF,OAAA,CAAAb,EAAA,EAAAa,OAAA,CAAAN,OAAA,CAAiC;IAAA,EAAC;IAClFvB,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAoB,YAAA,EAAI,EACD;;;;IAFDpB,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAsB,kBAAA,MAAAO,OAAA,CAAAN,OAAA,MACF;;;;;IAJJvB,EAAA,CAAAwB,uBAAA,GAAiE;IAC/DxB,EAAA,CAAAgC,UAAA,IAAAC,4DAAA,iBAAwD;;;;;IAAnCjC,EAAA,CAAAqB,SAAA,EAAS;IAATrB,EAAA,CAAAkC,UAAA,YAAArB,MAAA,CAAAa,MAAA,CAAS;;;;;IAS5B1B,EADF,CAAAC,cAAA,SAAI,YACgD;IAChDD,EAAA,CAAAE,MAAA,4BACF;IACFF,EADE,CAAAoB,YAAA,EAAI,EACD;;;;;IAWXpB,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAAmB,SAAA,YAAgD;IAACnB,EAAA,CAAAE,MAAA,mCAEnD;IAAAF,EAAA,CAAAoB,YAAA,EAAM;;;;;IAkBFpB,EAAA,CAAAC,cAAA,aAAuE;IACrED,EAAA,CAAAmB,SAAA,YAA8C;IAACnB,EAAA,CAAAE,MAAA,wDAEjD;IAAAF,EAAA,CAAAoB,YAAA,EAAK;;;;;;IAKDpB,EADF,CAAAC,cAAA,SAA+B,YACwD;IAA5CD,EAAA,CAAAG,UAAA,mBAAAgC,gFAAA;MAAA,MAAAC,QAAA,GAAApC,EAAA,CAAAO,aAAA,CAAA8B,GAAA,EAAA3B,SAAA;MAAA,MAAAG,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAyB,UAAA,CAAAF,QAAA,CAAApB,EAAA,EAAAoB,QAAA,CAAAb,OAAA,CAAiC;IAAA,EAAC;IAClFvB,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAoB,YAAA,EAAI,EACD;;;;IAFDpB,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAsB,kBAAA,MAAAc,QAAA,CAAAb,OAAA,MACF;;;;;IAJJvB,EAAA,CAAAwB,uBAAA,GAAyD;IACvDxB,EAAA,CAAAgC,UAAA,IAAAO,4DAAA,iBAA+B;;;;;IAAVvC,EAAA,CAAAqB,SAAA,EAAQ;IAARrB,EAAA,CAAAkC,UAAA,YAAArB,MAAA,CAAA2B,KAAA,CAAQ;;;;;IAQ/BxC,EAAA,CAAAC,cAAA,aAAgF;IAC9ED,EAAA,CAAAmB,SAAA,YAAuC;IAACnB,EAAA,CAAAE,MAAA,yCAE1C;IAAAF,EAAA,CAAAoB,YAAA,EAAK;;;;;IA+CPpB,EAAA,CAAAC,cAAA,iBAAuE;IACrED,EAAA,CAAAmB,SAAA,YAAiC;IACnCnB,EAAA,CAAAoB,YAAA,EAAS;;;;;IA4CHpB,EAAA,CAAAwB,uBAAA,GAGI;IACFxB,EAAA,CAAAC,cAAA,gBAA0C;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAoB,YAAA,EAAO;;;;;;IAE7DpB,EAAA,CAAAwB,uBAAA,GAGI;IACFxB,EAAA,CAAAC,cAAA,gBAA0C;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAoB,YAAA,EAAO;;;;;;IAE7DpB,EAAA,CAAAwB,uBAAA,GAKI;IACFxB,EAAA,CAAAC,cAAA,gBAA0C;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAoB,YAAA,EAAO;;;;;;IAE9DpB,EAAA,CAAAwB,uBAAA,GAII;IACFxB,EAAA,CAAAC,cAAA,gBAA0C;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAoB,YAAA,EAAO;;;;;;;IArCnEpB,EAFJ,CAAAC,cAAA,eAAuF,eACxD,iBASrB;IANFD,EAAA,CAAAG,UAAA,oBAAAsC,oFAAApC,MAAA;MAAA,MAAAqC,QAAA,GAAA1C,EAAA,CAAAO,aAAA,CAAAoC,IAAA,EAAAjC,SAAA;MAAA,MAAAkC,SAAA,GAAA5C,EAAA,CAAAc,aAAA,IAAAJ,SAAA;MAAA,MAAAG,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAEfJ,MAAA,CAAAgC,aAAA,CAAAD,SAAA,CAAAE,oBAAA,EAAAJ,QAAA,EAAArC,MAAA,CAIU;IAAA,EAAI;IACLL,EATE,CAAAoB,YAAA,EAQM,EACF;IACNpB,EAAA,CAAAmB,SAAA,eAA4C;IAC5CnB,EAAA,CAAAC,cAAA,eAA6B;IAqB3BD,EApBA,CAAAgC,UAAA,IAAAe,0EAAA,2BAGI,IAAAC,0EAAA,2BAMA,IAAAC,0EAAA,2BAQA,IAAAC,0EAAA,2BAOA;IAIJlD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAoB,YAAA,EAAM,EACF;;;;;;IA1C8CpB,EAAA,CAAAqB,SAAA,GAE7C;IAF6CrB,EAAA,CAAAkC,UAAA,YAAArB,MAAA,CAAAsC,iBAAA,CAAAP,SAAA,CAAAE,oBAAA,EAAAJ,QAAA,EAE7C;IAUY1C,EAAA,CAAAqB,SAAA,GAGb;IAHarB,EAAA,CAAAkC,UAAA,SAAAQ,QAAA,CAAAU,QAAA,iBAAAV,QAAA,CAAAU,QAAA,eAGb;IAGapD,EAAA,CAAAqB,SAAA,EAGb;IAHarB,EAAA,CAAAkC,UAAA,SAAAQ,QAAA,CAAAU,QAAA,sBAAAV,QAAA,CAAAU,QAAA,eAGb;IAGapD,EAAA,CAAAqB,SAAA,EAKf;IALerB,EAAA,CAAAkC,UAAA,SAAAQ,QAAA,CAAAU,QAAA,iBAAAV,QAAA,CAAAU,QAAA,cAAAV,QAAA,CAAAU,QAAA,iBAAAV,QAAA,CAAAU,QAAA,cAKf;IAGepD,EAAA,CAAAqB,SAAA,EAId;IAJcrB,EAAA,CAAAkC,UAAA,SAAAQ,QAAA,CAAAU,QAAA,kBAAAV,QAAA,CAAAU,QAAA,gBAAAV,QAAA,CAAAU,QAAA,eAId;IAIDpD,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAAsB,kBAAA,MAAAT,MAAA,CAAAwC,wBAAA,CAAAX,QAAA,KAAAA,QAAA,MACF;;;;;IA5CJ1C,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAgC,UAAA,IAAAsB,2DAAA,mBAAuF;IA6CzFtD,EAAA,CAAAoB,YAAA,EAAM;;;;IA7CkBpB,EAAA,CAAAqB,SAAA,EAAwB;IAAxBrB,EAAA,CAAAkC,UAAA,YAAAU,SAAA,CAAAW,eAAA,CAAwB;;;;;;IAtBhDvD,EAFF,CAAAC,cAAA,cAA0D,cAGX;IAA3CD,EAAA,CAAAG,UAAA,mBAAAqD,qEAAA;MAAA,MAAAZ,SAAA,GAAA5C,EAAA,CAAAO,aAAA,CAAAkD,IAAA,EAAA/C,SAAA;MAAA,OAAAV,EAAA,CAAAiB,WAAA,CAAA2B,SAAA,CAAAc,QAAA,IAAAd,SAAA,CAAAc,QAAA;IAAA,EAA0C;IAC1C1D,EAAA,CAAAC,cAAA,eAEI;IAFeD,EAAA,CAAAG,UAAA,mBAAAwD,sEAAAtD,MAAA;MAAA,MAAAuC,SAAA,GAAA5C,EAAA,CAAAO,aAAA,CAAAkD,IAAA,EAAA/C,SAAA;MACQL,MAAA,CAAAa,eAAA,EAAwB;MAAA,OAAAlB,EAAA,CAAAiB,WAAA,CAAA2B,SAAA,CAAAc,QAAA,IAAAd,SAAA,CAAAc,QAAA;IAAA,EAChD;IACD1D,EAAA,CAAAmB,SAAA,QAIQ;IACVnB,EAAA,CAAAoB,YAAA,EAAO;IAEPpB,EAAA,CAAAC,cAAA,cAAgE;IAAnCD,EAAA,CAAAG,UAAA,mBAAAyD,qEAAAvD,MAAA;MAAAL,EAAA,CAAAO,aAAA,CAAAkD,IAAA;MAAA,OAAAzD,EAAA,CAAAiB,WAAA,CAASZ,MAAA,CAAAa,eAAA,EAAwB;IAAA,EAAC;IAC7DlB,EAAA,CAAAmB,SAAA,gBAAmG;IACrGnB,EAAA,CAAAoB,YAAA,EAAM;IAENpB,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAoB,YAAA,EAAM,EACF;IAENpB,EAAA,CAAAgC,UAAA,IAAA6B,qDAAA,kBAAmD;IA+CrD7D,EAAA,CAAAoB,YAAA,EAAM;;;;;IA/DGpB,EAAA,CAAAqB,SAAA,GAIA;IAJArB,EAAA,CAAA8D,UAAA,CAAAlB,SAAA,CAAAc,QAAA,kDAIA;IAI6C1D,EAAA,CAAAqB,SAAA,GAAgD;IAAhDrB,EAAA,CAAAkC,UAAA,YAAArB,MAAA,CAAAkD,QAAA,CAAAnB,SAAA,CAAAE,oBAAA,EAAgD;IAIhG9C,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAsB,kBAAA,MAAAsB,SAAA,CAAAE,oBAAA,MACF;IAGI9C,EAAA,CAAAqB,SAAA,EAAoB;IAApBrB,EAAA,CAAAkC,UAAA,SAAAU,SAAA,CAAAc,QAAA,CAAoB;;;AD9R1C;AASA,OAAM,MAAOM,8BAA8B;EAoB/BC,cAAA;EACAC,eAAA;EACAC,EAAA;EACAC,EAAA;EAtBDC,IAAI,GAAQ,EAAE;EACvB3C,MAAM,GAAU,EAAE;EAClBc,KAAK,GAAU,EAAE;EACjB8B,cAAc;EACd7C,gBAAgB;EAChB8C,gBAAgB;EAChBC,eAAe,GAAG,KAAK;EAEvBC,IAAI;EAEJ;EACAC,WAAW,GAAW,EAAE;EACxBC,iBAAiB,GAAW,EAAE;EAE9B;EACQC,aAAa,GAAiB,IAAI;EAClCC,mBAAmB,GAAiB,IAAI;EAEhDC,YACUb,cAA8B,EAC9BC,eAAgC,EAChCC,EAAqB,EACrBC,EAAe;IAHf,KAAAH,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,EAAE,GAAFA,EAAE;IAEV;IACA,IAAI,CAACK,IAAI,GAAG,IAAI,CAACL,EAAE,CAACW,KAAK,CAAC;MACxBC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZC,MAAM,EAAE,CAAC,EAAE;KACZ,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,GAAG,CAAC,IAAI,IAAI,CAACd,IAAI,CAACe,oBAAoB,IAAI,EAAE,CAAC,CAAC;IAChE,IAAI,IAAI,CAACf,IAAI,IAAI,IAAI,CAACA,IAAI,CAACgB,SAAS,EAAE;MACpC,IAAI,CAACA,SAAS,GAAG,IAAI,CAAChB,IAAI,CAACgB,SAAS;IACtC;IACA;IACA,IAAI,CAACC,UAAU,EAAE;EACnB;EAEA;EACA;EACA;EAEA;EACAD,SAAS,GAAU,EAAE;EAErBE,iBAAiBA,CAAA;IACf,IAAI,CAACb,WAAW,GAAG,EAAE;IACrB,MAAMc,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,kBAAkB,CAAC;IAChE,IAAIF,YAAY,EAAE;MAChB,IAAI,CAACZ,aAAa,GAAG,IAAI9E,KAAK,CAAC0F,YAAY,CAAC;MAC5C,IAAI,CAACZ,aAAa,CAACe,IAAI,EAAE;IAC3B;EACF;EAEAC,YAAYA,CAAA;IACV,MAAMC,cAAc,GAAG,IAAI,CAACpB,IAAI,CAACqB,KAAK,CAACb,MAAM;IAC7C,MAAMc,YAAY,GAAG,IAAI,CAACvD,KAAK,CAACwD,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACjF,EAAE,KAAK6E,cAAc,CAAC;IAE1E,MAAMK,eAAe,GAAG,IAAI,CAAC7B,IAAI,CAAC7B,KAAK,CAAC2D,GAAG,CAAEF,IAAS,IAAKA,IAAI,CAACjF,EAAE,CAAC;IAEnE,IAAI,CAACkF,eAAe,CAAC9C,QAAQ,CAACyC,cAAc,CAAC,EAAE;MAC7CK,eAAe,CAACE,IAAI,CAACP,cAAc,CAAC;IACtC;IAEA,IAAI,CAAC5B,cAAc,CAChBoC,oBAAoB,CAAC,IAAI,CAAChC,IAAI,CAACrD,EAAE,EAAE;MAAEsF,OAAO,EAAEJ;IAAe,CAAE,CAAC,CAChEK,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB;QACAC,OAAO,CAACC,GAAG,CAACF,QAAQ,CAAC;QACrB,IAAI,CAACG,kBAAkB,CAAC,6BAA6B,CAAC;QAEtD,IACE,CAAC,IAAI,CAACvC,IAAI,CAAC7B,KAAK,CAACqE,IAAI,CAAEZ,IAAS,IAAKA,IAAI,CAACjF,EAAE,KAAK6E,cAAc,CAAC,EAChE;UACA,IAAI,CAACxB,IAAI,CAAC7B,KAAK,CAAC4D,IAAI,CAAC;YACnBpF,EAAE,EAAE6E,cAAc;YAClBtE,OAAO,EAAEwE,YAAY,GACjBA,YAAY,CAACxE,OAAO,GACpB,IAAI,CAACgD;WACV,CAAC;QACJ;QAEA,IAAI,IAAI,CAACK,aAAa,EAAE;UACtB,IAAI,CAACA,aAAa,CAACkC,IAAI,EAAE;QAC3B;QACA,IAAI,CAAC3C,EAAE,CAAC4C,aAAa,EAAE;MACzB,CAAC;MACDC,KAAK,EAAGC,GAAG,IAAI;QACb,IAAI,CAACC,gBAAgB,CAACD,GAAG,CAAC;MAC5B;KACD,CAAC;EACN;EAEA3B,UAAUA,CAAA;IACR,IAAI,CAACd,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACN,eAAe,CAACiD,SAAS,EAAE,CAACZ,SAAS,CAAC;MACzCC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,IAAIA,QAAQ,CAACW,IAAI,EAAE;UAC7B,IAAI,CAAC1F,MAAM,GAAG+E,QAAQ,CAACW,IAAI;QAC7B,CAAC,MAAM;UACLV,OAAO,CAACW,IAAI,CAAC,4BAA4B,CAAC;UAC1C,IAAI,CAAC3F,MAAM,GAAG,EAAE;QAClB;MACF,CAAC;MACDsF,KAAK,EAAGC,GAAG,IAAI;QACbP,OAAO,CAACM,KAAK,CAAC,uBAAuB,EAAEC,GAAG,CAAC;MAC7C,CAAC;MACDK,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAAC9C,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACL,EAAE,CAAC4C,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEAQ,SAASA,CAACvC,MAAe;IACvB,IAAI,CAACd,eAAe,CAACsD,QAAQ,CAACxC,MAAM,CAAC,CAACuB,SAAS,CAAC;MAC9CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,IAAIA,QAAQ,CAACW,IAAI,EAAE;UAC7B,IAAI,CAAC5E,KAAK,GAAGiE,QAAQ,CAACW,IAAI;QAC5B,CAAC,MAAM;UACLV,OAAO,CAACW,IAAI,CAAC,2BAA2B,CAAC;UACzC,IAAI,CAAC7E,KAAK,GAAG,EAAE;QACjB;MACF,CAAC;MACDwE,KAAK,EAAGC,GAAG,IAAI;QACbP,OAAO,CAACM,KAAK,CAAC,sBAAsB,EAAEC,GAAG,CAAC;QAC1C,IAAI,CAACzE,KAAK,GAAG,EAAE;MACjB,CAAC;MACD8E,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACnD,EAAE,CAAC4C,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EACAhF,UAAUA,CAACiD,MAAc,EAAEyC,QAAgB;IACzC,IAAI,CAACnD,cAAc,GAAGU,MAAM;IAC5B,IAAI,CAACvD,gBAAgB,GAAGgG,QAAQ;IAEhC;IACA,IAAI,IAAI,CAAChD,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,CAACiD,UAAU,CAAC;QACnB1C,MAAM,EAAEA;OACT,CAAC;IACJ;IAEA;IACA,IAAI,CAACuC,SAAS,CAACvC,MAAM,CAAC;EACxB;EAEA1C,UAAUA,CAAC2C,MAAc,EAAE0C,QAAgB;IACzC,IAAI,CAACpD,gBAAgB,GAAGoD,QAAQ;IAEhC;IACA,IAAI,IAAI,CAAClD,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,CAACiD,UAAU,CAAC;QACnBzC,MAAM,EAAEA;OACT,CAAC;IACJ;EACF;EACAlE,cAAcA,CAACH,KAAa,EAAEqE,MAAc;IAC1C,IAAIrE,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACyD,IAAI,CAAC7B,KAAK,CAACb,MAAM,EAAE;MAChD,IAAIiG,cAAc,GAAGC,KAAK,CAACC,OAAO,CAAC,IAAI,CAACrD,IAAI,CAACqB,KAAK,CAACb,MAAM,CAAC,GACtD,IAAI,CAACR,IAAI,CAACqB,KAAK,CAACb,MAAM,GACtB,CAAC,IAAI,CAACR,IAAI,CAACqB,KAAK,CAACb,MAAM,CAAC;MAE5B,IAAI8C,cAAc,GAAGH,cAAc,CAACI,MAAM,CAAEhH,EAAU,IAAKA,EAAE,KAAKiE,MAAM,CAAC;MACzE,IAAI,CAACR,IAAI,CAACiD,UAAU,CAAC;QAAEzC,MAAM,EAAE8C;MAAc,CAAE,CAAC;MAChD,IAAI,CAAC1D,IAAI,CAAC7B,KAAK,CAACyF,MAAM,CAACrH,KAAK,EAAE,CAAC,CAAC;IAClC;EACF;EACA;EACA;EACA;EAEAsH,UAAU,GAAW,EAAE;EACvBC,WAAW,GAAY,KAAK;EAC5BC,uBAAuB,GAAU,EAAE;EACnCjD,aAAa,GAAU,EAAE;EAEzBkD,YAAY,GAAG,CACb;IACEvF,oBAAoB,EAAE,gCAAgC;IACtDS,eAAe,EAAE,CACf,8CAA8C,EAC9C,4DAA4D,EAC5D,kEAAkE,EAClE,6DAA6D,EAC7D,2DAA2D,EAC3D,uDAAuD,CACxD;IACDG,QAAQ,EAAE,KAAK;IACf4E,QAAQ,EAAE;GACX;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACExF,oBAAoB,EAAE,yBAAyB;IAC/CS,eAAe,EAAE,CACf,6CAA6C,EAC7C,wCAAwC,EACxC,iEAAiE,CAClE;IACDG,QAAQ,EAAE,KAAK;IACf4E,QAAQ,EAAE;GACX,EACD;IACExF,oBAAoB,EAAE,wBAAwB;IAC9CS,eAAe,EAAE,CACf,6CAA6C,EAC7C,wCAAwC,EACxC,iEAAiE,CAClE;IACDG,QAAQ,EAAE,KAAK;IACf4E,QAAQ,EAAE;GACX,EACD;IACExF,oBAAoB,EAAE,0BAA0B;IAChDS,eAAe,EAAE,CACf,kCAAkC,EAClC,kCAAkC,EAClC,sDAAsD,CACvD;IACDG,QAAQ,EAAE,KAAK;IACf4E,QAAQ,EAAE;GACX,EACD;IACExF,oBAAoB,EAAE,yBAAyB;IAC/CS,eAAe,EAAE,CACf,mCAAmC,EACnC,yCAAyC,EACzC,uDAAuD,EACvD,kDAAkD,EAClD,mCAAmC,CACpC;IACDG,QAAQ,EAAE,KAAK;IACf4E,QAAQ,EAAE;GACX,CACF;EAED;EACAC,uBAAuBA,CAAA;IACpB,IAAI,CAACL,UAAU,GAAG,EAAE;IACpB,MAAM1C,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,wBAAwB,CAAC;IACvE,IAAIF,YAAY,EAAE;MAChB,IAAI,CAACX,mBAAmB,GAAG,IAAI/E,KAAK,CAAC0F,YAAY,CAAC;MAClD,IAAI,CAACX,mBAAmB,CAACc,IAAI,EAAE;IACjC;EACF;EAEA5B,QAAQA,CAACyE,SAAiB;IACxB,OAAO,IAAI,CAACrD,aAAa,CAAC0B,IAAI,CAC3B4B,IAAI,IAAKA,IAAI,CAAC3F,oBAAoB,KAAK0F,SAAS,CAClD;EACH;EAEArF,iBAAiBA,CAACqF,SAAiB,EAAEE,cAAsB;IACzD,IAAI,CAAC,IAAI,CAACrE,IAAI,IAAI,CAAC,IAAI,CAACA,IAAI,CAACe,oBAAoB,EAAE,OAAO,KAAK;IAE/D,OAAO,IAAI,CAACf,IAAI,CAACe,oBAAoB,CAACyB,IAAI,CACvC4B,IAAS,IACRA,IAAI,CAAC3F,oBAAoB,KAAK0F,SAAS,IACvCC,IAAI,CAACC,cAAc,KAAKA,cAAc,CACzC;EACH;EAEA7F,aAAaA,CAAC2F,SAAiB,EAAEE,cAAsB,EAAEC,KAAY;IACnEjC,OAAO,CAACC,GAAG,CAAC+B,cAAc,CAAC;IAC3B,MAAME,OAAO,GAAID,KAAK,CAACE,MAA2B,CAACD,OAAO;IAC1D,IAAIA,OAAO,EAAE;MACX,MAAME,MAAM,GAAG,IAAI,CAAC3D,aAAa,CAAC0B,IAAI,CACnC4B,IAAI,IACHA,IAAI,CAAC3F,oBAAoB,KAAK0F,SAAS,IACvCC,IAAI,CAACC,cAAc,KAAKA,cAAc,CACzC;MAED,IAAI,CAACI,MAAM,EAAE;QACX,IAAI,CAAC3D,aAAa,CAACiB,IAAI,CAAC;UACtBtD,oBAAoB,EAAE0F,SAAS;UAC/BE,cAAc,EAAEA;SACjB,CAAC;MACJ;IACF,CAAC,MAAM;MACL,IAAI,CAACvD,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC6C,MAAM,CAC3CS,IAAI,IACH,EACEA,IAAI,CAAC3F,oBAAoB,KAAK0F,SAAS,IACvCC,IAAI,CAACC,cAAc,KAAKA,cAAc,CACvC,CACJ;IACH;IAEA,IAAI,CAACP,WAAW,GAAG,IAAI;EACzB;EAEAY,WAAWA,CAAA;IACT,IAAIC,OAAO,GAAQ;MAAE5D,oBAAoB,EAAE;IAAE,CAAE;IAE/CsB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACxB,aAAa,CAAC;IAElD,IAAI,CAACA,aAAa,CAAC8D,OAAO,CAAEC,KAAK,IAAI;MACnC,MAAMC,GAAG,GAAGD,KAAK,CAACpG,oBAAoB;MACtC,IAAI,CAACqG,GAAG,EAAE;MACV,IAAI,CAACH,OAAO,CAAC5D,oBAAoB,CAAC+D,GAAG,CAAC,EAAE;QACtCH,OAAO,CAAC5D,oBAAoB,CAAC+D,GAAG,CAAC,GAAG,EAAE;MACxC;MAEAH,OAAO,CAAC5D,oBAAoB,CAAC+D,GAAG,CAAC,CAAC/C,IAAI,CAAC8C,KAAK,CAACR,cAAc,CAAC;IAC9D,CAAC,CAAC;IAEFhC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEqC,OAAO,CAAC;IAEhC,IAAI,CAAC/E,cAAc,CAACoC,oBAAoB,CAAC,IAAI,CAAChC,IAAI,CAACrD,EAAE,EAAEgI,OAAO,CAAC,CAACzC,SAAS,CAAC;MACxEC,IAAI,EAAG4C,GAAG,IAAI;QACZ,IAAI,CAACjB,WAAW,GAAG,KAAK;QACxBzB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEqC,OAAO,CAAC;QAChCtC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEyC,GAAG,CAAC;QAE7B,IAAI,CAACxC,kBAAkB,CAAC,oBAAoB,CAAC;QAC7C,IAAI,CAAC/B,mBAAmB,EAAEiC,IAAI,EAAE;MAClC,CAAC;MACDE,KAAK,EAAGC,GAAG,IAAI;QACb,IAAI,CAACC,gBAAgB,CAACD,GAAG,CAAC;MAC5B;KACD,CAAC;EACJ;EAEQL,kBAAkBA,CAACyC,OAAe;IACxCtJ,IAAI,CAACuJ,IAAI,CAAC;MACRC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAEJ;KACP,CAAC;EACJ;EAEQnC,gBAAgBA,CAACF,KAAU;IACjCjH,IAAI,CAACuJ,IAAI,CAAC;MACRC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,OAAO;MACdC,IAAI,EAAEzC,KAAK,CAACA,KAAK,CAACqC;KACnB,CAAC;EACJ;EAEAhG,wBAAwB,GAA8B;IACtD,8CAA8C,EAAE,gCAAgC;IAChF,4DAA4D,EAAE,qCAAqC;IACnG,kEAAkE,EAAE,qCAAqC;IACzG,6DAA6D,EAAE,2CAA2C;IAC1G,2DAA2D,EAAE,wBAAwB;IACrF,uDAAuD,EAAE,qCAAqC;IAC9F,6CAA6C,EAAE,+BAA+B;IAC9E,wCAAwC,EAAE,0BAA0B;IACpE,iEAAiE,EAAE,oCAAoC;IACvG,kCAAkC,EAAE,oCAAoC;IACxE,kCAAkC,EAAE,oCAAoC;IACxE,sDAAsD,EAAE,yCAAyC;IACjG,mCAAmC,EAAE,qCAAqC;IAC1E,yCAAyC,EAAE,gCAAgC;IAC3E,uDAAuD,EAAE,0CAA0C;IACnG,kDAAkD,EAAE,gDAAgD;IACpG,mCAAmC,EAAE;GACtC;;qCAnXYW,8BAA8B,EAAAhE,EAAA,CAAA0J,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA5J,EAAA,CAAA0J,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA9J,EAAA,CAAA0J,iBAAA,CAAA1J,EAAA,CAAA+J,iBAAA,GAAA/J,EAAA,CAAA0J,iBAAA,CAAAM,EAAA,CAAAC,WAAA;EAAA;;UAA9BjG,8BAA8B;IAAAkG,SAAA;IAAAC,MAAA;MAAA9F,IAAA;IAAA;IAAA+F,UAAA;IAAAC,QAAA,GAAArK,EAAA,CAAAsK,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QChBrC5K,EAJN,CAAAC,cAAA,aAAgC,aAEyE,aACzE,YACA;QAAAD,EAAA,CAAAE,MAAA,4CAAqC;QAEnEF,EAFmE,CAAAoB,YAAA,EAAK,EAChE,EACF;QAMIpB,EALV,CAAAC,cAAA,aAAuD,cACpB,aACO,aACd,eACgC,YAC5C;QAAAD,EAAA,CAAAE,MAAA,wBAAgB;QACxBF,EADwB,CAAAoB,YAAA,EAAO,EACvB;QAENpB,EADF,CAAAC,cAAA,eAA6B,eACV;QACfD,EAAA,CAAAmB,SAAA,eAA4B;QAGxBnB,EAFJ,CAAAC,cAAA,eAAsB,eACkB,kBAC+C;QAApCD,EAAA,CAAAG,UAAA,mBAAA2K,iEAAA;UAAA9K,EAAA,CAAAO,aAAA,CAAAwK,GAAA;UAAA,OAAA/K,EAAA,CAAAiB,WAAA,CAAS4J,GAAA,CAAAtC,uBAAA,EAAyB;QAAA,EAAC;QAChFvI,EAAA,CAAAmB,SAAA,aAAuC;QAMnDnB,EALU,CAAAoB,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF;QA4BFpB,EAFJ,CAAAC,cAAA,cAAsB,gBACgC,YAC5C;QAAAD,EAAA,CAAAE,MAAA,iBAAS;QAAAF,EAAA,CAAAoB,YAAA,EAAO;QACtBpB,EAAA,CAAAC,cAAA,aACqG;QAGnGD,EAFA,CAAAmB,SAAA,gBAA2B,gBACA,gBACA;QAE/BnB,EADE,CAAAoB,YAAA,EAAI,EACE;QAIFpB,EAHN,CAAAC,cAAA,eAA6B,eACV,eACO,eACiC;QACnDD,EAAA,CAAAgC,UAAA,KAAAgJ,+CAAA,mBACkH;QAUtHhL,EADE,CAAAoB,YAAA,EAAM,EACF;QAGFpB,EAFJ,CAAAC,cAAA,eAAsB,eACkB,kBACyC;QAA9BD,EAAA,CAAAG,UAAA,mBAAA8K,iEAAA;UAAAjL,EAAA,CAAAO,aAAA,CAAAwK,GAAA;UAAA,OAAA/K,EAAA,CAAAiB,WAAA,CAAS4J,GAAA,CAAAtF,iBAAA,EAAmB;QAAA,EAAC;QAC1EvF,EAAA,CAAAmB,SAAA,aAAgC;QAUpDnB,EATkB,CAAAoB,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF,EACF,EACD,EACH,EACF;QASEpB,EAJR,CAAAC,cAAA,eAAuH,eACrE,eACnB,eACkB,cACiC;QACxED,EAAA,CAAAmB,SAAA,aAA0C;QAACnB,EAAA,CAAAE,MAAA,0BAC7C;QAAAF,EAAA,CAAAoB,YAAA,EAAK;QACLpB,EAAA,CAAAmB,SAAA,kBAA4F;QAC9FnB,EAAA,CAAAoB,YAAA,EAAM;QAMEpB,EALR,CAAAC,cAAA,eAA2C,eAClB,eACM,eAEP,iBAC0C;QAAAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAoB,YAAA,EAAQ;QAQtEpB,EALA,CAAAgC,UAAA,KAAAkJ,8CAAA,kBAAiF,KAAAC,8CAAA,kBAKU;QAUvFnL,EALJ,CAAAC,cAAA,eAAmC,kBAIF,YACvB;QAIJD,EAHA,CAAAgC,UAAA,KAAAoJ,uDAAA,2BAAsC,KAAAC,uDAAA,2BAGC;QAGzCrL,EAAA,CAAAoB,YAAA,EAAO;QACPpB,EAAA,CAAAmB,SAAA,aAAmC;QACrCnB,EAAA,CAAAoB,YAAA,EAAS;QAETpB,EAAA,CAAAC,cAAA,cAKI;QAoBFD,EAlBA,CAAAgC,UAAA,KAAAsJ,6CAAA,iBAAwE,KAAAC,6CAAA,iBAMK,KAAAC,uDAAA,2BAIZ,KAAAC,sDAAA,gCAAAzL,EAAA,CAAA0L,sBAAA,CAQ1C;QAS7B1L,EAFI,CAAAoB,YAAA,EAAK,EACD,EACF;QAIJpB,EADF,CAAAC,cAAA,eAAkB,iBAC0C;QAAAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAoB,YAAA,EAAQ;QAGtEpB,EAAA,CAAAgC,UAAA,KAAA2J,8CAAA,kBAAmE;QAU/D3L,EALJ,CAAAC,cAAA,eAAmC,kBAIF,YACvB;QAAAD,EAAA,CAAAE,MAAA,IAAuC;QAAAF,EAAA,CAAAoB,YAAA,EAAO;QACpDpB,EAAA,CAAAmB,SAAA,aAAmC;QACrCnB,EAAA,CAAAoB,YAAA,EAAS;QAETpB,EAAA,CAAAC,cAAA,cAKI;QAiBFD,EAfA,CAAAgC,UAAA,KAAA4J,6CAAA,iBAAuE,KAAAC,uDAAA,2BAMd,KAAAC,6CAAA,iBASuB;QAS5F9L,EALU,CAAAoB,YAAA,EAAK,EACD,EACF,EACF,EACF,EACF;QAEJpB,EADF,CAAAC,cAAA,eAA0B,kBACsD;QAC5ED,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAoB,YAAA,EAAS;QACTpB,EAAA,CAAAC,cAAA,kBAA4E;QAAzBD,EAAA,CAAAG,UAAA,mBAAA4L,iEAAA;UAAA/L,EAAA,CAAAO,aAAA,CAAAwK,GAAA;UAAA,OAAA/K,EAAA,CAAAiB,WAAA,CAAS4J,GAAA,CAAAjF,YAAA,EAAc;QAAA,EAAC;QACzE5F,EAAA,CAAAmB,SAAA,aAAqC;QAACnB,EAAA,CAAAE,MAAA,uBACxC;QAIRF,EAJQ,CAAAoB,YAAA,EAAS,EACL,EACF,EACF,EACF;QAUEpB,EALR,CAAAC,cAAA,eACqB,eACsC,eAC5B,eACkB,cACwB;QAC/DD,EAAA,CAAAE,MAAA,gCACF;QAAAF,EAAA,CAAAoB,YAAA,EAAK;QACLpB,EAAA,CAAAmB,SAAA,kBAA4F;QAC9FnB,EAAA,CAAAoB,YAAA,EAAM;QAGFpB,EAFJ,CAAAC,cAAA,eAAwB,eACJ,cACoB;QAClCD,EAAA,CAAAE,MAAA,8CACF;QAAAF,EAAA,CAAAoB,YAAA,EAAK;QAMCpB,EAJN,CAAAC,cAAA,eAA2C,eAEvB,eACS,gBACiB;QACtCD,EAAA,CAAAmB,SAAA,aAAkC;QACpCnB,EAAA,CAAAoB,YAAA,EAAO;QACPpB,EAAA,CAAAC,cAAA,iBAC+C;QAA7CD,EAAA,CAAAgM,gBAAA,2BAAAC,wEAAA5L,MAAA;UAAAL,EAAA,CAAAO,aAAA,CAAAwK,GAAA;UAAA/K,EAAA,CAAAkM,kBAAA,CAAArB,GAAA,CAAA3C,UAAA,EAAA7H,MAAA,MAAAwK,GAAA,CAAA3C,UAAA,GAAA7H,MAAA;UAAA,OAAAL,EAAA,CAAAiB,WAAA,CAAAZ,MAAA;QAAA,EAAwB;QAD1BL,EAAA,CAAAoB,YAAA,EAC+C;QAC/CpB,EAAA,CAAAgC,UAAA,MAAAmK,kDAAA,qBAAuE;QAI3EnM,EADE,CAAAoB,YAAA,EAAM,EACF;QAGNpB,EAAA,CAAAC,cAAA,gBAAiC;QAC/BD,EAAA,CAAAgC,UAAA,MAAAoK,+CAAA,kBAA0D;QAwE9DpM,EADE,CAAAoB,YAAA,EAAM,EACF;QAENpB,EAAA,CAAAC,cAAA,gBAAuC;QACrCD,EAAA,CAAAE,MAAA,qFAEF;QAEJF,EAFI,CAAAoB,YAAA,EAAM,EACF,EACF;QAEJpB,EADF,CAAAC,cAAA,gBAA0B,mBAC4C;QAClED,EAAA,CAAAE,MAAA,iBACF;QAAAF,EAAA,CAAAoB,YAAA,EAAS;QACTpB,EAAA,CAAAC,cAAA,mBAAgG;QAAxBD,EAAA,CAAAG,UAAA,mBAAAkM,kEAAA;UAAArM,EAAA,CAAAO,aAAA,CAAAwK,GAAA;UAAA,OAAA/K,EAAA,CAAAiB,WAAA,CAAS4J,GAAA,CAAA9B,WAAA,EAAa;QAAA,EAAC;QAC7F/I,EAAA,CAAAmB,SAAA,cAAqC;QAACnB,EAAA,CAAAE,MAAA,eACxC;QAIRF,EAJQ,CAAAoB,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;QA1SkCpB,EAAA,CAAAqB,SAAA,IAAe;QAAfrB,EAAA,CAAAkC,UAAA,YAAA2I,GAAA,CAAAxG,IAAA,CAAA7B,KAAA,CAAe;QAuCzBxC,EAAA,CAAAqB,SAAA,IAAkB;QAAlBrB,EAAA,CAAAkC,UAAA,cAAA2I,GAAA,CAAApG,IAAA,CAAkB;QAQ5BzE,EAAA,CAAAqB,SAAA,GAAqB;QAArBrB,EAAA,CAAAkC,UAAA,SAAA2I,GAAA,CAAArG,eAAA,CAAqB;QAKrBxE,EAAA,CAAAqB,SAAA,EAA6C;QAA7CrB,EAAA,CAAAkC,UAAA,UAAA2I,GAAA,CAAArG,eAAA,IAAAqG,GAAA,CAAAnJ,MAAA,CAAAC,MAAA,OAA6C;QAS/C3B,EAAA,CAAAqB,SAAA,GAA4B;QAA5BrB,EAAA,CAAAkC,UAAA,aAAA2I,GAAA,CAAArG,eAAA,CAA4B;QAEXxE,EAAA,CAAAqB,SAAA,GAAqB;QAArBrB,EAAA,CAAAkC,UAAA,SAAA2I,GAAA,CAAArG,eAAA,CAAqB;QAGrBxE,EAAA,CAAAqB,SAAA,EAAsB;QAAtBrB,EAAA,CAAAkC,UAAA,UAAA2I,GAAA,CAAArG,eAAA,CAAsB;QAclCxE,EAAA,CAAAqB,SAAA,GAAqB;QAArBrB,EAAA,CAAAkC,UAAA,SAAA2I,GAAA,CAAArG,eAAA,CAAqB;QAMrBxE,EAAA,CAAAqB,SAAA,EAAsB;QAAtBrB,EAAA,CAAAkC,UAAA,UAAA2I,GAAA,CAAArG,eAAA,CAAsB;QAIZxE,EAAA,CAAAqB,SAAA,EAAmC;QAAArB,EAAnC,CAAAkC,UAAA,SAAA2I,GAAA,CAAAnJ,MAAA,IAAAmJ,GAAA,CAAAnJ,MAAA,CAAAC,MAAA,KAAmC,aAAA2K,YAAA,CAAa;QAwB7DtM,EAAA,CAAAqB,SAAA,GAAqB;QAArBrB,EAAA,CAAAkC,UAAA,UAAA2I,GAAA,CAAAvG,cAAA,CAAqB;QASvBtE,EAAA,CAAAqB,SAAA,GAA4B;QAA5BrB,EAAA,CAAAkC,UAAA,cAAA2I,GAAA,CAAAvG,cAAA,CAA4B;QACtBtE,EAAA,CAAAqB,SAAA,GAAuC;QAAvCrB,EAAA,CAAAuM,iBAAA,CAAA1B,GAAA,CAAAtG,gBAAA,kBAAuC;QAWxCvE,EAAA,CAAAqB,SAAA,GAAqB;QAArBrB,EAAA,CAAAkC,UAAA,UAAA2I,GAAA,CAAAvG,cAAA,CAAqB;QAMXtE,EAAA,CAAAqB,SAAA,EAAwC;QAAxCrB,EAAA,CAAAkC,UAAA,SAAA2I,GAAA,CAAAvG,cAAA,IAAAuG,GAAA,CAAArI,KAAA,CAAAb,MAAA,KAAwC;QASlD3B,EAAA,CAAAqB,SAAA,EAA0C;QAA1CrB,EAAA,CAAAkC,UAAA,SAAA2I,GAAA,CAAAvG,cAAA,IAAAuG,GAAA,CAAArI,KAAA,CAAAb,MAAA,OAA0C;QAiD/C3B,EAAA,CAAAqB,SAAA,IAAwB;QAAxBrB,EAAA,CAAAwM,gBAAA,YAAA3B,GAAA,CAAA3C,UAAA,CAAwB;QACjBlI,EAAA,CAAAqB,SAAA,EAAgB;QAAhBrB,EAAA,CAAAkC,UAAA,SAAA2I,GAAA,CAAA3C,UAAA,CAAgB;QAQJlI,EAAA,CAAAqB,SAAA,GAAe;QAAfrB,EAAA,CAAAkC,UAAA,YAAA2I,GAAA,CAAAxC,YAAA,CAAe;QAoFErI,EAAA,CAAAqB,SAAA,GAAyB;QAAzBrB,EAAA,CAAAkC,UAAA,cAAA2I,GAAA,CAAA1C,WAAA,CAAyB;;;mBDtVnExI,YAAY,EAAA8M,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE/M,WAAW,EAAAoK,EAAA,CAAA4C,aAAA,EAAA5C,EAAA,CAAA6C,oBAAA,EAAA7C,EAAA,CAAA8C,eAAA,EAAA9C,EAAA,CAAA+C,oBAAA,EAAA/C,EAAA,CAAAgD,OAAA,EAAAhD,EAAA,CAAAiD,MAAA,EAAEpN,mBAAmB,EAAAmK,EAAA,CAAAkD,kBAAA;IAAAC,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}