{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/taskes/New folder/easydeal-frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BaseGridComponent } from 'src/app/pages/shared/base-grid/base-grid.component';\nimport { Modal } from 'bootstrap';\nimport { MenuComponent } from 'src/app/_metronic/kt/components';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/units.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"../../../../broker/shared/broker-title/broker-title.component\";\nimport * as i5 from \"../../../../broker/dataandproperties/components/empty-properties-card/empty-properties-card.component\";\nimport * as i6 from \"../../../developer-dashboard/components/header/header.component\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"../../../../../_metronic/shared/keenicon/keenicon.component\";\nimport * as i9 from \"../model-unit-filter/model-unit-filter.component\";\nconst _c0 = () => [\"/developer/projects/models/units/details\"];\nconst _c1 = a0 => ({\n  unitId: a0\n});\nfunction UnitsComponent_app_broker_title_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-broker-title\");\n  }\n}\nfunction UnitsComponent_app_developer_header_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-developer-header\", 30);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"title\", \"unit\")(\"subtitle\", \"To view existing unite\");\n  }\n}\nfunction UnitsComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"app-model-unit-filter\", 32);\n    i0.ɵɵlistener(\"filtersApplied\", function UnitsComponent_div_20_Template_app_model_unit_filter_filtersApplied_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFiltersApplied($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UnitsComponent_a_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 33);\n    i0.ɵɵelement(1, \"i\", 34);\n    i0.ɵɵtext(2, \" Download Excel \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UnitsComponent_app_empty_properties_card_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-empty-properties-card\", 35);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"userRole\", \"developer\");\n  }\n}\nfunction UnitsComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function UnitsComponent_div_23_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.uploadModel());\n    });\n    i0.ɵɵelement(2, \"i\", 38);\n    i0.ɵɵtext(3, \" Upload Unite Excel Sheet \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UnitsComponent_div_24_tr_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 51)(2, \"div\", 43)(3, \"input\", 52);\n    i0.ɵɵlistener(\"change\", function UnitsComponent_div_24_tr_46_Template_input_change_3_listener($event) {\n      const row_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleUnitSelection(row_r7.id, $event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(4, \"td\")(5, \"span\", 53);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\")(8, \"span\", 53);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"td\")(11, \"span\", 53);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"td\")(14, \"span\", 53);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"td\")(17, \"span\", 53);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"td\")(20, \"span\", 54);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"td\")(23, \"span\", 54);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"td\")(26, \"span\", 54);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"td\")(29, \"span\", 54);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"td\")(32, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function UnitsComponent_div_24_tr_46_Template_button_click_32_listener() {\n      const row_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.showUnitPlanModal(row_r7.diagram));\n    });\n    i0.ɵɵelement(33, \"i\", 56);\n    i0.ɵɵelementStart(34, \"span\", 57);\n    i0.ɵɵtext(35, \"View Diagram\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"span\", 58);\n    i0.ɵɵtext(37, \"Diagram\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(38, \"td\")(39, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function UnitsComponent_div_24_tr_46_Template_button_click_39_listener() {\n      const row_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.showUnitPlanModal(row_r7.locationInMasterPlan));\n    });\n    i0.ɵɵelement(40, \"i\", 56);\n    i0.ɵɵelementStart(41, \"span\", 57);\n    i0.ɵɵtext(42, \" View Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"span\", 58);\n    i0.ɵɵtext(44, \"Location In Master Plan\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(45, \"td\")(46, \"span\", 54);\n    i0.ɵɵtext(47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"td\")(49, \"button\", 59);\n    i0.ɵɵelement(50, \"i\", 60);\n    i0.ɵɵelementStart(51, \"span\");\n    i0.ɵɵtext(52, \"View Details\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const row_r7 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", ctx_r2.selectedUnitIds.includes(row_r7.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", row_r7.modelCode, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", row_r7.type, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", row_r7.buildingNumber, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", row_r7.unitNumber, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", row_r7.view, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", row_r7.floor, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", row_r7.deliveryDate, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", row_r7.finishingType, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", row_r7.status, \" \");\n    i0.ɵɵadvance(17);\n    i0.ɵɵtextInterpolate1(\" \", row_r7.otherAccessories, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(13, _c0))(\"queryParams\", i0.ɵɵpureFunction1(14, _c1, row_r7.id));\n  }\n}\nfunction UnitsComponent_div_24_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function UnitsComponent_div_24_div_47_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.makeSelectedAvailable());\n    });\n    i0.ɵɵtext(2, \" Make Selected Available \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UnitsComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"table\", 40)(2, \"thead\")(3, \"tr\", 41)(4, \"th\", 42)(5, \"div\", 43)(6, \"input\", 44);\n    i0.ɵɵlistener(\"change\", function UnitsComponent_div_24_Template_input_change_6_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleAll($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"th\", 45);\n    i0.ɵɵtext(8, \" Model Code \");\n    i0.ɵɵelement(9, \"i\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 45);\n    i0.ɵɵtext(11, \" Unit \");\n    i0.ɵɵelement(12, \"i\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\", 45);\n    i0.ɵɵtext(14, \" Building Number \");\n    i0.ɵɵelement(15, \"i\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 47);\n    i0.ɵɵtext(17, \" Apartment Number \");\n    i0.ɵɵelement(18, \"i\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"th\", 45);\n    i0.ɵɵtext(20, \" View \");\n    i0.ɵɵelement(21, \"i\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"th\", 48);\n    i0.ɵɵtext(23, \" Floor \");\n    i0.ɵɵelement(24, \"i\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"th\", 45);\n    i0.ɵɵtext(26, \" Delivery Date \");\n    i0.ɵɵelement(27, \"i\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"th\", 45);\n    i0.ɵɵtext(29, \" Finishing Type \");\n    i0.ɵɵelement(30, \"i\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"th\", 48);\n    i0.ɵɵtext(32, \" Status \");\n    i0.ɵɵelement(33, \"i\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"th\", 48);\n    i0.ɵɵtext(35, \" Diagram \");\n    i0.ɵɵelement(36, \"i\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"th\", 47);\n    i0.ɵɵtext(38, \" Location In Master Plan \");\n    i0.ɵɵelement(39, \"i\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"th\", 45);\n    i0.ɵɵtext(41, \" Other Accessories \");\n    i0.ɵɵelement(42, \"i\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"th\", 48);\n    i0.ɵɵtext(44, \"Actions\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(45, \"tbody\");\n    i0.ɵɵtemplate(46, UnitsComponent_div_24_tr_46_Template, 53, 16, \"tr\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(47, UnitsComponent_div_24_div_47_Template, 3, 0, \"div\", 50);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"checked\", ctx_r2.isAllSelected());\n    i0.ɵɵadvance(40);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.rows);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.user == null ? null : ctx_r2.user.role) === \"developer\");\n  }\n}\nfunction UnitsComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"img\", 63);\n    i0.ɵɵelementStart(2, \"div\", 64)(3, \"p\", 65);\n    i0.ɵɵtext(4, \"Image\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r2.selectedUnitPlanImage, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction UnitsComponent_ng_template_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66);\n    i0.ɵɵtext(1, \"No available\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class UnitsComponent extends BaseGridComponent {\n  cd;\n  unitService;\n  activatedRoute;\n  user;\n  showEmptyCard = false;\n  appliedFilters = {};\n  searchText = '';\n  searchTimeout;\n  isFilterDropdownVisible = false;\n  selectedUnitIds = [];\n  constructor(cd, unitService, activatedRoute) {\n    super(cd);\n    this.cd = cd;\n    this.unitService = unitService;\n    this.activatedRoute = activatedRoute;\n    this.setService(unitService);\n    this.orderBy = 'id';\n    this.orderDir = 'desc';\n  }\n  ngOnInit() {\n    super.ngOnInit();\n    // Get user from localStorage\n    const userJson = localStorage.getItem('currentUser');\n    this.user = userJson ? JSON.parse(userJson) : null;\n    this.activatedRoute.queryParams.subscribe(params => {\n      if (params['modelCode']) {\n        this.page.filters = {\n          modelCode: params['modelCode']\n        };\n      }\n    });\n  }\n  onSearchTextChange(value) {\n    clearTimeout(this.searchTimeout);\n    this.searchTimeout = setTimeout(() => {\n      this.page.filters = {\n        ...this.page.filters,\n        unitType: value.trim()\n      };\n      this.reloadTable(this.page);\n    }, 300);\n  }\n  toggleFilterDropdown() {\n    this.isFilterDropdownVisible = !this.isFilterDropdownVisible;\n  }\n  toggleAll(event) {\n    const checkbox = event.target;\n    const checked = checkbox.checked;\n    if (checked) {\n      // Select all row IDs\n      this.selectedUnitIds = this.rows.map(row => row.id);\n    } else {\n      this.selectedUnitIds = [];\n    }\n  }\n  // Check if all are selected\n  isAllSelected() {\n    return this.rows.length > 0 && this.selectedUnitIds.length === this.rows.length;\n  }\n  toggleUnitSelection(unitId, $event) {\n    const checkbox = $event.target;\n    const checked = checkbox.checked;\n    if (checked) {\n      this.selectedUnitIds.push(unitId);\n    } else {\n      this.selectedUnitIds = this.selectedUnitIds.filter(id => id !== unitId);\n    }\n  }\n  onFiltersApplied(filters) {\n    this.toggleFilterDropdown();\n    this.page.filters = {\n      ...this.page.filters,\n      ...filters\n    };\n    this.reloadTable(this.page);\n  }\n  reloadTable(pageInfo) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.page.pageNumber = pageInfo.pageNumber ?? pageInfo;\n      _this.loading = true;\n      yield _this._service.getAll(_this.page).subscribe(pagedData => {\n        console.log(pagedData.data);\n        _this.rows = Array.isArray(pagedData.data) ? pagedData.data : [];\n        _this.rows = [..._this.rows];\n        _this.page.totalElements = pagedData.count;\n        _this.page.count = Math.ceil(pagedData.count / _this.page.size);\n        // Update empty card visibility\n        _this.updateEmptyCardVisibility();\n        _this.cd.markForCheck();\n        _this.loading = false;\n        _this.afterGridLoaded();\n        MenuComponent.reinitialization();\n      }, error => {\n        console.log(error);\n        _this.cd.markForCheck();\n        _this.loading = false;\n        Swal.fire('Failed to load data. please try again later.', '', 'error');\n      });\n    })();\n  }\n  updateEmptyCardVisibility() {\n    // Show empty card when there are no units\n    this.showEmptyCard = this.rows.length === 0;\n  }\n  selectedUnitPlanImage;\n  showUnitPlanModal(diagramUrl) {\n    this.selectedUnitPlanImage = diagramUrl;\n    const modalElement = document.getElementById('viewUnitPlanModal');\n    if (modalElement) {\n      const modal = new Modal(modalElement);\n      modal.show();\n    }\n  }\n  uploadModel() {\n    console.log('Upload model clicked');\n  }\n  makeSelectedAvailable() {\n    var _this2 = this;\n    if (this.selectedUnitIds.length === 0) {\n      Swal.fire('Please select at least one unit.', '', 'warning');\n      return;\n    }\n    console.log(this.selectedUnitIds);\n    this.unitService.makeSelectedAvailable(this.selectedUnitIds).subscribe( /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (response) {\n        _this2.cd.detectChanges();\n        yield Swal.fire('Units updated successfully!', '', 'success');\n        _this2.selectedUnitIds = [];\n        _this2.reloadTable(_this2.page);\n      });\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }(), error => {\n      Swal.fire('Error making units available.', '', 'error');\n      console.error(error);\n    });\n  }\n  static ɵfac = function UnitsComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || UnitsComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.UnitsService), i0.ɵɵdirectiveInject(i2.ActivatedRoute));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: UnitsComponent,\n    selectors: [[\"app-units\"]],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 37,\n    vars: 10,\n    consts: [[\"noImage\", \"\"], [1, \"mb-5\", \"mt-0\"], [4, \"ngIf\"], [3, \"title\", \"subtitle\", 4, \"ngIf\"], [1, \"card\", \"mb-5\", \"mb-xl-10\"], [1, \"card-body\", \"pt-3\", \"pb-0\"], [1, \"d-flex\", \"flex-wrap\", \"flex-sm-nowrap\"], [1, \"flex-grow-1\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-start\", \"flex-wrap\"], [1, \"d-flex\", \"my-4\"], [1, \"text-dark-blue\", \"fs-2\", \"fw-bolder\", \"me-1\", \"mt-3\"], [\"data-kt-search-element\", \"form\", \"autocomplete\", \"off\", 1, \"w-300px\", \"position-relative\", \"mb-3\"], [\"name\", \"magnifier\", \"type\", \"outline\", 1, \"fs-2\", \"fs-lg-1\", \"text-gray-500\", \"position-absolute\", \"top-50\", \"translate-middle-y\", \"ms-3\"], [\"type\", \"text\", \"name\", \"searchText\", \"placeholder\", \"Search By Unit Type..\", \"data-kt-search-element\", \"input\", 1, \"form-control\", \"form-control-flush\", \"ps-10\", \"bg-light\", \"border\", \"rounded-pill\", 3, \"ngModelChange\", \"ngModel\"], [1, \"position-relative\", \"me-3\"], [1, \"btn\", \"btn-sm\", \"btn-light-dark-blue\", \"me-3\", \"cursor-pointer\", 3, \"click\"], [1, \"fa-solid\", \"fa-filter\"], [\"class\", \"dropdown-menu show p-3 shadow\", \"style\", \"position: absolute; top: 100%; left: 0; z-index: 1000;\", 4, \"ngIf\"], [\"class\", \"btn btn-sm btn-success cursor-pointer\", 4, \"ngIf\"], [3, \"userRole\", 4, \"ngIf\"], [\"class\", \"text-center mb-5\", 4, \"ngIf\"], [\"class\", \"table-responsive mb-5\", 4, \"ngIf\"], [\"id\", \"viewUnitPlanModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"unitPlanModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-lg\", \"modal-dialog-centered\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"unitPlanModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"btn-close\"], [1, \"modal-body\", \"text-center\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"title\", \"subtitle\"], [1, \"dropdown-menu\", \"show\", \"p-3\", \"shadow\", 2, \"position\", \"absolute\", \"top\", \"100%\", \"left\", \"0\", \"z-index\", \"1000\"], [3, \"filtersApplied\"], [1, \"btn\", \"btn-sm\", \"btn-success\", \"cursor-pointer\"], [1, \"bi\", \"bi-file-earmark-spreadsheet\", \"me-1\"], [3, \"userRole\"], [1, \"text-center\", \"mb-5\"], [1, \"btn\", \"btn-primary\", \"btn-lg\", 3, \"click\"], [1, \"bi\", \"bi-file-earmark-spreadsheet\", \"me-2\"], [1, \"table-responsive\", \"mb-5\"], [1, \"table\", \"table-row-bordered\", \"table-row-gray-100\", \"align-middle\", \"gs-0\", \"gy-3\", \"mt-5\"], [1, \"fw-bold\", \"bg-light-dark-blue\", \"text-dark-blue\", \"me-1\", \"ms-1\"], [1, \"w-25px\", \"ps-4\", \"rounded-start\"], [1, \"form-check\", \"form-check-sm\", \"form-check-custom\", \"form-check-solid\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"change\", \"checked\"], [1, \"min-w-150px\"], [1, \"fa-solid\", \"fa-arrow-down\", \"text-dark-blue\", \"ms-1\"], [1, \"min-w-200px\"], [1, \"min-w-100px\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"d-flex justify-content-end\", 4, \"ngIf\"], [1, \"ps-4\"], [\"type\", \"checkbox\", 1, \"form-check-input\", \"widget-13-check\", 3, \"change\", \"checked\"], [1, \"text-gray-800\", \"fw-semibold\", \"d-block\", \"mb-1\", \"fs-6\"], [1, \"fw-bold\", \"badge\", \"fs-6\", \"fw-semibold\"], [1, \"btn\", \"btn-sm\", \"btn-light-info\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"mx-auto\", 2, \"min-width\", \"120px\", \"white-space\", \"nowrap\", 3, \"click\"], [1, \"fa-solid\", \"fa-file-image\", \"me-1\"], [1, \"d-none\", \"d-md-inline\"], [1, \"d-inline\", \"d-md-none\"], [1, \"btn\", \"btn-sm\", \"btn-primary\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"mx-auto\", 2, \"min-width\", \"100px\", \"white-space\", \"nowrap\", 3, \"routerLink\", \"queryParams\"], [1, \"fa-solid\", \"fa-eye\", \"me-1\"], [1, \"d-flex\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-light-dark-blue\", \"me-3\", 3, \"click\"], [\"alt\", \"Unit Diagram\", 1, \"img-fluid\", \"rounded\", 2, \"max-height\", \"500px\", 3, \"src\"], [1, \"mt-3\"], [1, \"text-muted\"], [1, \"alert\", \"alert-warning\"]],\n    template: function UnitsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1);\n        i0.ɵɵtemplate(1, UnitsComponent_app_broker_title_1_Template, 1, 0, \"app-broker-title\", 2)(2, UnitsComponent_app_developer_header_2_Template, 1, 2, \"app-developer-header\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 4)(4, \"div\", 5)(5, \"div\", 6)(6, \"div\", 7)(7, \"div\", 8)(8, \"div\", 9)(9, \"h1\", 10);\n        i0.ɵɵtext(10, \"Units\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 9)(12, \"form\", 11);\n        i0.ɵɵelement(13, \"app-keenicon\", 12);\n        i0.ɵɵelementStart(14, \"input\", 13);\n        i0.ɵɵtwoWayListener(\"ngModelChange\", function UnitsComponent_Template_input_ngModelChange_14_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          i0.ɵɵtwoWayBindingSet(ctx.searchText, $event) || (ctx.searchText = $event);\n          return i0.ɵɵresetView($event);\n        });\n        i0.ɵɵlistener(\"ngModelChange\", function UnitsComponent_Template_input_ngModelChange_14_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSearchTextChange($event));\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(15, \"div\", 9)(16, \"div\", 14)(17, \"a\", 15);\n        i0.ɵɵlistener(\"click\", function UnitsComponent_Template_a_click_17_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.toggleFilterDropdown());\n        });\n        i0.ɵɵelement(18, \"i\", 16);\n        i0.ɵɵtext(19, \" Filter \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(20, UnitsComponent_div_20_Template, 2, 0, \"div\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(21, UnitsComponent_a_21_Template, 3, 0, \"a\", 18);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵtemplate(22, UnitsComponent_app_empty_properties_card_22_Template, 1, 1, \"app-empty-properties-card\", 19)(23, UnitsComponent_div_23_Template, 4, 0, \"div\", 20)(24, UnitsComponent_div_24_Template, 48, 3, \"div\", 21);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(25, \"router-outlet\");\n        i0.ɵɵelementStart(26, \"div\", 22)(27, \"div\", 23)(28, \"div\", 24)(29, \"div\", 25)(30, \"h5\", 26);\n        i0.ɵɵtext(31, \"View\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(32, \"button\", 27);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(33, \"div\", 28);\n        i0.ɵɵtemplate(34, UnitsComponent_div_34_Template, 5, 1, \"div\", 29)(35, UnitsComponent_ng_template_35_Template, 2, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        const noImage_r9 = i0.ɵɵreference(36);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (ctx.user == null ? null : ctx.user.role) === \"broker\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (ctx.user == null ? null : ctx.user.role) === \"developer\");\n        i0.ɵɵadvance(12);\n        i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchText);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", ctx.isFilterDropdownVisible);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (ctx.user == null ? null : ctx.user.role) === \"developer\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showEmptyCard);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showEmptyCard);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.showEmptyCard && ctx.rows.length > 0);\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedUnitPlanImage)(\"ngIfElse\", noImage_r9);\n      }\n    },\n    dependencies: [i3.NgForOf, i3.NgIf, i4.BrokerTitleComponent, i5.EmptyPropertiesCardComponent, i6.HeaderComponent, i7.ɵNgNoValidate, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgControlStatusGroup, i7.NgModel, i7.NgForm, i2.RouterOutlet, i2.RouterLink, i8.KeeniconComponent, i9.ModelUnitFilterComponent],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["BaseGridComponent", "Modal", "MenuComponent", "<PERSON><PERSON>", "i0", "ɵɵelement", "ɵɵproperty", "ɵɵelementStart", "ɵɵlistener", "UnitsComponent_div_20_Template_app_model_unit_filter_filtersApplied_1_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onFiltersApplied", "ɵɵelementEnd", "ɵɵtext", "UnitsComponent_div_23_Template_button_click_1_listener", "_r4", "uploadModel", "UnitsComponent_div_24_tr_46_Template_input_change_3_listener", "row_r7", "_r6", "$implicit", "toggleUnitSelection", "id", "UnitsComponent_div_24_tr_46_Template_button_click_32_listener", "showUnitPlanModal", "diagram", "UnitsComponent_div_24_tr_46_Template_button_click_39_listener", "locationInMasterPlan", "ɵɵadvance", "selectedUnitIds", "includes", "ɵɵtextInterpolate1", "modelCode", "type", "buildingNumber", "unitNumber", "view", "floor", "deliveryDate", "finishingType", "status", "otherAccessories", "ɵɵpureFunction0", "_c0", "ɵɵpureFunction1", "_c1", "UnitsComponent_div_24_div_47_Template_button_click_1_listener", "_r8", "makeSelectedAvailable", "UnitsComponent_div_24_Template_input_change_6_listener", "_r5", "toggleAll", "ɵɵtemplate", "UnitsComponent_div_24_tr_46_Template", "UnitsComponent_div_24_div_47_Template", "isAllSelected", "rows", "user", "role", "selectedUnitPlanImage", "ɵɵsanitizeUrl", "UnitsComponent", "cd", "unitService", "activatedRoute", "showEmptyCard", "appliedFilters", "searchText", "searchTimeout", "isFilterDropdownVisible", "constructor", "setService", "orderBy", "orderDir", "ngOnInit", "userJson", "localStorage", "getItem", "JSON", "parse", "queryParams", "subscribe", "params", "page", "filters", "onSearchTextChange", "value", "clearTimeout", "setTimeout", "unitType", "trim", "reloadTable", "toggleFilterDropdown", "event", "checkbox", "target", "checked", "map", "row", "length", "unitId", "push", "filter", "pageInfo", "_this", "_asyncToGenerator", "pageNumber", "loading", "_service", "getAll", "pagedData", "console", "log", "data", "Array", "isArray", "totalElements", "count", "Math", "ceil", "size", "updateEmptyCardVisibility", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "afterGridLoaded", "reinitialization", "error", "fire", "diagramUrl", "modalElement", "document", "getElementById", "modal", "show", "_this2", "_ref", "response", "detectChanges", "_x", "apply", "arguments", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "UnitsService", "i2", "ActivatedRoute", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "UnitsComponent_Template", "rf", "ctx", "UnitsComponent_app_broker_title_1_Template", "UnitsComponent_app_developer_header_2_Template", "ɵɵtwoWayListener", "UnitsComponent_Template_input_ngModelChange_14_listener", "_r1", "ɵɵtwoWayBindingSet", "UnitsComponent_Template_a_click_17_listener", "UnitsComponent_div_20_Template", "UnitsComponent_a_21_Template", "UnitsComponent_app_empty_properties_card_22_Template", "UnitsComponent_div_23_Template", "UnitsComponent_div_24_Template", "UnitsComponent_div_34_Template", "UnitsComponent_ng_template_35_Template", "ɵɵtemplateRefExtractor", "ɵɵtwoWayProperty", "noImage_r9"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\developer\\projects\\components\\units\\units.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\developer\\projects\\components\\units\\units.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component } from '@angular/core';\r\nimport { BaseGridComponent } from 'src/app/pages/shared/base-grid/base-grid.component';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { Modal } from 'bootstrap';\r\nimport { UnitsService } from '../../../services/units.service';\r\nimport { MenuComponent } from 'src/app/_metronic/kt/components';\r\nimport Swal from 'sweetalert2';\r\nimport { event } from 'jquery';\r\n\r\n@Component({\r\n  selector: 'app-units',\r\n  templateUrl: './units.component.html',\r\n  styleUrl: './units.component.scss',\r\n})\r\nexport class UnitsComponent extends BaseGridComponent {\r\n  user: any;\r\n  showEmptyCard = false;\r\n  appliedFilters: any = {};\r\n  searchText: string = '';\r\n  private searchTimeout: any;\r\n  isFilterDropdownVisible = false;\r\n  selectedUnitIds: number[] = [];\r\n\r\n  constructor(\r\n    protected cd: ChangeDetectorRef,\r\n    protected unitService: UnitsService,\r\n    private activatedRoute: ActivatedRoute\r\n  ) {\r\n    super(cd);\r\n    this.setService(unitService);\r\n    this.orderBy = 'id';\r\n    this.orderDir = 'desc';\r\n  }\r\n\r\n  ngOnInit() {\r\n    super.ngOnInit();\r\n\r\n    // Get user from localStorage\r\n    const userJson = localStorage.getItem('currentUser');\r\n    this.user = userJson ? JSON.parse(userJson) : null;\r\n\r\n    this.activatedRoute.queryParams.subscribe((params: any) => {\r\n      if (params['modelCode']) {\r\n        this.page.filters = { modelCode: params['modelCode'] };\r\n      }\r\n    });\r\n  }\r\n\r\n  onSearchTextChange(value: string): void {\r\n    clearTimeout(this.searchTimeout);\r\n\r\n    this.searchTimeout = setTimeout(() => {\r\n      this.page.filters = {...this.page.filters, unitType: value.trim()};\r\n      this.reloadTable(this.page);\r\n    }, 300);\r\n  }\r\n\r\n  toggleFilterDropdown() {\r\n    this.isFilterDropdownVisible = !this.isFilterDropdownVisible;\r\n  }\r\n\r\n  toggleAll(event: Event): void {\r\n  const checkbox = event.target as HTMLInputElement;\r\n  const checked = checkbox.checked;\r\n\r\n  if (checked) {\r\n    // Select all row IDs\r\n    this.selectedUnitIds = this.rows.map((row: any) => row.id);\r\n  } else {\r\n    this.selectedUnitIds = [];\r\n  }\r\n}\r\n\r\n// Check if all are selected\r\n  isAllSelected(): boolean {\r\n    return this.rows.length > 0 && this.selectedUnitIds.length === this.rows.length;\r\n  }\r\n\r\n  toggleUnitSelection(unitId: number, $event: any) {\r\n    const checkbox = $event.target as HTMLInputElement;\r\n    const checked = checkbox.checked;\r\n    if (checked) {\r\n      this.selectedUnitIds.push(unitId);\r\n    } else {\r\n      this.selectedUnitIds = this.selectedUnitIds.filter(id => id !== unitId);\r\n    }\r\n  }\r\n\r\n  onFiltersApplied(filters: any) {\r\n    this.toggleFilterDropdown();\r\n    this.page.filters = {...this.page.filters, ...filters};\r\n\r\n    this.reloadTable(this.page);\r\n  }\r\n\r\n  async reloadTable(pageInfo: any): Promise<void> {\r\n    this.page.pageNumber = pageInfo.pageNumber ?? pageInfo;\r\n\r\n    this.loading = true;\r\n    await this._service.getAll(this.page).subscribe(\r\n      (pagedData: any) => {\r\n        console.log(pagedData.data);\r\n        this.rows = Array.isArray(pagedData.data)? pagedData.data : [];\r\n        this.rows = [...this.rows];\r\n\r\n        this.page.totalElements = pagedData.count;\r\n        this.page.count = Math.ceil(pagedData.count / this.page.size);\r\n\r\n        // Update empty card visibility\r\n        this.updateEmptyCardVisibility();\r\n\r\n        this.cd.markForCheck();\r\n        this.loading = false;\r\n\r\n        this.afterGridLoaded();\r\n        MenuComponent.reinitialization();\r\n      },\r\n      (error: any) => {\r\n        console.log(error);\r\n        this.cd.markForCheck();\r\n        this.loading = false;\r\n        Swal.fire('Failed to load data. please try again later.', '', 'error');\r\n      }\r\n    )\r\n  }\r\n\r\n  updateEmptyCardVisibility() {\r\n    // Show empty card when there are no units\r\n    this.showEmptyCard = this.rows.length === 0;\r\n  }\r\n\r\n  selectedUnitPlanImage: string;\r\n  showUnitPlanModal(diagramUrl: string) {\r\n    this.selectedUnitPlanImage = diagramUrl;\r\n    const modalElement = document.getElementById('viewUnitPlanModal');\r\n    if (modalElement) {\r\n      const modal = new Modal(modalElement);\r\n      modal.show();\r\n    }\r\n  }\r\n\r\n  uploadModel() {\r\n    console.log('Upload model clicked');\r\n  }\r\n\r\n  makeSelectedAvailable()\r\n  {\r\n    if (this.selectedUnitIds.length === 0) {\r\n      Swal.fire('Please select at least one unit.', '', 'warning');\r\n      return;\r\n    }\r\n    console.log(this.selectedUnitIds);\r\n    this.unitService.makeSelectedAvailable(this.selectedUnitIds).subscribe(\r\n      async (response:any)  => {\r\n        this.cd.detectChanges();\r\n        await Swal.fire('Units updated successfully!', '', 'success');\r\n        this.selectedUnitIds = [];\r\n        this.reloadTable(this.page);\r\n      },\r\n      (error: any ) => {\r\n        Swal.fire('Error making units available.', '', 'error');\r\n        console.error(error);\r\n      }\r\n    );\r\n  }\r\n\r\n}\r\n", "<div class=\"mb-5 mt-0\">\r\n  <app-broker-title *ngIf=\"user?.role === 'broker'\"></app-broker-title>\r\n  <app-developer-header *ngIf=\"user?.role === 'developer'\" [title]=\"'unit'\" [subtitle]=\"'To view existing unite'\">\r\n  </app-developer-header>\r\n</div>\r\n<div class=\"card mb-5 mb-xl-10\">\r\n  <div class=\"card-body pt-3 pb-0\">\r\n    <div class=\"d-flex flex-wrap flex-sm-nowrap\">\r\n      <div class=\"flex-grow-1\">\r\n        <div class=\"d-flex justify-content-between align-items-start flex-wrap\">\r\n          <div class=\"d-flex my-4\">\r\n            <h1 class=\"text-dark-blue fs-2 fw-bolder me-1 mt-3\">Units</h1>\r\n          </div>\r\n          <div class=\"d-flex my-4\">\r\n            <form data-kt-search-element=\"form\" class=\"w-300px position-relative mb-3\" autocomplete=\"off\">\r\n              <app-keenicon name=\"magnifier\"\r\n                class=\"fs-2 fs-lg-1 text-gray-500 position-absolute top-50 translate-middle-y ms-3\" type=\"outline\">\r\n              </app-keenicon>\r\n              <input type=\"text\" name=\"searchText\"\r\n                class=\"form-control form-control-flush ps-10 bg-light border rounded-pill\" [(ngModel)]=\"searchText\"\r\n                (ngModelChange)=\"onSearchTextChange($event)\" placeholder=\"Search By Unit Type..\"\r\n                data-kt-search-element=\"input\" />\r\n            </form>\r\n          </div>\r\n          <div class=\"d-flex my-4\">\r\n            <div class=\"position-relative me-3\">\r\n              <a class=\"btn btn-sm btn-light-dark-blue me-3 cursor-pointer\" (click)=\"toggleFilterDropdown()\">\r\n                <i class=\"fa-solid fa-filter\"></i> Filter\r\n              </a>\r\n\r\n              <!-- Filter Dropdown -->\r\n              <div *ngIf=\"isFilterDropdownVisible\" class=\"dropdown-menu show p-3 shadow\"\r\n                style=\"position: absolute; top: 100%; left: 0; z-index: 1000;\">\r\n                <app-model-unit-filter (filtersApplied)=\"onFiltersApplied($event)\"></app-model-unit-filter>\r\n              </div>\r\n            </div>\r\n            <a class=\"btn btn-sm btn-success cursor-pointer\" *ngIf=\"user?.role === 'developer'\">\r\n              <i class=\"bi bi-file-earmark-spreadsheet me-1\"></i>\r\n              Download Excel\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <app-empty-properties-card *ngIf=\"showEmptyCard\" [userRole]=\"'developer'\"></app-empty-properties-card>\r\n\r\n    <div *ngIf=\"showEmptyCard\" class=\"text-center mb-5\">\r\n      <button class=\"btn btn-primary btn-lg\" (click)=\"uploadModel()\">\r\n        <i class=\"bi bi-file-earmark-spreadsheet me-2\"></i>\r\n        Upload Unite Excel Sheet\r\n      </button>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mb-5\" *ngIf=\"!showEmptyCard && rows.length > 0\">\r\n      <table class=\"table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5\">\r\n        <thead>\r\n          <tr class=\"fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1\">\r\n            <th class=\"w-25px ps-4 rounded-start\">\r\n              <div class=\"form-check form-check-sm form-check-custom form-check-solid\">\r\n                <input class=\"form-check-input\" type=\"checkbox\" [checked]=\"isAllSelected()\"\r\n                  (change)=\"toggleAll($event)\" />\r\n              </div>\r\n            </th>\r\n            <th class=\"min-w-150px\">\r\n              Model Code\r\n              <i class=\"fa-solid fa-arrow-down text-dark-blue ms-1\"></i>\r\n            </th>\r\n            <th class=\"min-w-150px\">\r\n              Unit\r\n              <i class=\"fa-solid fa-arrow-down text-dark-blue ms-1\"></i>\r\n            </th>\r\n            <th class=\"min-w-150px\">\r\n              Building Number\r\n              <i class=\"fa-solid fa-arrow-down text-dark-blue ms-1\"></i>\r\n            </th>\r\n            <th class=\"min-w-200px\">\r\n              Apartment Number\r\n              <i class=\"fa-solid fa-arrow-down text-dark-blue ms-1\"></i>\r\n            </th>\r\n            <th class=\"min-w-150px\">\r\n              View\r\n              <i class=\"fa-solid fa-arrow-down text-dark-blue ms-1\"></i>\r\n            </th>\r\n            <th class=\"min-w-100px\">\r\n              Floor\r\n              <i class=\"fa-solid fa-arrow-down text-dark-blue ms-1\"></i>\r\n            </th>\r\n            <th class=\"min-w-150px\">\r\n              Delivery Date\r\n              <i class=\"fa-solid fa-arrow-down text-dark-blue ms-1\"></i>\r\n            </th>\r\n            <th class=\"min-w-150px\">\r\n              Finishing Type\r\n              <i class=\"fa-solid fa-arrow-down text-dark-blue ms-1\"></i>\r\n            </th>\r\n            <th class=\"min-w-100px\">\r\n              Status\r\n              <i class=\"fa-solid fa-arrow-down text-dark-blue ms-1\"></i>\r\n            </th>\r\n            <th class=\"min-w-100px\">\r\n              Diagram\r\n              <i class=\"fa-solid fa-arrow-down text-dark-blue ms-1\"></i>\r\n            </th>\r\n            <th class=\"min-w-200px\">\r\n              Location In Master Plan\r\n              <i class=\"fa-solid fa-arrow-down text-dark-blue ms-1\"></i>\r\n            </th>\r\n            <th class=\"min-w-150px\">\r\n              Other Accessories\r\n              <i class=\"fa-solid fa-arrow-down text-dark-blue ms-1\"></i>\r\n            </th>\r\n            <th class=\"min-w-100px\">Actions</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let row of rows\">\r\n            <td class=\"ps-4\">\r\n              <div class=\"form-check form-check-sm form-check-custom form-check-solid\">\r\n                <input class=\"form-check-input widget-13-check\" type=\"checkbox\"\r\n                  [checked]=\"selectedUnitIds.includes(row.id)\" (change)=\"toggleUnitSelection(row.id, $event)\" />\r\n              </div>\r\n            </td>\r\n            <td>\r\n              <span class=\"text-gray-800 fw-semibold d-block mb-1 fs-6\">\r\n                {{ row.modelCode }}\r\n              </span>\r\n            </td>\r\n            <td>\r\n              <span class=\"text-gray-800 fw-semibold d-block mb-1 fs-6\">\r\n                {{ row.type }}\r\n              </span>\r\n            </td>\r\n            <td>\r\n              <span class=\"text-gray-800 fw-semibold d-block mb-1 fs-6\">\r\n                {{ row.buildingNumber }}\r\n              </span>\r\n            </td>\r\n            <td>\r\n              <span class=\"text-gray-800 fw-semibold d-block mb-1 fs-6\">\r\n                {{ row.unitNumber }}\r\n              </span>\r\n            </td>\r\n            <td>\r\n              <span class=\"text-gray-800 fw-semibold d-block mb-1 fs-6\">\r\n                {{ row.view }}\r\n              </span>\r\n            </td>\r\n            <td>\r\n              <span class=\"fw-bold badge fs-6 fw-semibold\">\r\n                {{ row.floor }}\r\n              </span>\r\n            </td>\r\n            <td>\r\n              <span class=\"fw-bold badge fs-6 fw-semibold\">\r\n                {{ row.deliveryDate }}\r\n              </span>\r\n            </td>\r\n            <td>\r\n              <span class=\"fw-bold badge fs-6 fw-semibold\">\r\n                {{ row.finishingType }}\r\n              </span>\r\n            </td>\r\n            <td>\r\n              <span class=\"fw-bold badge fs-6 fw-semibold\">\r\n                {{ row.status }}\r\n              </span>\r\n            </td>\r\n            <td>\r\n              <button class=\"btn btn-sm btn-light-info d-flex align-items-center justify-content-center mx-auto\"\r\n                (click)=\"showUnitPlanModal(row.diagram)\" style=\"min-width: 120px; white-space: nowrap\">\r\n                <i class=\"fa-solid fa-file-image me-1\"></i>\r\n                <span class=\"d-none d-md-inline\">View Diagram</span>\r\n                <span class=\"d-inline d-md-none\">Diagram</span>\r\n              </button>\r\n            </td>\r\n            <td>\r\n              <button class=\"btn btn-sm btn-light-info d-flex align-items-center justify-content-center mx-auto\"\r\n                (click)=\"showUnitPlanModal(row.locationInMasterPlan)\" style=\"min-width: 120px; white-space: nowrap\">\r\n                <i class=\"fa-solid fa-file-image me-1\"></i>\r\n                <span class=\"d-none d-md-inline\"> View Location</span>\r\n                <span class=\"d-inline d-md-none\">Location In Master Plan</span>\r\n              </button>\r\n            </td>\r\n            <td>\r\n              <span class=\"fw-bold badge fs-6 fw-semibold\">\r\n                {{ row.otherAccessories }}\r\n              </span>\r\n            </td>\r\n            <td>\r\n              <button class=\"btn btn-sm btn-primary d-flex align-items-center justify-content-center mx-auto\"\r\n                [routerLink]=\"['/developer/projects/models/units/details']\" [queryParams]=\"{ unitId: row.id }\"\r\n                style=\"min-width: 100px; white-space: nowrap\">\r\n                <i class=\"fa-solid fa-eye me-1\"></i>\r\n                <span>View Details</span>\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n      <div class=\"d-flex justify-content-end\" *ngIf=\"user?.role === 'developer'\">\r\n        <button type=\"button\" class=\"btn btn-sm btn-light-dark-blue me-3\" (click)=\"makeSelectedAvailable()\">\r\n          Make Selected Available\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<router-outlet></router-outlet>\r\n\r\n<!-- View Unit Plan Modal -->\r\n<div class=\"modal fade\" id=\"viewUnitPlanModal\" tabindex=\"-1\" aria-labelledby=\"unitPlanModalLabel\" aria-hidden=\"true\">\r\n  <div class=\"modal-dialog modal-lg modal-dialog-centered\">\r\n    <div class=\"modal-content\">\r\n      <div class=\"modal-header\">\r\n        <h5 class=\"modal-title\" id=\"unitPlanModalLabel\">View</h5>\r\n        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\r\n      </div>\r\n      <div class=\"modal-body text-center\">\r\n        <div *ngIf=\"selectedUnitPlanImage; else noImage\">\r\n          <img [src]=\"selectedUnitPlanImage\" alt=\"Unit Diagram\" class=\"img-fluid rounded\" style=\"max-height: 500px\" />\r\n          <div class=\"mt-3\">\r\n            <p class=\"text-muted\">Image</p>\r\n          </div>\r\n        </div>\r\n        <ng-template #noImage>\r\n          <div class=\"alert alert-warning\">No available</div>\r\n        </ng-template>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";AACA,SAASA,iBAAiB,QAAQ,oDAAoD;AAEtF,SAASC,KAAK,QAAQ,WAAW;AAEjC,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;;;;;;;;ICL5BC,EAAA,CAAAC,SAAA,uBAAqE;;;;;IACrED,EAAA,CAAAC,SAAA,+BACuB;;;IADmDD,EAAjB,CAAAE,UAAA,iBAAgB,sCAAsC;;;;;;IA+BjGF,EAFF,CAAAG,cAAA,cACiE,gCACI;IAA5CH,EAAA,CAAAI,UAAA,4BAAAC,+EAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAkBF,MAAA,CAAAG,gBAAA,CAAAN,MAAA,CAAwB;IAAA,EAAC;IACpEN,EADqE,CAAAa,YAAA,EAAwB,EACvF;;;;;IAERb,EAAA,CAAAG,cAAA,YAAoF;IAClFH,EAAA,CAAAC,SAAA,YAAmD;IACnDD,EAAA,CAAAc,MAAA,uBACF;IAAAd,EAAA,CAAAa,YAAA,EAAI;;;;;IAMZb,EAAA,CAAAC,SAAA,oCAAsG;;;IAArDD,EAAA,CAAAE,UAAA,yBAAwB;;;;;;IAGvEF,EADF,CAAAG,cAAA,cAAoD,iBACa;IAAxBH,EAAA,CAAAI,UAAA,mBAAAW,uDAAA;MAAAf,EAAA,CAAAO,aAAA,CAAAS,GAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAQ,WAAA,EAAa;IAAA,EAAC;IAC5DjB,EAAA,CAAAC,SAAA,YAAmD;IACnDD,EAAA,CAAAc,MAAA,iCACF;IACFd,EADE,CAAAa,YAAA,EAAS,EACL;;;;;;IAmEMb,EAHN,CAAAG,cAAA,SAA6B,aACV,cAC0D,gBAEyB;IAAjDH,EAAA,CAAAI,UAAA,oBAAAc,6DAAAZ,MAAA;MAAA,MAAAa,MAAA,GAAAnB,EAAA,CAAAO,aAAA,CAAAa,GAAA,EAAAC,SAAA;MAAA,MAAAZ,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAUF,MAAA,CAAAa,mBAAA,CAAAH,MAAA,CAAAI,EAAA,EAAAjB,MAAA,CAAmC;IAAA,EAAC;IAEjGN,EAHI,CAAAa,YAAA,EACgG,EAC5F,EACH;IAEHb,EADF,CAAAG,cAAA,SAAI,eACwD;IACxDH,EAAA,CAAAc,MAAA,GACF;IACFd,EADE,CAAAa,YAAA,EAAO,EACJ;IAEHb,EADF,CAAAG,cAAA,SAAI,eACwD;IACxDH,EAAA,CAAAc,MAAA,GACF;IACFd,EADE,CAAAa,YAAA,EAAO,EACJ;IAEHb,EADF,CAAAG,cAAA,UAAI,gBACwD;IACxDH,EAAA,CAAAc,MAAA,IACF;IACFd,EADE,CAAAa,YAAA,EAAO,EACJ;IAEHb,EADF,CAAAG,cAAA,UAAI,gBACwD;IACxDH,EAAA,CAAAc,MAAA,IACF;IACFd,EADE,CAAAa,YAAA,EAAO,EACJ;IAEHb,EADF,CAAAG,cAAA,UAAI,gBACwD;IACxDH,EAAA,CAAAc,MAAA,IACF;IACFd,EADE,CAAAa,YAAA,EAAO,EACJ;IAEHb,EADF,CAAAG,cAAA,UAAI,gBAC2C;IAC3CH,EAAA,CAAAc,MAAA,IACF;IACFd,EADE,CAAAa,YAAA,EAAO,EACJ;IAEHb,EADF,CAAAG,cAAA,UAAI,gBAC2C;IAC3CH,EAAA,CAAAc,MAAA,IACF;IACFd,EADE,CAAAa,YAAA,EAAO,EACJ;IAEHb,EADF,CAAAG,cAAA,UAAI,gBAC2C;IAC3CH,EAAA,CAAAc,MAAA,IACF;IACFd,EADE,CAAAa,YAAA,EAAO,EACJ;IAEHb,EADF,CAAAG,cAAA,UAAI,gBAC2C;IAC3CH,EAAA,CAAAc,MAAA,IACF;IACFd,EADE,CAAAa,YAAA,EAAO,EACJ;IAEHb,EADF,CAAAG,cAAA,UAAI,kBAEuF;IAAvFH,EAAA,CAAAI,UAAA,mBAAAoB,8DAAA;MAAA,MAAAL,MAAA,GAAAnB,EAAA,CAAAO,aAAA,CAAAa,GAAA,EAAAC,SAAA;MAAA,MAAAZ,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgB,iBAAA,CAAAN,MAAA,CAAAO,OAAA,CAA8B;IAAA,EAAC;IACxC1B,EAAA,CAAAC,SAAA,aAA2C;IAC3CD,EAAA,CAAAG,cAAA,gBAAiC;IAAAH,EAAA,CAAAc,MAAA,oBAAY;IAAAd,EAAA,CAAAa,YAAA,EAAO;IACpDb,EAAA,CAAAG,cAAA,gBAAiC;IAAAH,EAAA,CAAAc,MAAA,eAAO;IAE5Cd,EAF4C,CAAAa,YAAA,EAAO,EACxC,EACN;IAEHb,EADF,CAAAG,cAAA,UAAI,kBAEoG;IAApGH,EAAA,CAAAI,UAAA,mBAAAuB,8DAAA;MAAA,MAAAR,MAAA,GAAAnB,EAAA,CAAAO,aAAA,CAAAa,GAAA,EAAAC,SAAA;MAAA,MAAAZ,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgB,iBAAA,CAAAN,MAAA,CAAAS,oBAAA,CAA2C;IAAA,EAAC;IACrD5B,EAAA,CAAAC,SAAA,aAA2C;IAC3CD,EAAA,CAAAG,cAAA,gBAAiC;IAACH,EAAA,CAAAc,MAAA,sBAAa;IAAAd,EAAA,CAAAa,YAAA,EAAO;IACtDb,EAAA,CAAAG,cAAA,gBAAiC;IAAAH,EAAA,CAAAc,MAAA,+BAAuB;IAE5Dd,EAF4D,CAAAa,YAAA,EAAO,EACxD,EACN;IAEHb,EADF,CAAAG,cAAA,UAAI,gBAC2C;IAC3CH,EAAA,CAAAc,MAAA,IACF;IACFd,EADE,CAAAa,YAAA,EAAO,EACJ;IAEHb,EADF,CAAAG,cAAA,UAAI,kBAG8C;IAC9CH,EAAA,CAAAC,SAAA,aAAoC;IACpCD,EAAA,CAAAG,cAAA,YAAM;IAAAH,EAAA,CAAAc,MAAA,oBAAY;IAGxBd,EAHwB,CAAAa,YAAA,EAAO,EAClB,EACN,EACF;;;;;IA7EGb,EAAA,CAAA6B,SAAA,GAA4C;IAA5C7B,EAAA,CAAAE,UAAA,YAAAO,MAAA,CAAAqB,eAAA,CAAAC,QAAA,CAAAZ,MAAA,CAAAI,EAAA,EAA4C;IAK9CvB,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAgC,kBAAA,MAAAb,MAAA,CAAAc,SAAA,MACF;IAIEjC,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAgC,kBAAA,MAAAb,MAAA,CAAAe,IAAA,MACF;IAIElC,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAgC,kBAAA,MAAAb,MAAA,CAAAgB,cAAA,MACF;IAIEnC,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAgC,kBAAA,MAAAb,MAAA,CAAAiB,UAAA,MACF;IAIEpC,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAgC,kBAAA,MAAAb,MAAA,CAAAkB,IAAA,MACF;IAIErC,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAgC,kBAAA,MAAAb,MAAA,CAAAmB,KAAA,MACF;IAIEtC,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAgC,kBAAA,MAAAb,MAAA,CAAAoB,YAAA,MACF;IAIEvC,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAgC,kBAAA,MAAAb,MAAA,CAAAqB,aAAA,MACF;IAIExC,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAgC,kBAAA,MAAAb,MAAA,CAAAsB,MAAA,MACF;IAoBEzC,EAAA,CAAA6B,SAAA,IACF;IADE7B,EAAA,CAAAgC,kBAAA,MAAAb,MAAA,CAAAuB,gBAAA,MACF;IAIE1C,EAAA,CAAA6B,SAAA,GAA2D;IAAC7B,EAA5D,CAAAE,UAAA,eAAAF,EAAA,CAAA2C,eAAA,KAAAC,GAAA,EAA2D,gBAAA5C,EAAA,CAAA6C,eAAA,KAAAC,GAAA,EAAA3B,MAAA,CAAAI,EAAA,EAAmC;;;;;;IAUtGvB,EADF,CAAAG,cAAA,cAA2E,iBAC2B;IAAlCH,EAAA,CAAAI,UAAA,mBAAA2C,8DAAA;MAAA/C,EAAA,CAAAO,aAAA,CAAAyC,GAAA;MAAA,MAAAvC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAwC,qBAAA,EAAuB;IAAA,EAAC;IACjGjD,EAAA,CAAAc,MAAA,gCACF;IACFd,EADE,CAAAa,YAAA,EAAS,EACL;;;;;;IAhJIb,EANZ,CAAAG,cAAA,cAA6E,gBACY,YAC9E,aAC2D,aACxB,cACqC,gBAEtC;IAA/BH,EAAA,CAAAI,UAAA,oBAAA8C,uDAAA5C,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAA4C,GAAA;MAAA,MAAA1C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAUF,MAAA,CAAA2C,SAAA,CAAA9C,MAAA,CAAiB;IAAA,EAAC;IAElCN,EAHI,CAAAa,YAAA,EACiC,EAC7B,EACH;IACLb,EAAA,CAAAG,cAAA,aAAwB;IACtBH,EAAA,CAAAc,MAAA,mBACA;IAAAd,EAAA,CAAAC,SAAA,YAA0D;IAC5DD,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAG,cAAA,cAAwB;IACtBH,EAAA,CAAAc,MAAA,cACA;IAAAd,EAAA,CAAAC,SAAA,aAA0D;IAC5DD,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAG,cAAA,cAAwB;IACtBH,EAAA,CAAAc,MAAA,yBACA;IAAAd,EAAA,CAAAC,SAAA,aAA0D;IAC5DD,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAG,cAAA,cAAwB;IACtBH,EAAA,CAAAc,MAAA,0BACA;IAAAd,EAAA,CAAAC,SAAA,aAA0D;IAC5DD,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAG,cAAA,cAAwB;IACtBH,EAAA,CAAAc,MAAA,cACA;IAAAd,EAAA,CAAAC,SAAA,aAA0D;IAC5DD,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAG,cAAA,cAAwB;IACtBH,EAAA,CAAAc,MAAA,eACA;IAAAd,EAAA,CAAAC,SAAA,aAA0D;IAC5DD,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAG,cAAA,cAAwB;IACtBH,EAAA,CAAAc,MAAA,uBACA;IAAAd,EAAA,CAAAC,SAAA,aAA0D;IAC5DD,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAG,cAAA,cAAwB;IACtBH,EAAA,CAAAc,MAAA,wBACA;IAAAd,EAAA,CAAAC,SAAA,aAA0D;IAC5DD,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAG,cAAA,cAAwB;IACtBH,EAAA,CAAAc,MAAA,gBACA;IAAAd,EAAA,CAAAC,SAAA,aAA0D;IAC5DD,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAG,cAAA,cAAwB;IACtBH,EAAA,CAAAc,MAAA,iBACA;IAAAd,EAAA,CAAAC,SAAA,aAA0D;IAC5DD,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAG,cAAA,cAAwB;IACtBH,EAAA,CAAAc,MAAA,iCACA;IAAAd,EAAA,CAAAC,SAAA,aAA0D;IAC5DD,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAG,cAAA,cAAwB;IACtBH,EAAA,CAAAc,MAAA,2BACA;IAAAd,EAAA,CAAAC,SAAA,aAA0D;IAC5DD,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAG,cAAA,cAAwB;IAAAH,EAAA,CAAAc,MAAA,eAAO;IAEnCd,EAFmC,CAAAa,YAAA,EAAK,EACjC,EACC;IACRb,EAAA,CAAAG,cAAA,aAAO;IACLH,EAAA,CAAAqD,UAAA,KAAAC,oCAAA,mBAA6B;IAmFjCtD,EADE,CAAAa,YAAA,EAAQ,EACF;IACRb,EAAA,CAAAqD,UAAA,KAAAE,qCAAA,kBAA2E;IAK7EvD,EAAA,CAAAa,YAAA,EAAM;;;;IAjJsDb,EAAA,CAAA6B,SAAA,GAA2B;IAA3B7B,EAAA,CAAAE,UAAA,YAAAO,MAAA,CAAA+C,aAAA,GAA2B;IAwD7DxD,EAAA,CAAA6B,SAAA,IAAO;IAAP7B,EAAA,CAAAE,UAAA,YAAAO,MAAA,CAAAgD,IAAA,CAAO;IAoFUzD,EAAA,CAAA6B,SAAA,EAAgC;IAAhC7B,EAAA,CAAAE,UAAA,UAAAO,MAAA,CAAAiD,IAAA,kBAAAjD,MAAA,CAAAiD,IAAA,CAAAC,IAAA,kBAAgC;;;;;IAoBvE3D,EAAA,CAAAG,cAAA,UAAiD;IAC/CH,EAAA,CAAAC,SAAA,cAA4G;IAE1GD,EADF,CAAAG,cAAA,cAAkB,YACM;IAAAH,EAAA,CAAAc,MAAA,YAAK;IAE/Bd,EAF+B,CAAAa,YAAA,EAAI,EAC3B,EACF;;;;IAJCb,EAAA,CAAA6B,SAAA,EAA6B;IAA7B7B,EAAA,CAAAE,UAAA,QAAAO,MAAA,CAAAmD,qBAAA,EAAA5D,EAAA,CAAA6D,aAAA,CAA6B;;;;;IAMlC7D,EAAA,CAAAG,cAAA,cAAiC;IAAAH,EAAA,CAAAc,MAAA,mBAAY;IAAAd,EAAA,CAAAa,YAAA,EAAM;;;ADrN7D,OAAM,MAAOiD,cAAe,SAAQlE,iBAAiB;EAUvCmE,EAAA;EACAC,WAAA;EACFC,cAAA;EAXVP,IAAI;EACJQ,aAAa,GAAG,KAAK;EACrBC,cAAc,GAAQ,EAAE;EACxBC,UAAU,GAAW,EAAE;EACfC,aAAa;EACrBC,uBAAuB,GAAG,KAAK;EAC/BxC,eAAe,GAAa,EAAE;EAE9ByC,YACYR,EAAqB,EACrBC,WAAyB,EAC3BC,cAA8B;IAEtC,KAAK,CAACF,EAAE,CAAC;IAJC,KAAAA,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACb,KAAAC,cAAc,GAAdA,cAAc;IAGtB,IAAI,CAACO,UAAU,CAACR,WAAW,CAAC;IAC5B,IAAI,CAACS,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,QAAQ,GAAG,MAAM;EACxB;EAEAC,QAAQA,CAAA;IACN,KAAK,CAACA,QAAQ,EAAE;IAEhB;IACA,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACpD,IAAI,CAACpB,IAAI,GAAGkB,QAAQ,GAAGG,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC,GAAG,IAAI;IAElD,IAAI,CAACX,cAAc,CAACgB,WAAW,CAACC,SAAS,CAAEC,MAAW,IAAI;MACxD,IAAIA,MAAM,CAAC,WAAW,CAAC,EAAE;QACvB,IAAI,CAACC,IAAI,CAACC,OAAO,GAAG;UAAEpD,SAAS,EAAEkD,MAAM,CAAC,WAAW;QAAC,CAAE;MACxD;IACF,CAAC,CAAC;EACJ;EAEAG,kBAAkBA,CAACC,KAAa;IAC9BC,YAAY,CAAC,IAAI,CAACnB,aAAa,CAAC;IAEhC,IAAI,CAACA,aAAa,GAAGoB,UAAU,CAAC,MAAK;MACnC,IAAI,CAACL,IAAI,CAACC,OAAO,GAAG;QAAC,GAAG,IAAI,CAACD,IAAI,CAACC,OAAO;QAAEK,QAAQ,EAAEH,KAAK,CAACI,IAAI;MAAE,CAAC;MAClE,IAAI,CAACC,WAAW,CAAC,IAAI,CAACR,IAAI,CAAC;IAC7B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAS,oBAAoBA,CAAA;IAClB,IAAI,CAACvB,uBAAuB,GAAG,CAAC,IAAI,CAACA,uBAAuB;EAC9D;EAEAlB,SAASA,CAAC0C,KAAY;IACtB,MAAMC,QAAQ,GAAGD,KAAK,CAACE,MAA0B;IACjD,MAAMC,OAAO,GAAGF,QAAQ,CAACE,OAAO;IAEhC,IAAIA,OAAO,EAAE;MACX;MACA,IAAI,CAACnE,eAAe,GAAG,IAAI,CAAC2B,IAAI,CAACyC,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAAC5E,EAAE,CAAC;IAC5D,CAAC,MAAM;MACL,IAAI,CAACO,eAAe,GAAG,EAAE;IAC3B;EACF;EAEA;EACE0B,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,IAAI,CAAC2C,MAAM,GAAG,CAAC,IAAI,IAAI,CAACtE,eAAe,CAACsE,MAAM,KAAK,IAAI,CAAC3C,IAAI,CAAC2C,MAAM;EACjF;EAEA9E,mBAAmBA,CAAC+E,MAAc,EAAE/F,MAAW;IAC7C,MAAMyF,QAAQ,GAAGzF,MAAM,CAAC0F,MAA0B;IAClD,MAAMC,OAAO,GAAGF,QAAQ,CAACE,OAAO;IAChC,IAAIA,OAAO,EAAE;MACX,IAAI,CAACnE,eAAe,CAACwE,IAAI,CAACD,MAAM,CAAC;IACnC,CAAC,MAAM;MACL,IAAI,CAACvE,eAAe,GAAG,IAAI,CAACA,eAAe,CAACyE,MAAM,CAAChF,EAAE,IAAIA,EAAE,KAAK8E,MAAM,CAAC;IACzE;EACF;EAEAzF,gBAAgBA,CAACyE,OAAY;IAC3B,IAAI,CAACQ,oBAAoB,EAAE;IAC3B,IAAI,CAACT,IAAI,CAACC,OAAO,GAAG;MAAC,GAAG,IAAI,CAACD,IAAI,CAACC,OAAO;MAAE,GAAGA;IAAO,CAAC;IAEtD,IAAI,CAACO,WAAW,CAAC,IAAI,CAACR,IAAI,CAAC;EAC7B;EAEMQ,WAAWA,CAACY,QAAa;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC7BD,KAAI,CAACrB,IAAI,CAACuB,UAAU,GAAGH,QAAQ,CAACG,UAAU,IAAIH,QAAQ;MAEtDC,KAAI,CAACG,OAAO,GAAG,IAAI;MACnB,MAAMH,KAAI,CAACI,QAAQ,CAACC,MAAM,CAACL,KAAI,CAACrB,IAAI,CAAC,CAACF,SAAS,CAC5C6B,SAAc,IAAI;QACjBC,OAAO,CAACC,GAAG,CAACF,SAAS,CAACG,IAAI,CAAC;QAC3BT,KAAI,CAAChD,IAAI,GAAG0D,KAAK,CAACC,OAAO,CAACL,SAAS,CAACG,IAAI,CAAC,GAAEH,SAAS,CAACG,IAAI,GAAG,EAAE;QAC9DT,KAAI,CAAChD,IAAI,GAAG,CAAC,GAAGgD,KAAI,CAAChD,IAAI,CAAC;QAE1BgD,KAAI,CAACrB,IAAI,CAACiC,aAAa,GAAGN,SAAS,CAACO,KAAK;QACzCb,KAAI,CAACrB,IAAI,CAACkC,KAAK,GAAGC,IAAI,CAACC,IAAI,CAACT,SAAS,CAACO,KAAK,GAAGb,KAAI,CAACrB,IAAI,CAACqC,IAAI,CAAC;QAE7D;QACAhB,KAAI,CAACiB,yBAAyB,EAAE;QAEhCjB,KAAI,CAAC1C,EAAE,CAAC4D,YAAY,EAAE;QACtBlB,KAAI,CAACG,OAAO,GAAG,KAAK;QAEpBH,KAAI,CAACmB,eAAe,EAAE;QACtB9H,aAAa,CAAC+H,gBAAgB,EAAE;MAClC,CAAC,EACAC,KAAU,IAAI;QACbd,OAAO,CAACC,GAAG,CAACa,KAAK,CAAC;QAClBrB,KAAI,CAAC1C,EAAE,CAAC4D,YAAY,EAAE;QACtBlB,KAAI,CAACG,OAAO,GAAG,KAAK;QACpB7G,IAAI,CAACgI,IAAI,CAAC,8CAA8C,EAAE,EAAE,EAAE,OAAO,CAAC;MACxE,CAAC,CACF;IAAA;EACH;EAEAL,yBAAyBA,CAAA;IACvB;IACA,IAAI,CAACxD,aAAa,GAAG,IAAI,CAACT,IAAI,CAAC2C,MAAM,KAAK,CAAC;EAC7C;EAEAxC,qBAAqB;EACrBnC,iBAAiBA,CAACuG,UAAkB;IAClC,IAAI,CAACpE,qBAAqB,GAAGoE,UAAU;IACvC,MAAMC,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC;IACjE,IAAIF,YAAY,EAAE;MAChB,MAAMG,KAAK,GAAG,IAAIvI,KAAK,CAACoI,YAAY,CAAC;MACrCG,KAAK,CAACC,IAAI,EAAE;IACd;EACF;EAEApH,WAAWA,CAAA;IACT+F,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;EACrC;EAEAhE,qBAAqBA,CAAA;IAAA,IAAAqF,MAAA;IAEnB,IAAI,IAAI,CAACxG,eAAe,CAACsE,MAAM,KAAK,CAAC,EAAE;MACrCrG,IAAI,CAACgI,IAAI,CAAC,kCAAkC,EAAE,EAAE,EAAE,SAAS,CAAC;MAC5D;IACF;IACAf,OAAO,CAACC,GAAG,CAAC,IAAI,CAACnF,eAAe,CAAC;IACjC,IAAI,CAACkC,WAAW,CAACf,qBAAqB,CAAC,IAAI,CAACnB,eAAe,CAAC,CAACoD,SAAS;MAAA,IAAAqD,IAAA,GAAA7B,iBAAA,CACpE,WAAO8B,QAAY,EAAK;QACtBF,MAAI,CAACvE,EAAE,CAAC0E,aAAa,EAAE;QACvB,MAAM1I,IAAI,CAACgI,IAAI,CAAC,6BAA6B,EAAE,EAAE,EAAE,SAAS,CAAC;QAC7DO,MAAI,CAACxG,eAAe,GAAG,EAAE;QACzBwG,MAAI,CAAC1C,WAAW,CAAC0C,MAAI,CAAClD,IAAI,CAAC;MAC7B,CAAC;MAAA,iBAAAsD,EAAA;QAAA,OAAAH,IAAA,CAAAI,KAAA,OAAAC,SAAA;MAAA;IAAA,KACAd,KAAU,IAAK;MACd/H,IAAI,CAACgI,IAAI,CAAC,+BAA+B,EAAE,EAAE,EAAE,OAAO,CAAC;MACvDf,OAAO,CAACc,KAAK,CAACA,KAAK,CAAC;IACtB,CAAC,CACF;EACH;;qCAtJWhE,cAAc,EAAA9D,EAAA,CAAA6I,iBAAA,CAAA7I,EAAA,CAAA8I,iBAAA,GAAA9I,EAAA,CAAA6I,iBAAA,CAAAE,EAAA,CAAAC,YAAA,GAAAhJ,EAAA,CAAA6I,iBAAA,CAAAI,EAAA,CAAAC,cAAA;EAAA;;UAAdpF,cAAc;IAAAqF,SAAA;IAAAC,QAAA,GAAApJ,EAAA,CAAAqJ,0BAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCd3B3J,EAAA,CAAAG,cAAA,aAAuB;QAErBH,EADA,CAAAqD,UAAA,IAAAwG,0CAAA,8BAAkD,IAAAC,8CAAA,kCAC8D;QAElH9J,EAAA,CAAAa,YAAA,EAAM;QAOMb,EANZ,CAAAG,cAAA,aAAgC,aACG,aACc,aAClB,aACiD,aAC7C,aAC6B;QAAAH,EAAA,CAAAc,MAAA,aAAK;QAC3Dd,EAD2D,CAAAa,YAAA,EAAK,EAC1D;QAEJb,EADF,CAAAG,cAAA,cAAyB,gBACuE;QAC5FH,EAAA,CAAAC,SAAA,wBAEe;QACfD,EAAA,CAAAG,cAAA,iBAGmC;QAF0CH,EAAA,CAAA+J,gBAAA,2BAAAC,wDAAA1J,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA0J,GAAA;UAAAjK,EAAA,CAAAkK,kBAAA,CAAAN,GAAA,CAAAxF,UAAA,EAAA9D,MAAA,MAAAsJ,GAAA,CAAAxF,UAAA,GAAA9D,MAAA;UAAA,OAAAN,EAAA,CAAAW,WAAA,CAAAL,MAAA;QAAA,EAAwB;QACnGN,EAAA,CAAAI,UAAA,2BAAA4J,wDAAA1J,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA0J,GAAA;UAAA,OAAAjK,EAAA,CAAAW,WAAA,CAAiBiJ,GAAA,CAAAtE,kBAAA,CAAAhF,MAAA,CAA0B;QAAA,EAAC;QAGlDN,EALI,CAAAa,YAAA,EAGmC,EAC9B,EACH;QAGFb,EAFJ,CAAAG,cAAA,cAAyB,eACa,aAC6D;QAAjCH,EAAA,CAAAI,UAAA,mBAAA+J,4CAAA;UAAAnK,EAAA,CAAAO,aAAA,CAAA0J,GAAA;UAAA,OAAAjK,EAAA,CAAAW,WAAA,CAASiJ,GAAA,CAAA/D,oBAAA,EAAsB;QAAA,EAAC;QAC5F7F,EAAA,CAAAC,SAAA,aAAkC;QAACD,EAAA,CAAAc,MAAA,gBACrC;QAAAd,EAAA,CAAAa,YAAA,EAAI;QAGJb,EAAA,CAAAqD,UAAA,KAAA+G,8BAAA,kBACiE;QAGnEpK,EAAA,CAAAa,YAAA,EAAM;QACNb,EAAA,CAAAqD,UAAA,KAAAgH,4BAAA,gBAAoF;QAO5FrK,EAHM,CAAAa,YAAA,EAAM,EACF,EACF,EACF;QAWNb,EATA,CAAAqD,UAAA,KAAAiH,oDAAA,wCAA0E,KAAAC,8BAAA,kBAEtB,KAAAC,8BAAA,mBAOyB;QAyJjFxK,EADE,CAAAa,YAAA,EAAM,EACF;QAENb,EAAA,CAAAC,SAAA,qBAA+B;QAOvBD,EAJR,CAAAG,cAAA,eAAqH,eAC1D,eAC5B,eACC,cACwB;QAAAH,EAAA,CAAAc,MAAA,YAAI;QAAAd,EAAA,CAAAa,YAAA,EAAK;QACzDb,EAAA,CAAAC,SAAA,kBAA4F;QAC9FD,EAAA,CAAAa,YAAA,EAAM;QACNb,EAAA,CAAAG,cAAA,eAAoC;QAOlCH,EANA,CAAAqD,UAAA,KAAAoH,8BAAA,kBAAiD,KAAAC,sCAAA,gCAAA1K,EAAA,CAAA2K,sBAAA,CAM3B;QAM9B3K,EAHM,CAAAa,YAAA,EAAM,EACF,EACF,EACF;;;;QAvOeb,EAAA,CAAA6B,SAAA,EAA6B;QAA7B7B,EAAA,CAAAE,UAAA,UAAA0J,GAAA,CAAAlG,IAAA,kBAAAkG,GAAA,CAAAlG,IAAA,CAAAC,IAAA,eAA6B;QACzB3D,EAAA,CAAA6B,SAAA,EAAgC;QAAhC7B,EAAA,CAAAE,UAAA,UAAA0J,GAAA,CAAAlG,IAAA,kBAAAkG,GAAA,CAAAlG,IAAA,CAAAC,IAAA,kBAAgC;QAiBkC3D,EAAA,CAAA6B,SAAA,IAAwB;QAAxB7B,EAAA,CAAA4K,gBAAA,YAAAhB,GAAA,CAAAxF,UAAA,CAAwB;QAY/FpE,EAAA,CAAA6B,SAAA,GAA6B;QAA7B7B,EAAA,CAAAE,UAAA,SAAA0J,GAAA,CAAAtF,uBAAA,CAA6B;QAKatE,EAAA,CAAA6B,SAAA,EAAgC;QAAhC7B,EAAA,CAAAE,UAAA,UAAA0J,GAAA,CAAAlG,IAAA,kBAAAkG,GAAA,CAAAlG,IAAA,CAAAC,IAAA,kBAAgC;QAS9D3D,EAAA,CAAA6B,SAAA,EAAmB;QAAnB7B,EAAA,CAAAE,UAAA,SAAA0J,GAAA,CAAA1F,aAAA,CAAmB;QAEzClE,EAAA,CAAA6B,SAAA,EAAmB;QAAnB7B,EAAA,CAAAE,UAAA,SAAA0J,GAAA,CAAA1F,aAAA,CAAmB;QAOWlE,EAAA,CAAA6B,SAAA,EAAuC;QAAvC7B,EAAA,CAAAE,UAAA,UAAA0J,GAAA,CAAA1F,aAAA,IAAA0F,GAAA,CAAAnG,IAAA,CAAA2C,MAAA,KAAuC;QAsKjEpG,EAAA,CAAA6B,SAAA,IAA6B;QAAA7B,EAA7B,CAAAE,UAAA,SAAA0J,GAAA,CAAAhG,qBAAA,CAA6B,aAAAiH,UAAA,CAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}